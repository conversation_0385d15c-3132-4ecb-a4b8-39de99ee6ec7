import { Directive, ElementRef, HostListener, Renderer2 } from '@angular/core';
import { NgControl } from '@angular/forms';

/**
 * Directive to fix iOS date input behavior where it automatically selects the current date
 * when a user clicks on the calendar and then moves away without selecting a date.
 */
@Directive({
  selector: '[iosDateInput]'
})
export class IosDateInputDirective {
  private initialValue: string = '';
  private isIOS: boolean = false;
  private userSelected: boolean = false;

  constructor(
    private el: ElementRef,
    private renderer: Renderer2,
    private control: NgControl
  ) {
    // Check if the device is iOS
    this.isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);

    // Initialize the has-value class based on initial value
    this.updateHasValueClass();
  }

  private updateHasValueClass() {
    if (this.control && this.control.value) {
      this.renderer.addClass(this.el.nativeElement, 'has-value');
    } else {
      this.renderer.removeClass(this.el.nativeElement, 'has-value');
    }
  }

  @HostListener('focus')
  onFocus() {
    if (!this.isIOS) return;

    // Store the initial value when the input gets focus
    this.initialValue = this.control.value || '';
    this.userSelected = false;
  }

  @HostListener('change')
  onChange() {
    if (!this.isIOS) return;

    // Mark as user-selected when the value changes due to user interaction
    this.userSelected = true;

    // Update the has-value class
    this.updateHasValueClass();
  }

  @HostListener('input')
  onInput() {
    if (!this.isIOS) return;

    // Update the has-value class on input as well
    this.updateHasValueClass();
  }

  @HostListener('blur')
  onBlur() {
    if (!this.isIOS) return;

    // If the value changed but wasn't explicitly selected by the user,
    // reset it to the initial value (which is likely empty)
    if (!this.userSelected && this.control.value !== this.initialValue) {
      // Use setTimeout to ensure this happens after all other blur handlers
      setTimeout(() => {
        this.control.control?.setValue(this.initialValue);
        this.control.control?.markAsPristine();
        this.control.control?.markAsUntouched();
        this.updateHasValueClass();
      });
    }
  }
}
