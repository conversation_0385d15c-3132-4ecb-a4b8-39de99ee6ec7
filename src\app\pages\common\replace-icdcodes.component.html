<div class="modal fade" id="saveICDsAllEcounters" tabindex="-1" aria-labelledby="exampleModalCenterTitle"
  maria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
      <div class="modal-header py-2">
        <h5 class="modal-title" id="exampleModalCenterTitle">Encounter(s) Selection</h5>
        <div class="custom-control custom-checkbox font text-dark display-inline-block">
          <input type="checkbox" class="custom-control-input" id="chkAll" value="chkAll" (change)="selectAll($event)">
          <label class="custom-control-label text-white" for="chkAll">Select All</label>
        </div>
      </div>
      <div class="modal-body">
        <!-- search start -->
        <div class="row">
          <div class="col-12">

            <div class="card px-3 py-2" id="nav-tabContentKareo">

              <div class="tab-pane">
                <div class="row scrolling">
                  <div class="col-12 my-2" *ngFor="let item of listOfAllEncounters">
                    <ng-container *ngIf="encounterObj.encounteR_ID != item.encounteR_ID && item.status =='0'">
                      <div class="custom-control custom-checkbox font text-dark">
                        <input *ngIf="item.encounteR_ID" type="checkbox" class="custom-control-input"
                          [checked]="item.checked" id="{{item.encounteR_ID}}" value="{{item.encounteR_ID}}"
                          (change)="chkChangeEvent($event)">
                        <label class="custom-control-label" for="{{item.encounteR_ID}}">
                          <span class="pl-4"><i>Encounter created at:</i>
                            {{item.encounterseendate}}--<span class="small">
                              <i>By <span class="text-color-green">{{item.posT_TO_BILLED_BY}}</span></i></span></span>
                        </label>
                      </div>
                    </ng-container>
                  </div>
                  <div class="text-danger ml-3 font" *ngIf="isValidationMessage">
                    Please select atleast one encounter.
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer py-2">
        <button class="btn btn-outline-info float-right"
          (click)="confrimsubmitICDsAllEncouners(encounterObj,PatientObject,listOfAllEncounters)">Submit</button>
      </div>

    </div>
  </div>
</div>