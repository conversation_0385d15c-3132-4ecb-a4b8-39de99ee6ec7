#sp1 {
    background-color: #ffc9c9;
    border-color: #ffc9c9;
    color: #ac1818;
    padding: 10px;
    width: 100%;
    margin-bottom: 8px;
    text-align: center;
    border-radius: 4px;
}

#sp2 {
    background-color: #ffc9c9;
    border-color: #ffc9c9;
    color: #ac1818;
    padding: 10px;
    width: 100%;
    margin-bottom: 8px;
    text-align: center;
    border-radius: 4px;
}

#sp3 {
    background-color: #ffefa4;
    border-color: #ffefa4;
    color: #574802;
    padding: 10px;
    width: 100%;
    margin-bottom: 8px;
    text-align: center;
    border-radius: 4px;
}

#sp4 {
    background-color: #ffc9c9;
    border-color: #ffc9c9;
    color: #ac1818;
    padding: 10px;
    width: 100%;
    margin-bottom: 8px;
    text-align: center;
    border-radius: 4px;
}

#sp5 {
    background-color: green;
    border-color: #ffc9c9;
    color: #ffffff;
    padding: 10px;
    width: 100%;
    margin-bottom: 8px;
    text-align: center;
    border-radius: 4px;
}


@media (max-width: 767.98px) {
    .modal-dialog.modal-lg {
        max-width: 95%;
        margin: 0.5rem auto;
    }

    .modal-content {
        border-radius: 8px;
    }

    .modal-header {
        padding: 0.5rem 1rem;
        background-color: #336699;
        color: white;
    }

    .modal-title {
        font-size: 1rem;
        font-weight: 600;
    }

    .modal-body {
        padding: 0.75rem;
        max-height: 70vh;
        overflow-y: auto;
    }

    #sp1, #sp2, #sp3, #sp4, #sp5 {
        font-size: 14px;
        padding: 8px;
        margin-bottom: 6px;
        border-radius: 4px;
        text-align: center;
        word-wrap: break-word;
    }

    .table-responsive {
        border: none;
    }

    .table-bordered {
        border: none;
    }

    .table th {
        padding: 0;
        border: none;
    }
}