<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new p-2" style="background: #0169ab;">
                    <div class="align-items-center row mx-auto">
                        <div class="col-12 px-0">
                            <div class="row my-1 mx-auto" [formGroup]="searchForm">
                                <div class="col-12 col-md-4 px-1 py-1">
                                    <select id="ddlFacility" class="form-control w-100"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}"
                                        formControlName="ddlFacility"
                                        (change)="getPhysiciansByFacility($any($event.target).value)">
                                        <option value="">---Select Facility---</option>
                                        <option *ngFor="let s of listOfFacilities">
                                            {{s.facilityName}}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-12 col-md-3 px-1 py-1">
                                    <select class="form-control w-100" id="ddlPhysician"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlPhysician'].errors}"
                                        formControlName="ddlPhysician">
                                        <option value="">---Select Physician---</option>
                                        <option value="All Physicians">All Physicians</option>
                                        <option [value]="s.physicianEmaiId" *ngFor="let s of listOfPhysicians">
                                            {{s.physicianName}}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-12 col-md-3 px-1 py-1">
                                    <select class="form-control w-100"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlFromYear'].errors}"
                                        formControlName="ddlFromYear">
                                        <option value=''>--Select Year--</option>
                                        <option [value]="year" *ngFor="let year of listOfYears">
                                            <span>{{year}}</span>
                                        </option>
                                    </select>
                                </div>
                                <div class="col-12 col-md-2 px-1 py-1">
                                    <button class="btn btn-outline-info px-2 btn-block" type="submit"
                                        (click)="getReportSummary()">Search</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-1">

                    <div class="row mx-auto">
                        <!-- column 1 Chart start-->
                        <div class="col-12 p-0">
                            <div class="card shadow mb-3">
                                <div class="card-header pl-0 position-relative bg-blue py-3">
                                    <ng-container *ngIf="this.lstOfReportData.length>0">
                                        <img alt=' ' [src]="img1" class="float-right" (keyup)="exportAsXLSX()"
                                            (click)="exportAsXLSX()"
                                            style="width: 24px;position: absolute;right: 10px;top: 6px;cursor: pointer;"
                                            title="Export Excel">
                                        <i class="fa-eye far text-info" data-toggle="modal"
                                            data-target="#viewReportData"
                                            style="width: 24px;position: absolute;right: 50px;top: 7px;cursor: pointer;font-size: 24px;color: white;"></i>
                                    </ng-container>
                                </div>
                                <div class="card-body">
                                    <div id="conatinerCPTSummary"
                                        style="min-width: 1200px; height: 800px; max-width: 1200px; margin: 0 auto">
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>

    </div>

</div>
<!-- Begin Page Content Ends -->


<!-- model popups starts -->
<div class="modal fade" id="viewReportData" tabindex="-1" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title" id="exampleModalCenterTitle">View Data</h5>
            </div>
            <div class="modal-body p-2 modal-height">
                <div class="row mx-0">
                    <div class="col">
                        <div class="table-responsive">
                            <table class="table table-bordered" id="dataTable width-100per">
                                <thead>
                                    <tr>
                                        <th class="text-nowrap bg-gradient-primary text-white">Physician</th>
                                        <th class="text-nowrap bg-gradient-primary text-white">CPT</th>
                                        <th class="text-nowrap bg-gradient-primary text-white">Year</th>
                                        <th class="text-nowrap bg-gradient-primary text-white">JAN</th>
                                        <th class="text-nowrap bg-gradient-primary text-white">FEB</th>
                                        <th class="text-nowrap bg-gradient-primary text-white">MAR</th>
                                        <th class="text-nowrap bg-gradient-primary text-white">APR</th>
                                        <th class="text-nowrap bg-gradient-primary text-white">MAY</th>
                                        <th class="text-nowrap bg-gradient-primary text-white">JUN</th>
                                        <th class="text-nowrap bg-gradient-primary text-white">JUL</th>
                                        <th class="text-nowrap bg-gradient-primary text-white">AUG</th>
                                        <th class="text-nowrap bg-gradient-primary text-white">SEP</th>
                                        <th class="text-nowrap bg-gradient-primary text-white">OCT</th>
                                        <th class="text-nowrap bg-gradient-primary text-white">NOV</th>
                                        <th class="text-nowrap bg-gradient-primary text-white">DEC</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let item of lstOfReportData">
                                        <td class="pname">{{item.physicianname}}</td>
                                        <td class="faci">{{item.cpt}}</td>
                                        <td class="faci">{{item.year}}</td>
                                        <td class="faci">{{item.jan}}</td>
                                        <td class="faci">{{item.feb}}</td>
                                        <td class="faci">{{item.march}}</td>
                                        <td class="faci">{{item.april}}</td>
                                        <td class="faci">{{item.may}}</td>
                                        <td class="faci">{{item.june}}</td>
                                        <td class="faci">{{item.july}}</td>
                                        <td class="faci">{{item.aug}}</td>
                                        <td class="faci">{{item.sep}}</td>
                                        <td class="faci">{{item.oct}}</td>
                                        <td class="faci">{{item.nov}}</td>
                                        <td class="faci">{{item.dec}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-2">

                <button class="btn btn-outline-info float-right" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="CPT" tabindex="-1" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog custom-modal modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title" id="exampleModalCenterTitle">Add CPT</h5>
            </div>
            <div class="modal-body p-2 modal-height">
                <div class="row mx-0">
                    <div class="col">
                        <div class="table-responsive">
                            <table class="table table-bordered" id="dataTable width-100per">
                                <thead>
                                    <tr>
                                        <th class="text-nowrap bg-gradient-primary text-white">Physician</th>
                                        <th class="text-nowrap bg-gradient-primary text-white">Facility</th>
                                        <th class="text-nowrap bg-gradient-primary text-white">Group Name</th>
                                        <th class="text-nowrap bg-gradient-primary text-white">Encounter Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="pname">Amith Bawa</td>
                                        <td class="faci">SRMC</td>
                                        <td class="faci1">Hospitalist-SMRMC</td>
                                        <td>06/16/2019</td>
                                    </tr>
                                    <tr>
                                        <td class="pname">Amith Bawa</td>
                                        <td class="faci">SRMC</td>
                                        <td class="faci1">Hospitalist-SMRMC</td>
                                        <td>06/12/2019</td>
                                    </tr>
                                    <tr>
                                        <td class="pname">Tim Stacy</td>
                                        <td class="faci">CVMC</td>
                                        <td class="faci1">Hospitalist-CVMC</td>
                                        <td>10/27/2019</td>
                                    </tr>
                                    <tr>
                                        <td class="pname">Fernando Palacios</td>
                                        <td class="faci">CVMC</td>
                                        <td class="faci1">Hospitalist-CVMC</td>
                                        <td>05/24/2019</td>
                                    </tr>
                                    <tr>
                                        <td class="pname">Jeffrey Crudo</td>
                                        <td class="faci">CVMC</td>
                                        <td class="faci1">Hospitalist-CVMC</td>
                                        <td>05/24/2019</td>
                                    </tr>
                                    <tr>
                                        <td class="pname">Allen Ozeran</td>
                                        <td class="faci">SOH</td>
                                        <td class="faci1">Hospitalist-SOH</td>
                                        <td>08/15/2019</td>
                                    </tr>
                                    <tr>
                                        <td class="pname">Noel Nunez</td>
                                        <td class="faci">SOH</td>
                                        <td class="faci1">Hospitalist-SOH</td>
                                        <td>07/07/2019</td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-2">

                <button class="btn btn-outline-info float-right" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<!-- model popups ends -->