import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { forkJoin } from 'rxjs';
import { AppComponent } from 'src/app/app.component';
import { PhysicianModel } from 'src/app/models/physician.model';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';

declare let $: any;

@Component({
  selector: 'app-deleted-encounter',
  templateUrl: './deleted-encounter.component.html',
  styles: []
})
export class DeletedEncounterComponent implements OnInit {
  public listOfFacilities: Array<any> = [];
  public request: any = {};
  public PatientObject: any = {};
  public lisfOfEncounters: Array<any> = [];
  device = false;
  public totalCount: number;
  public envAccountNo: string = "";
  public envMrnNo: string = "";
  public envFacility: string = "";
  public lisfOfAttachments: Array<any> = [];
  public listOfUsersAndGroups: Array<any> = [];
  public lisfOfSentNotes: Array<any> = [];
  public selectedUsersAndGroups: Array<any> = [];
  public encounterObj: any = {};
  public listOfHistory: Array<any> = [];
  public listOfPhysicians = Array<PhysicianModel>();
  public lisfOfMissingEncounters: Array<any> = [];

  constructor(private readonly appComp: AppComponent, private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService
    , private readonly toastr: ToastrService) { }

  ngOnInit() {
    this.appComp.loadPageName('Historical Encounters', 'HistoricalEncountersTab');
    if (/Android|webOS|iPhone|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) && (window.innerWidth <= 575 || window.innerWidth >= 575)) {
      // some code..
      this.device = true;
    } else {
      this.device = false;
    }
  }

  getPatientDetails(request: any) {
    this.commonServ.startLoading();
    this.commonServ.getPatientDetails(request).subscribe((p: any) => {
      if (p != null) {
        this.PatientObject = p;
        this.PatientObject.admission_Date = p.admission_Date1;
        if (this.PatientObject != null) {
          this.totalCount = 1;
        }
        else {
          this.totalCount = 0;
        }
        this.commonServ.stopLoading();
        this.getEncountersByPatient(this.PatientObject);
      }
      else {
        this.commonServ.stopLoading();
        this.toastr.error("No Patient Exist with this Account Number.");
      }
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  getEncountersByPatient(pObj) {

    this.envAccountNo = pObj.account_Number;
    this.envMrnNo = pObj.mrn;
    this.envFacility = pObj.facility_Name;
    this.commonServ.startLoading();
    this.request.account_Number = this.encrDecr.set(pObj.account_Number);
    this.request.facilityName = this.encrDecr.set(pObj.facility_Name);
    this.commonServ.getDeletedEncountersDetails(this.request).subscribe((p: any) => {
      this.lisfOfEncounters = p.listofEncounters;
      this.lisfOfEncounters.forEach(y => y.listOfEncounters.forEach(x => x.encounterseendate = this.encrDecr.get(x.encounterseendate)));
      this.request = {};

      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      console.error(error.status);
      this.commonServ.stopLoading();
    });
    this.PatientObject = pObj;
  }

  getPhysicians(facilitystr) {
    if (facilitystr == 'All') {
      facilitystr = '';
    }
    this.commonServ.startLoading();
    this.commonServ.getPhysiciansByFacility(facilitystr).subscribe((p: any) => {
      this.listOfPhysicians = p;
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  getAttachments(pObj) {
    this.commonServ.startLoading();
    this.request.AccountNumber = this.encrDecr.set(pObj.account_Number);
    this.request.FacilityName = this.encrDecr.set(pObj.facility_Name);
    this.commonServ.getAttachments(this.request).subscribe((p: any) => {
      this.lisfOfAttachments = p;
      this.request = {};
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  openNotes(item) {
    this.PatientObject = item;
    this.selectedUsersAndGroups = [];
    this.commonServ.startLoading();
    this.request.ACCOUNT_NUMBER = this.encrDecr.set(item.account_Number);
    this.request.FACILITYNAME = this.encrDecr.set(item.facility_Name);
    forkJoin(
      this.commonServ.getUserGroups(item.facility_Name),
      this.commonServ.getNotes(this.request)
    ).subscribe((p: any) => {
      this.listOfUsersAndGroups = p[0];
      this.lisfOfSentNotes = p[1];
      $('#mdlNotes').modal('show');
      this.request = {};
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.toastr.error('Something went wrong!!!');
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  getMissingEncounters(pObj) {
    this.commonServ.startLoading();
    this.getPhysicians(pObj.facility_Name);
    this.request.AccountNumber = this.encrDecr.set(pObj.account_Number);
    this.request.FacilityName = this.encrDecr.set(pObj.facility_Name);
    this.commonServ.getMissingEncounters(this.request).subscribe((p: any) => {
      this.lisfOfMissingEncounters = p;
      this.request = {};
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      console.error(error.status);
      this.commonServ.stopLoading();
    });

  }

  updateAttCount(patient) {
    console.log(patient);
  }


}
