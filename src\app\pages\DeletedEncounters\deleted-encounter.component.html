<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <app-search-patients (eventGetPatientDetails)="getPatientDetails($event)"></app-search-patients>
    <!-- Card Body -->
    <div class="card-body px-2 py-1">
        <div class="row mx-0">
            <div class="col-12 px-0">
                <!-- table start -->
                <div class="tablechange table-responsive">
                    <table class="table table-borderless">
                        <thead>
                            <tr *ngIf="PatientObject.account_Number!='' && totalCount>0">

                                <th class="pb-0" [ngClass]="{'width-13per': !device}">Patient Name</th>
                                <th *ngIf="!device" class="width-10per text-center pb-0">Account No.</th>
                                <th *ngIf="!device" class="width-10per text-center pb-0">MRN</th>
                                <th *ngIf="!device" class="width-8per text-center pb-0">DOB</th>
                                <th *ngIf="!device" class="width-4per text-center pb-0">Sex</th>
                                <th *ngIf="!device" class="width-8per text-center pb-0">SSN</th>
                                <th class="text-center pb-0" [ngClass]="{'width-6per': !device}">Facility
                                </th>
                                <th *ngIf="!device" class="width-6per text-center pb-0">Room</th>
                                <th *ngIf="!device" class="width-11per small-scroll mr-1 pb-0">Admission Date
                                </th>
                                <th *ngIf="!device" class="width-11per small-scroll ml-1 pb-0">Discharge Date
                                </th>
                                <th class="pb-0" [ngClass]="{'width-10per small-scroll-2 small-scroll ml-2': !device}">
                                    Primary Coverage</th>

                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngIf="PatientObject.account_Number!='' && totalCount>0">
                                <td [ngClass]="{'width-13per text-nowrap position-relative': !device}">
                                    <span
                                        class="overflow-hidden pl-3 text-nowrap overflow-text cursor-pointer font-weight-bold">{{PatientObject.patient_Name}}</span>
                                    <span *ngIf="PatientObject.discharge_Date" class="ml-1"><i class="fa fa-bed"
                                            title="Discharged"></i></span>
                                    <span *ngIf="PatientObject.billervisitstatus=='0'" class="ml-1"><i
                                            class="fa fa-user-plus" title="New"></i></span>
                                </td>
                                <td *ngIf="!device" class="width-10per text-center ">
                                    {{PatientObject.account_Number}}</td>
                                <td *ngIf="!device" class="width-10per text-center">{{PatientObject.mrn}}</td>
                                <td *ngIf="!device" class="width-8per text-center">{{PatientObject.dob}}</td>
                                <td *ngIf="!device" class="width-4per text-center">{{PatientObject.sex}}</td>
                                <td *ngIf="!device" class="width-8per text-center"><span
                                        *ngIf="PatientObject.ssn">******</span>{{PatientObject.ssn | slice:6:9}}
                                </td>
                                <td class="text-center" [ngClass]="{'width-6per': !device}">
                                    {{PatientObject.facility_Name}}</td>
                                <td *ngIf="!device" class="width-6per text-center">{{PatientObject.room_Number}}
                                </td>
                                <td *ngIf="!device" class="width-6per text-left">{{PatientObject.admission_Date1}}
                                </td>
                                <td *ngIf="!device" class="width-6per text-center">
                                    {{PatientObject.discharge_Date1}}</td>
                                <td class="pb-0" [ngClass]="{'width-10per small-scroll-2 small-scroll ml-2': !device}">
                                    {{PatientObject.primary_Coverage}}
                                </td>
                            </tr>
                            <tr
                                *ngIf="PatientObject.account_Number==envAccountNo&&PatientObject.mrn==envMrnNo&&PatientObject.facility_Name==envFacility">
                                <td colspan="11" class="inner-table-boder" style="background: #fff !important;">
                                    <div class="row mx-0" [ngClass]="{'innerscroll': !device}">
                                        <div class="col-12 px-md-1 px-0">
                                            <!-- innerconetct start -->
                                            <div class="row mx-0">
                                                <div class="col-12 ml-auto text-right pb-1 px-0">

                                                    <a class="" (click)="getAttachments(PatientObject)">
                                                        <img alt=' ' src="../../../assets/img/pin.png"
                                                            title="Attachements" width="26px" data-target="#attachment"
                                                            data-toggle="modal" class="mx-1">
                                                    </a>
                                                    <a class="text-center" (click)="openNotes(PatientObject)">
                                                        <img alt=' ' src="../../../assets/img/notespin.png"
                                                            title="Notes" width="26px" class="mx-1">
                                                    </a>
                                                    <a class="mx-1" (click)="getMissingEncounters(PatientObject)">
                                                        <img alt=' ' src="../../../assets/img/treatment-plan.png"
                                                            title="Missing Encounters" width="26px"
                                                            data-target="#missingEn" data-toggle="modal">
                                                    </a>
                                                </div>
                                            </div>
                                            <div *ngIf="device" class="row mx-0 text-secondary border bg-light my-2">
                                                <div class="col-12 m-auto p-1">
                                                    <div
                                                        class="d-inline-block pr-md-2 pr-2 text-left m-auto callout callout-primary bg-white">
                                                        <h6 class="card-title small my-auto px-1">Name-:
                                                            <span>{{PatientObject.patient_Name}}</span></h6>
                                                    </div>
                                                    <div
                                                        class="d-inline-block pr-md-2 pr-2 text-left m-auto callout callout-primary bg-white">
                                                        <h6 class="card-title small my-auto px-1">Account:
                                                            <span>{{PatientObject.account_Number}}</span></h6>
                                                    </div>
                                                    <div
                                                        class="d-inline-block pr-md-2 pr-2 text-left m-auto callout callout-primary bg-white">
                                                        <h6 class="card-title small my-auto px-1">MRN:
                                                            <span>{{PatientObject.mrn}}</span></h6>
                                                    </div>
                                                    <div
                                                        class="d-inline-block pr-md-2 pr-2 text-left m-auto callout callout-primary bg-white">
                                                        <h6 class="card-title small my-auto px-1">DOB:
                                                            <span>{{PatientObject.dob}}</span></h6>
                                                    </div>
                                                    <div
                                                        class="d-inline-block pr-md-2 pr-2 text-left m-auto callout callout-primary bg-white">
                                                        <h6 class="card-title small my-auto px-1">Sex:
                                                            <span>{{PatientObject.sex}}</span></h6>
                                                    </div>
                                                    <div
                                                        class="d-inline-block pr-md-2 pr-2 text-left m-auto callout callout-primary bg-white">
                                                        <h6 class="card-title small my-auto px-1">SSN: <span><span
                                                                    *ngIf="PatientObject.ssn">******</span>{{PatientObject.ssn
                                                                | slice:6:9}}</span>
                                                        </h6>
                                                    </div>
                                                    <div
                                                        class="d-inline-block pr-md-2 pr-2 text-left m-auto callout callout-primary bg-white">
                                                        <h6 class="card-title small my-auto px-1">Facility:
                                                            <span>{{PatientObject.facility_Name}}</span></h6>
                                                    </div>
                                                    <div
                                                        class="d-inline-block pr-md-2 pr-2 text-left m-auto callout callout-primary bg-white">
                                                        <h6 class="card-title small my-auto px-1">Room:
                                                            <span>{{PatientObject.room_Number}}</span></h6>
                                                    </div>
                                                    <div
                                                        class="d-inline-block pr-md-2 pr-2 text-left m-auto callout callout-primary bg-white">
                                                        <h6 class="card-title small my-auto px-1">Adm.:
                                                            <span>{{PatientObject.admission_Date}}</span></h6>
                                                    </div>
                                                    <div
                                                        class="d-inline-block pr-md-2 text-left m-auto callout callout-primary bg-white">
                                                        <h6 class="card-title small my-auto px-1">Primary Coverage:
                                                            <span>{{PatientObject.primary_Coverage}}</span></h6>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- encounter starts>-->
                                            <app-encounter [lisfOfEncounters]="lisfOfEncounters"
                                                [PatientObject]="PatientObject">
                                            </app-encounter>
                                            <!--- <encouter ends -->

                                        </div>
                                    </div>
                                </td>
                            </tr>

                        </tbody>

                    </table>

                </div>
            </div>
        </div>
    </div>
</div>

<!---- View History Start-->
<app-view-encounter-history [listOfHistory]='listOfHistory'></app-view-encounter-history>
<!---- View History End-->
<!-- attachment popup starts -->
<app-upload-attachment (eventUpdateCount)="updateAttCount(PatientObject)" [lisfOfAttachments]="lisfOfAttachments" [PatientObject]="PatientObject"
    [userType]="'COMMON'"></app-upload-attachment>
<!-- attachment popup ends -->

<!-- send note Popup Starts -->
<app-send-note [PatientObject]="PatientObject" [listOfUsersAndGroups]="listOfUsersAndGroups"
    [lisfOfSentNotes]="lisfOfSentNotes" [selectedUsersAndGroups]="selectedUsersAndGroups" (eventListOfNotes)="openNotes(PatientObject)"></app-send-note>
<!-- send note Popup Ends -->

<!-- Missing Encounter popup starts -->
<app-add-missing-encounter [lisfOfMissingEncounters]="lisfOfMissingEncounters" [PatientObject]="PatientObject"
    [listOfPhysicians]="listOfPhysicians"></app-add-missing-encounter>
<!-- Missing Encounter popup ends -->