<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new p-2" style="background: #0169ab;" [formGroup]="FilterForm">
                    <div class="align-items-center row mx-auto">
                        <div class="col-12 px-0">

                            <div class="row mt-1 mx-auto">
                                <div class="col-12 col-md-5 px-1 py-1">
                                    <select id="ddlFacility" class="form-control" formControlName="ddlFacility"
                                        (change)="getPhysiciansByFacility($any($event.target).value)"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                                        <option value="">---Select Facility---</option>
                                        <option value="All">All Facilities</option>
                                        <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                                            <span>{{s.facilityName}}</span>
                                        </option>
                                    </select>
                                    <div *ngIf="submitted && f['ddlFacility'].errors" class="invalid-feedback">
                                        <div *ngIf="f['ddlFacility'].errors['required']">Facility is required</div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-5 px-1 py-1">
                                    <select id="ddlPhysician" class="form-control custom-control"
                                        formControlName="ddlPhysician"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlPhysician'].errors}">
                                        <option value="">---Select Physician---</option>
                                        <option value="All">All Physicians</option>
                                        <option [value]="s.physicianEmaiId" *ngFor="let s of listOfPhysicians">
                                            {{s.physicianName}}
                                        </option>
                                    </select>
                                    <div *ngIf="submitted && f['ddlPhysician'].errors" class="invalid-feedback">
                                        <div *ngIf="f['ddlPhysician'].errors['required']">Physician is required</div>
                                    </div>
                                </div>
                                <div class="col-12 px-1 py-1"
                                    [ngClass]="{'col-md-1': this.listOfReports.length, 'col-md-2': !this.listOfReports.length}">
                                    <button class="btn btn-outline-info px-2 btn-block" type="submit"
                                        (click)="filterReport()">
                                        <i class="fa-filter fas font-size-13"></i>
                                        Filter
                                    </button>
                                </div>
                                <div *ngIf="this.listOfReports.length>0" class="col-12 col-md-1 px-1 py-1 text-center">
                                    <img alt=' ' (keyup)="exportAsXLSX()" (click)="exportAsXLSX()" class="ml-2 shadow"
                                        [src]="img1" style="width:40px;cursor:pointer;" title="Export Excel">
                                </div>
                            </div>

                            <div class="row my-1 mx-auto">
                                <div class="col-12 px-1 py-1"
                                    [ngClass]="{'col-md-11': this.listOfReports.length, 'col-md-12': !this.listOfReports.length}">
                                    <div class="input-group">
                                        <input type="text" class="form-control small" maxlength="1000"
                                            placeholder="Search for..." aria-label="Search"
                                            aria-describedby="basic-addon2" [(ngModel)]="searchByName"
                                            [ngModelOptions]="{standalone: true}" (keyup)="search(searchByName)">
                                        <div class="input-group-append">
                                            <button class="bg-gradient-light btn text-dark" type="button">
                                                <i class="fas fa-search fa-sm"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Card Body -->
                <div class="card-body px-2 py-1">
                    <div class="row mx-0">
                        <div class="col-12 px-0">
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered table-hover my-auto">
                                    <tr>
                                        <th class="text-center pb-0 cursor-pointer"
                                            (keyup)="sortColumn('accountNumber')" (click)="sortColumn('accountNumber')">
                                            Account No#
                                            <span class="float-right" tooltip="Sort By Account No#"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'accountNumber' && orderByAccountNumber == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'accountNumber' && orderByAccountNumber == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'accountNumber'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer" (keyup)="sortColumn('patientName')"
                                            (click)="sortColumn('patientName')">
                                            Patient Name
                                            <span class="float-right" tooltip="Sort By Patient Name"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'patientName' && orderByPatientName == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'patientName' && orderByPatientName == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'patientName'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer" (keyup)="sortColumn('dob')"
                                            (click)="sortColumn('dob')">
                                            DOB
                                            <span class="float-right" tooltip="Sort By dob"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'dob' && orderByPatientMRNDOB == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'dob' && orderByPatientMRNDOB == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'dob'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer" (keyup)="sortColumn('facilityName')"
                                            (click)="sortColumn('facilityName')">
                                            Facility Name
                                            <span class="float-right" tooltip="Sort By Facility Name"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'facilityName' && orderByFacilityName == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'facilityName' && orderByFacilityName == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'facilityName'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer"
                                            (keyup)="sortColumn('missedEncounterSeenDate')"
                                            (click)="sortColumn('missedEncounterSeenDate')">
                                            Missed Encounter Seen Date
                                            <span class="float-right" tooltip="Sort By Missed Encounter Seen Date"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'missedEncounterSeenDate' && orderByMissedEncounterSeenDate == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'missedEncounterSeenDate' && orderByMissedEncounterSeenDate == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'missedEncounterSeenDate'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer"
                                            (keyup)="sortColumn('admissionDate')" (click)="sortColumn('admissionDate')">
                                            Admission Date
                                            <span class="float-right" tooltip="Sort By Admission Date"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'admissionDate' && orderByAdmissionDate == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'admissionDate' && orderByAdmissionDate == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'admissionDate'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer" (keyup)="sortColumn('reportedBy')"
                                            (click)="sortColumn('reportedBy')">
                                            Reported By
                                            <span class="float-right" tooltip="Sort By Admission Date"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'reportedBy' && orderByReportedBy == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'reportedBy' && orderByReportedBy == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'reportedBy'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer" (keyup)="sortColumn('reportedDate')"
                                            (click)="sortColumn('reportedDate')">
                                            Reported Date
                                            <span class="float-right" tooltip="Sort By Reported By"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'reportedDate' && orderByReportedDate == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'reportedDate' && orderByReportedDate == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'reportedDate'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer"
                                            (keyup)="sortColumn('physicianName')" (click)="sortColumn('physicianName')">
                                            Physican
                                            <span class="float-right" tooltip="Sort By Physican"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'physicianName' && orderByPhysicianName == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'physicianName' && orderByPhysicianName == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'physicianName'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer"
                                            (keyup)="sortColumn('physicianSubmitted')"
                                            (click)="sortColumn('physicianSubmitted')">
                                            Physican Submitted
                                            <span class="float-right" tooltip="Sort By Physican Submitted"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'physicianSubmitted' && orderByPhysicianSubmitted == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'physicianSubmitted' && orderByPhysicianSubmitted == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'physicianSubmitted'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer"
                                            (keyup)="sortColumn('physicianSubmittedDate')"
                                            (click)="sortColumn('physicianSubmittedDate')">
                                            Physician Submitted Date
                                            <span class="float-right" tooltip="Sort By Physician Submitted Date"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'physicianSubmittedDate' && orderByPhysicianSubmittedDate == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'physicianSubmittedDate' && orderByPhysicianSubmittedDate == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'physicianSubmittedDate'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                    </tr>
                                    <tr
                                        *ngFor="let item of listOfReports| paginate: { itemsPerPage:11, currentPage: p}">
                                        <td>{{item.accountNumber}}</td>
                                        <td>{{item.patientName}}</td>
                                        <td>{{item.dob}}</td>
                                        <td>{{item.facilityName}}</td>
                                        <td>{{item.missedEncounterSeenDate}}</td>
                                        <td>{{item.admissionDate}}</td>
                                        <td>{{item.reportedBy}}</td>
                                        <td>{{item.reportedDate}}</td>
                                        <td>{{item.physicianName}}</td>
                                        <td>{{item.physicianSubmitted}}</td>
                                        <td>{{item.physicianSubmittedDate}}</td>
                                    </tr>
                                    <tr>
                                        <td colspan="10" class="m-0 p-0" style="background: white !important;">
                                            <pagination-controls previousLabel="" nextLabel=""
                                                (pageChange)="p = $event"></pagination-controls>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                </div>

            </div>
        </div>
        <!-- Area Chart Ends -->
    </div>
</div>
<!-- Begin Page Content Ends -->