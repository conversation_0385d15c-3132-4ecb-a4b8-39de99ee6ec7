import { Component, OnInit } from '@angular/core';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { CommonService } from 'src/app/services/common/common.service';
import { AppComponent } from 'src/app/app.component';
import { FormGroup, FormBuilder, Validators, FormControl } from '@angular/forms';
import { CoordinatorService } from 'src/app/services/coordinator/coordinator.service';
import { FacilityModel } from 'src/app/models/facility.model';
import { PhysicianModel } from 'src/app/models/physician.model';
import { PhysicianService } from 'src/app/services/physician/physician.service';
import { DatePipe } from '@angular/common';
import { ToastrService } from 'ngx-toastr';

declare let $: any;

@Component({
  selector: 'app-view-modify-encounters',
  templateUrl: './view-modify-encounters.component.html',
  styleUrls: ['./view-modify-encounters.component.css']
})
export class ViewModifyEncountersComponent implements OnInit {

  public FilterForm: FormGroup;
  public listOfFacilities = Array<FacilityModel>();
  public listOfPatients: Array<any> = [];
  public request: any = {};
  public p: number = 1;
  public searchByName: string = "";
  public totalCount: number;
  public searchByFacility: string = "";
  public listOfPhysicians = Array<PhysicianModel>();
  public submitted: boolean = false;
  public firstEcounterIdToOpen: string = "";
  public envAccountNo: string = "";
  public envMrnNo: string = "";
  public envFacility: string = "";
  public lisfOfEncounters: Array<any> = [];
  public isEnvRemoveFromBilling: number = 1;
  public PatientObject: any = {};
  public encounterObj: any = {};
  public listOfHistory: Array<any> = [];
  public lisfOfAttachments: Array<any> = [];
  public groupAlerts: any = {};
  public patient: any = {};
  public listOfUsersAndGroups: Array<any> = [];
  public lisfOfSentNotes: Array<any> = [];
  public selectedUsersAndGroups: Array<any> = [];
  public savedCoordinatorNotes: string = '';
  public lisfOfMissingEncounters: Array<any> = [];
  public timeout: any = null;
  public encounterIds: string = "";
  public daterange: any = {};
  public arrival = '';
  public arrivalLabel: string = "";
  public arrivalDateVal: boolean = false;
  device = false;
  public isDisabled: boolean = false;

  constructor(private readonly coordinatorServ: CoordinatorService, private readonly phyServ: PhysicianService, private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService
    , private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly datePipe: DatePipe, private readonly toastr: ToastrService) {

  }

  ngOnInit() {
    if (/Android|webOS|iPhone|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) && (window.innerWidth <= 575 || window.innerWidth >= 575)) {
      this.device = true;
    } else {
      this.device = false;
    }

    this.appComp.loadPageName('Physician View/Edit', 'physicianTab');
    this.FilterForm = this.fb.group({
      ddlFacility: [''],
      encounterSeenDateFrom: ['', [Validators.nullValidator, endDateValidator]],
      encounterSeenDateTo: ['', [Validators.nullValidator, endDateValidator]],
      discharged: [false],
      ddlMarkAsApp: ['', [Validators.nullValidator]]
    });

    this.getFacilities();
  }

  getPatientsByFacility(facility) {
    this.getPatientsViewCharges("1");
    this.p = 1;
  }

  getFacilities() {
    this.commonServ.getFacilitiesByUserType('PHYSICIAN').subscribe((p: any) => {
      this.listOfFacilities = p;
      this.FilterForm.get('ddlFacility')?.setValue(this.listOfFacilities[0].facilityName);
      this.getPatientsByFacility(this.FilterForm.value.ddlFacility);
    }, error => { console.error(error.status); });
  }
  get f() { return this.FilterForm.controls; }

  getPatientsViewCharges(pno) {
    this.p = pno;
    this.submitted = true;

    if (this.FilterForm.invalid) {
      return;
    }
    if (!this.FilterForm.value.encounterSeenDateFrom && !this.FilterForm.value.encounterSeenDateTo && this.FilterForm.value.encounterSeenDateFrom > this.FilterForm.value.encounterSeenDateTo) {
      return;
    }

    this.commonServ.startLoading();
    this.request.Facility_Name = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.MarkasSeenDateFrom = this.encrDecr.set(this.FilterForm.value.encounterSeenDateFrom);
    this.request.MarkasSeenDateTo = this.encrDecr.set(this.FilterForm.value.encounterSeenDateTo);
    this.request.strPageNumber = this.encrDecr.set(pno);
    this.request.strPagesize = this.encrDecr.set("14");
    this.request.searchText = this.encrDecr.set(this.searchByName);
    this.phyServ.getPatientsDataForPhysician(this.request).subscribe((p: any) => {
      this.submitted = false;
      this.totalCount = p.patientCount[0].totalCount;
      this.listOfPatients = p.patient;
      this.request = {};
      this.PatientObject = {};
      this.commonServ.stopLoading();
    }, error => { console.error(error.status); }
    )
  }

  SearchPatientData() {
    clearTimeout(this.timeout);

    this.timeout = setTimeout(() => {
      this.getPatientsViewCharges(1);
    }, 2000);
  }

  getEncountersByPatient(pObj, ui_click: boolean) {
    if (!ui_click || this.PatientObject.account_Number != pObj.account_Number) {
      this.envAccountNo = pObj.account_Number;
      this.envMrnNo = pObj.mrn;
      this.envFacility = pObj.facility_Name;
      this.commonServ.startLoading();
      this.request.account_Number = this.encrDecr.set(pObj.account_Number);
      this.request.facilityName = this.encrDecr.set(pObj.facility_Name);
      this.phyServ.getEncountersByPatient(this.request).subscribe((p: any) => {
        this.lisfOfEncounters = p.listofEncounters;
        this.lisfOfEncounters.forEach(y => y.listOfEncounters.forEach(x => x.encounterseendate = this.encrDecr.get(x.encounterseendate)));
        this.isEnvRemoveFromBilling = p.isEnvRemoveFromBilling;
        this.firstEcounterIdToOpen = p.firstEcounterIdToOpen;
        this.request = {};
        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        console.error(error.status);
        this.commonServ.stopLoading();
      });
      this.PatientObject = pObj;
    }
  }
  getAlerts(pObj, gName, lstEnvs) {
    this.commonServ.startLoading();
    this.PatientObject = pObj;
    this.request.Patient_Account_Number = this.encrDecr.set(pObj.account_Number);
    this.request.Facility_Name = this.encrDecr.set(pObj.facility_Name);
    this.request.GroupName = this.encrDecr.set(gName);
    lstEnvs.forEach((x, index) => {
      this.encounterIds = this.encounterIds + "," + x.encounteR_ID;
    });
    this.request.Encounter_Ids = this.encounterIds;
    this.encounterIds = "";
    this.coordinatorServ.getAlerts(this.request).subscribe((p: any) => {
      this.request = {};
      this.groupAlerts = p;
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
      console.error(error.status);
    });
  }
  clearFilterBillerPatients() {
    this.submitted = false;
    this.FilterForm.get('encounterSeenDateFrom')?.setValue('');
    this.FilterForm.get('encounterSeenDateTo')?.setValue('');
  }



}

export function endDateValidator(control: FormControl) {
  let startDate = control.root.get('encounterSeenDateFrom');
  let endDate = control.root.get('encounterSeenDateTo');
  if (startDate && endDate) {
    let startDateVal = new Date(startDate.value);
    let endDateVal = new Date(endDate.value);
    let errorObj = { 'encounterSeenDateFrom': false, 'encounterSeenDateTo': false };
    let isEndDateValid = true;
    if (startDate.value && endDate.value) {
      isEndDateValid = (startDateVal < endDateVal || startDateVal.getDate() == endDateVal.getDate());
    }
    endDate.setErrors((!isEndDateValid)
      ? errorObj : null);
    startDate.setErrors((!isEndDateValid)
      ? errorObj : null);
    if (control.errors) { return { "endDateError": true } };
  }
  return null;
}
