import { Component, OnInit, HostListener } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, Observable } from 'rxjs';
import { debounceTime, distinctUntilChanged, tap, switchMap } from 'rxjs/operators';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { UserManagementService } from 'src/app/services/user-management/userManagement.service';
declare let $;

@Component({
  selector: 'app-edit-user-details',
  templateUrl: './edit-user-details.component.html',
  styleUrls: ['./edit-user-details.component.css']
})
export class EditUserDetailsComponent implements OnInit {
  public userForm: FormGroup;
  public userDetails: any = {};
  public userDetailsInitial: any = {};
  public submitted: boolean = false;
  public listOfTitles: Array<any> = [];
  public listOfSpecialities: Array<any> = [];
  public listOfFacilities: Array<any> = [];
  public request: any = {};
  // search code starts
  public searchEmp = '';
  public adminModuleAccess: string = 'NO';
  public type: string;
  public emptyObser=new Observable<any>();
  public term$:BehaviorSubject<any> = new BehaviorSubject<any>([]);
  public results$: any = this.term$.pipe(
    debounceTime(1000),
    distinctUntilChanged(),
    tap(() => {
      const element = document.getElementById('page-top')!;
        element.classList.remove('loading');
    }),
    switchMap((term: string) => {
      if (term.length > 0) {
        return this.commonServ.getADUsers(term);
      }
      else {
        const element = document.getElementById('page-top')!;
        element.classList.remove('loading');
        return this.emptyObser;
      }
    }),
    tap(() => {
      const element = document.getElementById('page-top')!;
        element.classList.remove('loading');
    })
  );
  public seachClass: string;
  public isClicked = false;
  @HostListener('document:click')
  clickout() {
    if (!this.isClicked) {
      this.seachClass = 'hide';
    }
    this.isClicked = false;
  }
  showSearch() {
    this.seachClass = 'show';
  }
  // search code ends
  public isRemoveCompleteAccess: boolean = false;
  public filterObj: any = {};
  public isKareoFacility: boolean = false;
  public trueValue:boolean=true;
  public falseValue:boolean=false;

  constructor(private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly commonServ: CommonService, private readonly userSer: UserManagementService,
    private readonly encrDecr: EncrDecrServiceService, private readonly toastr: ToastrService, private readonly router: Router) { }

  ngOnInit(): void {

    this.appComp.loadPageName('Edit User Details', 'usermanagementTab');
    this.adminModuleAccess = this.appComp.userAccess.adminModuleAccess;
    this.userDetailsInitial = { ...history.state.user };
    this.userDetails = history.state.user;
    this.type = history.state.type;
    this.filterObj = history.state.filterObj;
    this.updateUserDetails(true);

    this.getUserTitlesSpecialitiesAndFacilities();
    this.userForm = this.fb.group({
      ddlTitle: ['0'],
      txtFirstName: [''],
      txtLastName: [''],
      txtMiddleName: ['', Validators.pattern('^[0-9a-zA-Z-’\'"“” ()_?!:;.,stn]+$')],
      txtDisplayName: [''],
      txtkareoPhysician: [''],
      ddlFacility: ['', Validators.required],
      txtNPI: [''],
      txtLoginID: ['', Validators.required],
      ddlAccountStatus: ['', Validators.required],
      ddlSpeciality: [''],
      chkPhysician: [false],
      chkCoordinator: [false],
      chkAuditor: [false],
      chkBiller: [false],
      chkUserManagement: [false],
      chkAdministrator: [false],
      chkResident: [false],
      chkMobile: [false],
      chkDashboard: [false],
      chkReport: [false],
      chkKareoDashboard: [false]
    });
  }

  get f() { return this.userForm.controls; }

  getUserTitlesSpecialitiesAndFacilities() {
    this.commonServ.startLoading();
    this.userSer.getUserTitlesSpecialitiesAndFacilities().subscribe((p: any) => {
      this.listOfTitles = p.listOfTitles;
      this.listOfSpecialities = p.listOfSpecialities;
      this.listOfFacilities = p.listOfFacilities;
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  insertUpdateUserDetails() {
    this.submitted = true;
    if (this.userForm.invalid) {
      return;
    }
    this.commonServ.startLoading();
    this.request.user_id = this.encrDecr.set(this.userDetails.user_id);
    this.request.title = this.encrDecr.set(this.userForm.value.ddlTitle != '0' ? $('#ddlTitle option:selected').text() : '');
    this.request.title_id = this.encrDecr.set(this.userForm.value.ddlTitle ?? '0');
    this.request.firstname = this.encrDecr.set(this.userForm.value.txtFirstName);
    this.request.lastname = this.encrDecr.set(this.userForm.value.txtLastName);
    this.request.middlename = this.encrDecr.set(this.userForm.value.txtMiddleName ? this.userForm.value.txtMiddleName : '');
    this.request.npi = this.encrDecr.set(this.userForm.value.txtNPI ? this.userForm.value.txtNPI : '');
    this.request.specialty = this.encrDecr.set(this.userForm.value.ddlSpeciality ? this.userForm.value.ddlSpeciality : '');
    this.request.facilityname = this.encrDecr.set($('#ddlFacility option:selected').text());
    this.request.facility_id = this.encrDecr.set(this.userForm.value.ddlFacility);
    this.request.display_name = this.encrDecr.set(this.userForm.value.txtDisplayName);
    this.request.email_id = this.encrDecr.set(this.userForm.value.txtLoginID);
    this.request.accountstatus = this.encrDecr.set(this.userForm.value.ddlAccountStatus);
    this.request.BillerModuleAccess = this.encrDecr.set(this.userForm.value.chkBiller);
    this.request.PhysicianModuleAccess = this.encrDecr.set(this.userForm.value.chkPhysician);
    this.request.AllowPhysicianRounds = this.encrDecr.set(this.userForm.value.chkMobile);
    this.request.CoordinatorModuleAccess = this.encrDecr.set(this.userForm.value.chkCoordinator);
    this.request.AdminModuleAccess = this.encrDecr.set(this.userForm.value.chkAdministrator);
    this.request.UserManagementAccess = this.encrDecr.set(this.userForm.value.chkUserManagement);
    this.request.AuditorModuleAccess = this.encrDecr.set(this.userForm.value.chkAuditor);
    this.request.ResidentModuleAccess = this.encrDecr.set(this.userForm.value.chkResident);
    this.request.dashboardModuleAccess = this.encrDecr.set(this.userForm.value.chkDashboard);
    this.request.reportModuleAccess = this.encrDecr.set(this.userForm.value.chkReport);
    this.request.kareoDashboardModuleAccess = this.encrDecr.set(this.userForm.value.chkKareoDashboard);
    this.userSer.insertOrUpdateUserDetails(this.request).subscribe((p: any) => {
      this.request = {};
      this.submitted = false;
      this.commonServ.stopLoading();
      this.toastr.success('Details Updated Successfully.', '', { timeOut: 2500 });
      if (this.userForm.value.ddlAccountStatus == 'Disabled') {
        this.router.navigate(['/usermanagement/add-modify-user'], { state: { filterObj: this.filterObj } });
      }
      else {
        this.updateUserDetailsInitial();
      }
      this.userForm.reset();
    },
      error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
  }

  updateUserDetailsInitial() {
    this.userDetailsInitial.billerModuleAccess = this.userForm.value.chkBiller ? 'YES' : 'NO';
    this.userDetailsInitial.physicianModuleAccess = this.userForm.value.chkPhysician ? 'YES' : 'NO';
    this.userDetailsInitial.auditorModuleAccess = this.userForm.value.chkAuditor ? 'YES' : 'NO';
    this.userDetailsInitial.adminModuleAccess = this.userForm.value.chkAdministrator ? 'YES' : 'NO';
    this.userDetailsInitial.coordinatorModuleAccess = this.userForm.value.chkCoordinator ? 'YES' : 'NO';
    this.userDetailsInitial.userManagementAccess = this.userForm.value.chkUserManagement ? 'YES' : 'NO';
    this.userDetailsInitial.residentModuleAccess = this.userForm.value.chkResident ? 'YES' : 'NO';
    this.userDetailsInitial.allowPhysicianRounds = this.userForm.value.chkMobile ? 'YES' : 'NO';
    this.userDetailsInitial.dashboardModuleAccess = this.userForm.value.chkDashboard ? 'YES' : 'NO';
    this.userDetailsInitial.reportModuleAccess = this.userForm.value.chkReport ? 'YES' : 'NO';
    this.userDetailsInitial.kareoDashboardModuleAccess = this.userForm.value.chkKareoDashboard ? 'YES' : 'NO';
    this.router.navigate(['/usermanagement/add-modify-role'], { state: { user: this.userDetailsInitial, filterObj: this.filterObj } });
  }

  updateUserDetails(flag: boolean) {
    this.userDetails.billerModuleAccess = this.userDetails.billerModuleAccess == 'YES' ? this.trueValue : this.falseValue;
    this.userDetails.physicianModuleAccess = this.userDetails.physicianModuleAccess == 'YES' ? this.trueValue : this.falseValue;
    this.userDetails.auditorModuleAccess = this.userDetails.auditorModuleAccess == 'YES' ? this.trueValue : this.falseValue;
    this.userDetails.adminModuleAccess = this.userDetails.adminModuleAccess == 'YES' ? this.trueValue : this.falseValue;
    this.userDetails.coordinatorModuleAccess = this.userDetails.coordinatorModuleAccess == 'YES' ? this.trueValue : this.falseValue;
    this.userDetails.userManagementAccess = this.userDetails.userManagementAccess == 'YES' ? this.trueValue : this.falseValue;
    this.userDetails.residentModuleAccess = this.userDetails.residentModuleAccess == 'YES' ? this.trueValue : this.falseValue;
    this.userDetails.allowPhysicianRounds = this.userDetails.allowPhysicianRounds == 'YES' ? this.trueValue : this.falseValue;
    this.userDetails.dashboardModuleAccess = this.userDetails.dashboardModuleAccess == 'YES' ? this.trueValue : this.falseValue;
    this.userDetails.reportModuleAccess = this.userDetails.reportModuleAccess == 'YES' ? this.trueValue : this.falseValue;
    this.userDetails.kareoDashboardModuleAccess = this.userDetails.kareoDashboardModuleAccess == 'YES' ? this.trueValue : this.falseValue;

    if (flag && (this.userDetails.physicianModuleAccess || this.userDetails.coordinatorModuleAccess || this.userDetails.auditorModuleAccess
      || this.userDetails.userManagementAccess || this.userDetails.adminModuleAccess || this.userDetails.billerModuleAccess
      || this.userDetails.residentModuleAccess || this.userDetails.allowPhysicianRounds || this.userDetails.dashboardModuleAccess
      || this.userDetails.reportModuleAccess || this.userDetails.kareoDashboardModuleAccess)) {
        this.isRemoveCompleteAccess = true;
    }
  }

  chkChangeEvent(user) {
    $('#txtUserName').val(user.display_name);
    this.seachClass = 'hide';
    this.userDetails = user;
    this.userDetailsInitial = { ...user };
    this.updateUserDetails(false);
  }

  removeCompleteAccess() {
    if (confirm('Do you want to remove complete access for this user?')) {
      this.commonServ.startLoading();
      this.request.user_id = this.encrDecr.set(this.userDetails.user_id);
      this.request.title = this.encrDecr.set($('#ddlTitle option:selected').text());
      this.request.title_id = this.encrDecr.set(this.userForm.value.ddlTitle);
      this.request.firstname = this.encrDecr.set(this.userForm.value.txtFirstName);
      this.request.lastname = this.encrDecr.set(this.userForm.value.txtLastName);
      this.request.middlename = this.encrDecr.set(this.userForm.value.txtMiddleName);
      this.request.npi = this.encrDecr.set(this.userForm.value.txtNPI);
      this.request.specialty = this.encrDecr.set(this.userForm.value.ddlSpeciality);
      this.request.facilityname = this.encrDecr.set($('#ddlFacility option:selected').text());
      this.request.facility_id = this.encrDecr.set(this.userForm.value.ddlFacility);
      this.request.display_name = this.encrDecr.set(this.userForm.value.txtDisplayName);
      this.request.email_id = this.encrDecr.set(this.userForm.value.txtLoginID);
      this.request.accountstatus = this.encrDecr.set(this.userForm.value.ddlAccountStatus);
      this.request.BillerModuleAccess = this.encrDecr.set('NO');
      this.request.PhysicianModuleAccess = this.encrDecr.set('NO');
      this.request.AllowPhysicianRounds = this.encrDecr.set('NO');
      this.request.CoordinatorModuleAccess = this.encrDecr.set('NO');
      this.request.AdminModuleAccess = this.encrDecr.set('NO');
      this.request.UserManagementAccess = this.encrDecr.set('NO');
      this.request.AuditorModuleAccess = this.encrDecr.set('NO');
      this.request.ResidentModuleAccess = this.encrDecr.set('NO');
      this.request.DashboardModuleAccess = this.encrDecr.set('NO');
      this.request.ReportModuleAccess = this.encrDecr.set('NO');
      this.request.KareoDashboardModuleAccess = this.encrDecr.set('NO');
      this.userSer.insertOrUpdateUserDetails(this.request).subscribe((p: any) => {
        this.request = {};
        this.submitted = false;
        this.commonServ.stopLoading();
        this.toastr.success('Access Removed Successfully.', '', { timeOut: 2500 });
        this.userForm.reset();
        this.router.navigate(['/usermanagement/add-modify-user'], { state: { filterObj: this.filterObj } });
      },
        error => {
          this.request = {};
          this.commonServ.stopLoading();
          console.error(error.status);
        });
    }
  }

  physicianModuleAccessEvent() {
    if (!this.userDetails.physicianModuleAccess) {
      this.userDetails.residentModuleAccess = false;
    }
  }

  residentModuleAccessEvent() {
    if (!this.userDetails.residentModuleAccess) {
      this.userDetails.physicianModuleAccess = false;
    }
  }

}
