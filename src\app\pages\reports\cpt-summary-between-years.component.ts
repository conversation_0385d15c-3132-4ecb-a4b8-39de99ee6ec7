import { Component, OnInit } from '@angular/core';
import * as Highcharts from 'highcharts';
import { CommonService } from 'src/app/services/common/common.service';
import { AppComponent } from 'src/app/app.component';
import { FacilityModel } from 'src/app/models/facility.model';
import { ReportsService } from 'src/app/services/reports/reports.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { ExcelServices } from 'src/app/services/common/ExcelService';
declare let $: any;

@Component({
  selector: 'app-cpt-summary-between-years',
  templateUrl: './cpt-summary-between-years.component.html',
  styles: []
})
export class CptSummaryBetweenYearsComponent implements OnInit {
  public listOfFacilities = Array<FacilityModel>();
  public request: any = {};
  public searchForm: FormGroup;
  public submitted: boolean = false;
  public cPTReportSummaryData: any = {};
  public lstOfReportData: Array<any> = [];
  public listOfYears: Array<any> = [];
  public img1: string = '.././../../assets/img/excel.png';
  constructor(private readonly commonServ: CommonService, private readonly reptServ: ReportsService, private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly excelService: ExcelServices) { }

  ngOnInit() {
    this.getYears();
    this.getFacilities();

    this.appComp.loadPageName('CPT Summary Between Years', 'reportsTab');

    this.searchForm = this.fb.group({
      ddlFacility: ['', Validators.required],
      ddlType: ['', Validators.required],
      ddlFromYear: ['', Validators.required],
      ddlToYear: ['', Validators.required]
    });
  }
  getYears() {
    let max = new Date().getFullYear(),
      min = max - 10;
    let years: any[] = [];
    for (let i = min; i <= max; i++) {

      years.push(i);
    }
    this.listOfYears = years;
  }

  get f() { return this.searchForm.controls; };

  getReportSummary() {
    this.submitted = true;
    if (this.searchForm.invalid) {
      return;
    }
    this.commonServ.startLoading();
    this.request.FacilityName = this.searchForm.value.ddlFacility;
    this.request.type = this.searchForm.value.ddlType;
    this.request.years = this.searchForm.value.ddlFromYear + ',' + this.searchForm.value.ddlToYear;
    this.reptServ.get_CPT_Summary_Between_Year(this.request).subscribe((p: any) => {
      let physicians: Array<any> = [];
      let seriesArray: Array<any> = [];

      let y1C1ObjArray: Array<any> = [];
      let y1C2ObjArray: Array<any> = [];
      let y1C3ObjArray: Array<any> = [];
      let y1C4ObjArray: Array<any> = [];
      let y1C5ObjArray: Array<any> = [];
      let y1C6ObjArray: Array<any> = [];
      let y1C7ObjArray: Array<any> = [];
      let y1C8ObjArray: Array<any> = [];

      let y2C1ObjArray: Array<any> = [];
      let y2C2ObjArray: Array<any> = [];
      let y2C3ObjArray: Array<any> = [];
      let y2C4ObjArray: Array<any> = [];
      let y2C5ObjArray: Array<any> = [];
      let y2C6ObjArray: Array<any> = [];
      let y2C7ObjArray: Array<any> = [];
      let y2C8ObjArray: Array<any> = [];

      this.cPTReportSummaryData = p.graphData;

      /** For All Types - start*/
      if (this.searchForm.value.ddlType == "All") {
        this.lstOfReportData = p.listofIHVData;

        this.cPTReportSummaryData.forEach(x => {
          physicians.push(x.physicianName);
          let isY1: boolean = true;
          let isY2: boolean = true;

          x.forEach(y => {
            if (y.yearvalue == this.searchForm.value.ddlFromYear) {
              isY1 = false;
              y1C1ObjArray.push(y.cpT_99221);
              y1C2ObjArray.push(y.cpT_99222);
              y1C3ObjArray.push(y.cpT_99223);
              y1C4ObjArray.push(y.cpT_99231);
              y1C5ObjArray.push(y.cpT_99232);
              y1C6ObjArray.push(y.cpT_99233);
              y1C7ObjArray.push(y.cpT_99291);
              y1C8ObjArray.push(y.cpT_99292);
            }
            else if (y.yearvalue == this.searchForm.value.ddlToYear) {
              isY2 = false;
              y2C1ObjArray.push(y.cpT_99221);
              y2C2ObjArray.push(y.cpT_99222);
              y2C3ObjArray.push(y.cpT_99223);
              y2C4ObjArray.push(y.cpT_99231);
              y2C5ObjArray.push(y.cpT_99232);
              y2C6ObjArray.push(y.cpT_99233);
              y2C7ObjArray.push(y.cpT_99291);
              y2C8ObjArray.push(y.cpT_99292);
            }

            if (isY1 && x.length == 1) {
              y1C1ObjArray.push(0);
              y1C2ObjArray.push(0);
              y1C3ObjArray.push(0);
              y1C4ObjArray.push(0);
              y1C5ObjArray.push(0);
              y1C6ObjArray.push(0);
              y1C7ObjArray.push(0);
              y1C8ObjArray.push(0);

            }
            if (isY2 && x.length == 1) {
              y2C1ObjArray.push(0);
              y2C2ObjArray.push(0);
              y2C3ObjArray.push(0);
              y2C4ObjArray.push(0);
              y2C5ObjArray.push(0);
              y2C6ObjArray.push(0);
              y2C7ObjArray.push(0);
              y2C8ObjArray.push(0);

            }
          });
        });

        let y1C1Obj: any = { name: this.searchForm.value.ddlFromYear + ' - 99221', data: y1C1ObjArray };
        seriesArray.push(y1C1Obj);
        let y2C1Obj: any = { name: this.searchForm.value.ddlToYear + ' - 99221', data: y2C1ObjArray };
        seriesArray.push(y2C1Obj);

        let y1C2Obj: any = { name: this.searchForm.value.ddlFromYear + ' - 99222', data: y1C2ObjArray };
        seriesArray.push(y1C2Obj);
        let y2C2Obj: any = { name: this.searchForm.value.ddlToYear + ' - 99222', data: y2C2ObjArray };
        seriesArray.push(y2C2Obj);

        let y1C3Obj: any = { name: this.searchForm.value.ddlFromYear + ' - 99223', data: y1C3ObjArray };
        seriesArray.push(y1C3Obj);
        let y2C3Obj: any = { name: this.searchForm.value.ddlToYear + ' - 99223', data: y2C3ObjArray };
        seriesArray.push(y2C3Obj);

        let y1C4Obj: any = { name: this.searchForm.value.ddlFromYear + ' - 99231', data: y1C4ObjArray };
        seriesArray.push(y1C4Obj);
        let y2C4Obj: any = { name: this.searchForm.value.ddlToYear + ' - 99231', data: y2C4ObjArray };
        seriesArray.push(y2C4Obj);

        let y1C5Obj: any = { name: this.searchForm.value.ddlFromYear + ' - 99232', data: y1C5ObjArray };
        seriesArray.push(y1C5Obj);
        let y2C5Obj: any = { name: this.searchForm.value.ddlToYear + ' - 99232', data: y2C5ObjArray };
        seriesArray.push(y2C5Obj);

        let y1C6Obj: any = { name: this.searchForm.value.ddlFromYear + ' - 99233', data: y1C6ObjArray };
        seriesArray.push(y1C6Obj);
        let y2C6Obj: any = { name: this.searchForm.value.ddlToYear + ' - 99233', data: y2C6ObjArray };
        seriesArray.push(y2C6Obj);

        let y1C7Obj: any = { name: this.searchForm.value.ddlFromYear + ' - 99291', data: y1C7ObjArray };
        seriesArray.push(y1C7Obj);
        let y2C7Obj: any = { name: this.searchForm.value.ddlToYear + ' - 99291', data: y2C7ObjArray };
        seriesArray.push(y2C7Obj);

        let y1C8Obj: any = { name: this.searchForm.value.ddlFromYear + ' - 99292', data: y1C8ObjArray };
        seriesArray.push(y1C8Obj);
        let y2C8Obj: any = { name: this.searchForm.value.ddlToYear + ' - 99292', data: y2C8ObjArray };
        seriesArray.push(y2C8Obj);
      }
      /** For All Types - end*/

      if (this.searchForm.value.ddlType == "IHV") {
        this.lstOfReportData = p.listofIHVData;

        this.cPTReportSummaryData.forEach(x => {
          physicians.push(x.physicianName);
          let isY1: boolean = true;
          let isY2: boolean = true;

          x.forEach(y => {
            if (y.yearvalue == this.searchForm.value.ddlFromYear) {
              isY1 = false;
              y1C1ObjArray.push(y.cpT_99221);
              y1C2ObjArray.push(y.cpT_99222);
              y1C3ObjArray.push(y.cpT_99223);
            }
            else if (y.yearvalue == this.searchForm.value.ddlToYear) {
              isY2 = false;
              y2C1ObjArray.push(y.cpT_99221);
              y2C2ObjArray.push(y.cpT_99222);
              y2C3ObjArray.push(y.cpT_99223);
            }

            if (isY1 && x.length == 1) {
              y1C1ObjArray.push(0);
              y1C2ObjArray.push(0);
              y1C3ObjArray.push(0);

            }
            if (isY2 && x.length == 1) {
              y2C1ObjArray.push(0);
              y2C2ObjArray.push(0);
              y2C3ObjArray.push(0);

            }
          });
        });

        let y1C1Obj: any = { name: this.searchForm.value.ddlFromYear + ' - 99221', data: y1C1ObjArray };
        seriesArray.push(y1C1Obj);
        let y2C1Obj: any = { name: this.searchForm.value.ddlToYear + ' - 99221', data: y2C1ObjArray };
        seriesArray.push(y2C1Obj);

        let y1C2Obj: any = { name: this.searchForm.value.ddlFromYear + ' - 99222', data: y1C2ObjArray };
        seriesArray.push(y1C2Obj);
        let y2C2Obj: any = { name: this.searchForm.value.ddlToYear + ' - 99222', data: y2C2ObjArray };
        seriesArray.push(y2C2Obj);

        let y1C3Obj: any = { name: this.searchForm.value.ddlFromYear + ' - 99223', data: y1C3ObjArray };
        seriesArray.push(y1C3Obj);
        let y2C3Obj: any = { name: this.searchForm.value.ddlToYear + ' - 99223', data: y2C3ObjArray };
        seriesArray.push(y2C3Obj);
      }

      if (this.searchForm.value.ddlType == "SHV") {
        this.lstOfReportData = p.listofSHVData;

        this.cPTReportSummaryData.forEach(x => {
          physicians.push(x.physicianName);
          let isY1: boolean = true;
          let isY2: boolean = true;

          x.forEach(y => {
            if (y.yearvalue == this.searchForm.value.ddlFromYear) {
              isY1 = false;
              y1C1ObjArray.push(y.cpT_99231);
              y1C2ObjArray.push(y.cpT_99232);
              y1C3ObjArray.push(y.cpT_99233);
            }
            else if (y.yearvalue == this.searchForm.value.ddlToYear) {
              isY2 = false;
              y2C1ObjArray.push(y.cpT_99231);
              y2C2ObjArray.push(y.cpT_99232);
              y2C3ObjArray.push(y.cpT_99233);
            }

            if (isY1 && x.length == 1) {
              y1C1ObjArray.push(0);
              y1C2ObjArray.push(0);
              y1C3ObjArray.push(0);

            }
            if (isY2 && x.length == 1) {
              y2C1ObjArray.push(0);
              y2C2ObjArray.push(0);
              y2C3ObjArray.push(0);

            }
          });
        });

        let y1C1Obj: any = { name: this.searchForm.value.ddlFromYear + ' - 99231', data: y1C1ObjArray };
        seriesArray.push(y1C1Obj);
        let y2C1Obj: any = { name: this.searchForm.value.ddlToYear + ' - 99231', data: y2C1ObjArray };
        seriesArray.push(y2C1Obj);

        let y1C2Obj: any = { name: this.searchForm.value.ddlFromYear + ' - 99232', data: y1C2ObjArray };
        seriesArray.push(y1C2Obj);
        let y2C2Obj: any = { name: this.searchForm.value.ddlToYear + ' - 99232', data: y2C2ObjArray };
        seriesArray.push(y2C2Obj);

        let y1C3Obj: any = { name: this.searchForm.value.ddlFromYear + ' - 99233', data: y1C3ObjArray };
        seriesArray.push(y1C3Obj);
        let y2C3Obj: any = { name: this.searchForm.value.ddlToYear + ' - 99233', data: y2C3ObjArray };
        seriesArray.push(y2C3Obj);
      }

      if (this.searchForm.value.ddlType == "CC") {
        this.lstOfReportData = p.listofCCData;

        this.cPTReportSummaryData.forEach(x => {
          physicians.push(x.physicianName);
          let isY1: boolean = true;
          let isY2: boolean = true;

          x.forEach(y => {
            if (y.yearvalue == this.searchForm.value.ddlFromYear) {
              isY1 = false;
              y1C1ObjArray.push(y.cpT_99291);
              y1C2ObjArray.push(y.cpT_99292);
            }
            else if (y.yearvalue == this.searchForm.value.ddlToYear) {
              isY2 = false;
              y2C1ObjArray.push(y.cpT_99291);
              y2C2ObjArray.push(y.cpT_99292);
            }

            if (isY1 && x.length == 1) {
              y1C1ObjArray.push(0);
              y1C2ObjArray.push(0);

            }
            if (isY2 && x.length == 1) {
              y2C1ObjArray.push(0);
              y2C2ObjArray.push(0);

            }
          });
        });

        let y1C1Obj: any = { name: this.searchForm.value.ddlFromYear + ' - 99291', data: y1C1ObjArray };
        seriesArray.push(y1C1Obj);
        let y2C1Obj: any = { name: this.searchForm.value.ddlToYear + ' - 99291', data: y2C1ObjArray };
        seriesArray.push(y2C1Obj);

        let y1C2Obj: any = { name: this.searchForm.value.ddlFromYear + ' - 99292', data: y1C2ObjArray };
        seriesArray.push(y1C2Obj);
        let y2C2Obj: any = { name: this.searchForm.value.ddlToYear + ' - 99292', data: y2C2ObjArray };
        seriesArray.push(y2C2Obj);
      }

      if (this.searchForm.value.ddlType == "OTHER") {
        this.lstOfReportData = p.listOfOtherData;
        this.cPTReportSummaryData.forEach(x => {
          physicians.push(x.physicianName);
        });

        p.graphDataOther.forEach(x => {
          let itemY1Array: Array<any> = [];
          let itemY2Array: Array<any> = [];

          physicians.forEach(p => {
            let dataY1 = this.findDataYear(x.list, this.searchForm.value.ddlFromYear, x.cptName, p);
            if (dataY1)
              itemY1Array.push(dataY1.cpT_Used_Count);
            else
              itemY1Array.push(0);
            let dataY2 = this.findDataYear(x.list, this.searchForm.value.ddlToYear, x.cptName, p);
            if (dataY2)
              itemY2Array.push(dataY2.cpT_Used_Count);
            else
              itemY2Array.push(0);
          });

          let itemY1: any = { name: this.searchForm.value.ddlFromYear + ' - ' + x.cptName, data: itemY1Array };
          let itemY2: any = { name: this.searchForm.value.ddlToYear + ' - ' + x.cptName, data: itemY2Array };
          seriesArray.push(itemY1);
          seriesArray.push(itemY2);

        });
      }
      Highcharts.setOptions({
        colors: ['#50B432', '#ED561B', '#DDDF00', '#24CBE5', '#64E572', '#FF9655', '#FFF263', '#6AF9C4', '#4B0082', '#2F4F4F', '#0000FF', '#6B8E23', '#FFFF00', '#000080', '#8B008B', '#2F4F4F', '#800000']
      });
      let optionsCPTSummaryBtYs: any =
      {
        chart: {
          type: 'column'
        },
        title: {
          text: 'CPT Summary between ' + this.searchForm.value.ddlFromYear + ' and ' + this.searchForm.value.ddlToYear + ' ' + $('#ddlType option:selected').text()
        },
        xAxis: {
          categories: physicians
        },
        yAxis: {
          min: 0,
          title: {
            text: 'Number of CPT'
          }
        },
        tooltip: {
          pointFormat: '<span style="color:{series.color}">{series.name}</span>: <b>{point.y}</b> ({point.percentage:.0f}%)<br/>',
          shared: true
        },
        plotOptions: {
          column: {
            dataLabels: {
              enabled: true,
              align: 'right',
              format: '{point.percentage:.0f} %',

              style: {
                color: "#333333",
                fontSize: "10px"

              }
            },
            stacking: 'percent'
          }
        },
        series: seriesArray
      };

      Highcharts.chart('conatinerCPTSummaryBtYs', optionsCPTSummaryBtYs);
      $('.highcharts-credits').hide();
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });

  }

  findDataYear(list, yearvalue, cpt, physicianname) {
    return list.find(s => s.yearvalue == yearvalue && s.cpt == cpt && s.physicianname == physicianname);
  }

  getFacilities() {
    this.commonServ.getFacilities().subscribe((p: any) => {
      this.listOfFacilities = p;
    }, error => { console.error(error.status); });
  }

  exportAsXLSX(): void {
    let fileName = "";
    fileName = 'CPT_Summary_Between_' + this.searchForm.value.ddlFromYear + '_And_' + this.searchForm.value.ddlToYear + '_' + this.searchForm.value.ddlType;
    this.excelService.exportAsExcelFile(this.lstOfReportData, fileName);
  }

}
