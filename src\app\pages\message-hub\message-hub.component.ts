import { Component, OnInit } from '@angular/core';
import { MessageHubService } from 'src/app/services/message-hub/message-hub.service';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { ToastrService } from 'ngx-toastr';
declare let $: any;

@Component({
  selector: 'app-message-hub',
  templateUrl: './message-hub.component.html',
  styleUrls: ['./message-hub.component.css']
})
export class MessageHubComponent implements OnInit {
  public listOfMessages: Array<any> = [];
  public listOfFacilities: Array<any> = [];
  public p: number;
  public searchByName: string;
  public request: any = {};
  public device: boolean = false;
  public navigator = navigator;
  constructor(private readonly mgsServ: MessageHubService, private readonly appComp: AppComponent, private readonly commonServ: CommonService
    , private readonly encrDecr: EncrDecrServiceService, private readonly toastr: ToastrService) { }

  ngOnInit() {
    this.device=this.appComp.device;
    this.appComp.loadPageName('Message Hub', 'messageHubTab');
    this.getMessages();
  }

  isMobileDevice(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }

  isSafariDesktop(): boolean {
    const userAgent = navigator.userAgent;
    const isSafari = /Safari/i.test(userAgent) && !/Chrome/i.test(userAgent) && !/Chromium/i.test(userAgent);
    const isDesktop = !this.isMobileDevice();
    return isSafari && isDesktop;
  }

  isMobileSafari(): boolean {
    const userAgent = navigator.userAgent;
    const isSafari = /Safari/i.test(userAgent) && !/Chrome/i.test(userAgent) && !/Chromium/i.test(userAgent);
    const isMobile = /iPhone|iPad|iPod/i.test(userAgent);
    return isSafari && isMobile;
  }

  isOtherMobile(): boolean {
    return this.isMobileDevice() && !this.isMobileSafari();
  }

  getMessages() {
    this.commonServ.startLoading();
    this.mgsServ.getMessages().subscribe((p: any) => {
      this.listOfMessages = p;
      this.commonServ.stopLoading();
    }, error => { this.commonServ.stopLoading(); });
  }

  markAllAsRead() {
    this.commonServ.startLoading();
    this.request.sGROUP_ID = this.encrDecr.set('0');
    this.request.FLAG = this.encrDecr.set('ALL');
    this.mgsServ.markAsAllRead(this.request).subscribe((p: any) => {
      this.request = {};
      this.mgsServ.getMessages().subscribe((p: any) => {
        this.listOfMessages = p;
      }, error => { });
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
    });
  }

  markAsRead(groupId, isRead) {
    if (!isRead) {
      this.request.sGROUP_ID = this.encrDecr.set(groupId);
      this.request.FLAG = this.encrDecr.set('BADGE');
      this.mgsServ.markAsAllRead(this.request).subscribe((p: any) => {
        this.request = {};
      }, error => {
        this.request = {};
      });
    }

    const modalId = "#readMsg-" + groupId;

    // Safari specific modal initialization (both desktop and mobile)
    if (this.isSafariDesktop()) {
      this.initSafariModal(modalId);
    } else if (this.isMobileSafari()) {
      this.initMobileSafariModal(modalId);
    } else {
      // Simple modal opening for all other devices (Android, other mobile browsers, other desktop browsers)
      $(modalId).modal('show');
    }
  }

  // Safari desktop specific modal initialization
  initSafariModal(modalId: string) {
    // Clean up any existing modal states first
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');

    // Set up Safari-specific event handlers
    $(modalId).off('shown.bs.modal').on('shown.bs.modal', () => {
      // Ensure proper z-index and positioning
      $(modalId).css('z-index', '1051');
      $('.modal-backdrop').css('z-index', '1040');
    });

    $(modalId).off('hidden.bs.modal').on('hidden.bs.modal', () => {
      // Additional cleanup when modal is hidden
      setTimeout(() => {
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        $(modalId).hide();
      }, 50);
    });

    // Show the modal
    $(modalId).modal('show');
  }

  // Mobile Safari specific modal initialization
  initMobileSafariModal(modalId: string) {
    // Clean up any existing modal states first
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    $('body').css('overflow', '');
    $('body').css('padding-right', '');

    // Remove any existing modal instances
    $('.modal').removeClass('show');
    $('.modal').attr('aria-hidden', 'true');
    $('.modal').hide();

    // Set up mobile Safari-specific event handlers
    $(modalId).off('shown.bs.modal').on('shown.bs.modal', () => {
      // Ensure proper z-index and positioning for mobile Safari
      $(modalId).css('z-index', '1051');
      $('.modal-backdrop').css('z-index', '1040');

      // Mobile Safari specific body handling
      $('body').css('position', 'fixed');
      $('body').css('width', '100%');
    });

    $(modalId).off('hidden.bs.modal').on('hidden.bs.modal', () => {
      // Mobile Safari cleanup when modal is hidden
      setTimeout(() => {
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        $('body').css('overflow', '');
        $('body').css('padding-right', '');
        $('body').css('position', '');
        $('body').css('width', '');
        $(modalId).hide();
      }, 100);
    });

    // Show the modal with a small delay for mobile Safari
    setTimeout(() => {
      $(modalId).modal('show');
    }, 50);
  }

  archiveMessage(groupId) {
    this.commonServ.startLoading();
    this.request.sGROUPID = this.encrDecr.set(groupId.toString());
    this.mgsServ.archiveMessage(this.request).subscribe((p: any) => {
      this.request = {};
      if (p > 0) {
        this.mgsServ.getMessages().subscribe((p: any) => {
          this.listOfMessages = p;
        }, error => { });
        this.commonServ.stopLoading();
      }
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
    });
  }

  closeReadMgs(id, isRead) {
    const modalId = "#readMsg-" + id;

    // Enhanced modal closing with Safari-specific handling
    if (this.isMobileSafari()) {
      // Mobile Safari specific modal closing
      this.closeMobileSafariModal(modalId);
    } else if (this.isOtherMobile()) {
      // Other mobile devices (Android, etc.) - preserve existing mobile functionality
      $(modalId).modal('hide');

      // Remove modal backdrop and reset body
      setTimeout(() => {
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        $('body').css('overflow', '');
        $('body').css('padding-right', '');
        $(modalId).hide();
      }, 100);
    } else if (this.isSafariDesktop()) {
      // Safari desktop specific modal closing
      this.closeSafariModal(modalId);
    } else {
      // Standard modal hide for other desktop browsers
      $(modalId).modal('hide');
    }

    if (!isRead) {
      this.mgsServ.getMessages().subscribe((p: any) => {
        this.listOfMessages = p;
      }, error => { });
    }
  }

  // Safari desktop specific modal closing method
  closeSafariModal(modalId: string) {
    // Immediate modal hide
    $(modalId).modal('hide');

    // Safari-specific cleanup with longer timeout
    setTimeout(() => {
      // Remove any stuck modal backdrops
      $('.modal-backdrop').remove();

      // Reset body classes and styles
      $('body').removeClass('modal-open');
      $('body').css('overflow', '');
      $('body').css('padding-right', '');

      // Force hide the modal element
      $(modalId).hide();
      $(modalId).removeClass('show');
      $(modalId).attr('aria-hidden', 'true');

      // Additional Safari-specific cleanup
      $(modalId).css('display', 'none');

      // Remove any event listeners that might interfere
      $(modalId).off('shown.bs.modal');
      $(modalId).off('hidden.bs.modal');

    }, 200); // Longer timeout for Safari
  }

  // Mobile Safari specific modal closing method
  closeMobileSafariModal(modalId: string) {
    // Immediate modal hide
    $(modalId).modal('hide');

    // Mobile Safari-specific cleanup with enhanced timeout
    setTimeout(() => {
      // Remove any stuck modal backdrops
      $('.modal-backdrop').remove();

      // Reset body classes and styles for mobile Safari
      $('body').removeClass('modal-open');
      $('body').css('overflow', '');
      $('body').css('padding-right', '');
      $('body').css('position', '');
      $('body').css('width', '');

      // Force hide the modal element
      $(modalId).hide();
      $(modalId).removeClass('show');
      $(modalId).attr('aria-hidden', 'true');

      // Additional mobile Safari-specific cleanup
      $(modalId).css('display', 'none');

      // Remove any event listeners that might interfere
      $(modalId).off('shown.bs.modal');
      $(modalId).off('hidden.bs.modal');

      // Additional mobile Safari viewport fixes
      window.scrollTo(0, 0);

    }, 250); // Longer timeout for mobile Safari
  }

  openNotes() {
    this.commonServ.startLoading();
    this.commonServ.getFacilities().subscribe((p: any) => {
      this.listOfFacilities = p;
      $('#sendMessagePop').modal('show');
      this.commonServ.stopLoading();
    }, error => { console.error(error.status); });
  }

}