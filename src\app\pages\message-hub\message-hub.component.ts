import { Component, OnInit } from '@angular/core';
import { MessageHubService } from 'src/app/services/message-hub/message-hub.service';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { ToastrService } from 'ngx-toastr';
declare let $: any;

@Component({
  selector: 'app-message-hub',
  templateUrl: './message-hub.component.html',
  styleUrls: ['./message-hub.component.css']
})
export class MessageHubComponent implements OnInit {
  public listOfMessages: Array<any> = [];
  public listOfFacilities: Array<any> = [];
  public p: number;
  public searchByName: string;
  public request: any = {};
  public device: boolean = false;
  public navigator = navigator;
  constructor(private readonly mgsServ: MessageHubService, private readonly appComp: AppComponent, private readonly commonServ: CommonService
    , private readonly encrDecr: EncrDecrServiceService, private readonly toastr: ToastrService) { }

  ngOnInit() {
    this.device=this.appComp.device;
    this.appComp.loadPageName('Message Hub', 'messageHubTab');
    this.getMessages();
  }

  isMobileDevice(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }

  isSafariDesktop(): boolean {
    const userAgent = navigator.userAgent;
    const isSafari = /Safari/i.test(userAgent) && !/Chrome/i.test(userAgent) && !/Chromium/i.test(userAgent);
    const isDesktop = !this.isMobileDevice();
    return isSafari && isDesktop;
  }

  isMobileSafari(): boolean {
    const userAgent = navigator.userAgent;
    const isSafari = /Safari/i.test(userAgent) && !/Chrome/i.test(userAgent) && !/Chromium/i.test(userAgent);
    const isMobile = /iPhone|iPad|iPod/i.test(userAgent);
    return isSafari && isMobile;
  }

  isOtherMobile(): boolean {
    return this.isMobileDevice() && !this.isMobileSafari();
  }

  getMessages() {
    this.commonServ.startLoading();
    this.mgsServ.getMessages().subscribe((p: any) => {
      this.listOfMessages = p;
      this.commonServ.stopLoading();
    }, error => { this.commonServ.stopLoading(); });
  }

  markAllAsRead() {
    this.commonServ.startLoading();
    this.request.sGROUP_ID = this.encrDecr.set('0');
    this.request.FLAG = this.encrDecr.set('ALL');
    this.mgsServ.markAsAllRead(this.request).subscribe((p: any) => {
      this.request = {};
      this.mgsServ.getMessages().subscribe((p: any) => {
        this.listOfMessages = p;
      }, error => { });
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
    });
  }

  markAsRead(groupId, isRead) {
    if (!isRead) {
      this.request.sGROUP_ID = this.encrDecr.set(groupId);
      this.request.FLAG = this.encrDecr.set('BADGE');
      this.mgsServ.markAsAllRead(this.request).subscribe((p: any) => {
        this.request = {};
      }, error => {
        this.request = {};
      });
    }

    const modalId = "#readMsg-" + groupId;

    // Safari specific modal initialization (both desktop and mobile)
    if (this.isSafariDesktop()) {
      this.initSafariModal(modalId);
    } else if (this.isMobileSafari()) {
      this.initMobileSafariModal(modalId);
    } else {
      // Simple modal opening for all other devices (Android, other mobile browsers, other desktop browsers)
      $(modalId).modal('show');
    }
  }

  // Safari desktop specific modal initialization
  initSafariModal(modalId: string) {
    // Clean up any existing modal states first
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');

    // Set up Safari-specific event handlers
    $(modalId).off('shown.bs.modal').on('shown.bs.modal', () => {
      // Ensure proper z-index and positioning
      $(modalId).css('z-index', '1051');
      $('.modal-backdrop').css('z-index', '1040');
    });

    $(modalId).off('hidden.bs.modal').on('hidden.bs.modal', () => {
      // Additional cleanup when modal is hidden
      setTimeout(() => {
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        $(modalId).hide();
      }, 50);
    });

    // Show the modal
    $(modalId).modal('show');
  }

  // Mobile Safari specific modal initialization
  initMobileSafariModal(modalId: string) {
    // Clean up any existing modal states first
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    $('body').css('overflow', '');
    $('body').css('padding-right', '');

    // Remove any existing modal instances
    $('.modal').removeClass('show');
    $('.modal').attr('aria-hidden', 'true');
    $('.modal').hide();

    // Set up mobile Safari-specific event handlers
    $(modalId).off('shown.bs.modal').on('shown.bs.modal', () => {
      // Ensure proper z-index and positioning for mobile Safari
      $(modalId).css('z-index', '1051');
      $('.modal-backdrop').css('z-index', '1040');

      // Mobile Safari specific body handling
      $('body').css('position', 'fixed');
      $('body').css('width', '100%');
    });

    $(modalId).off('hidden.bs.modal').on('hidden.bs.modal', () => {
      // Mobile Safari cleanup when modal is hidden
      setTimeout(() => {
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        $('body').css('overflow', '');
        $('body').css('padding-right', '');
        $('body').css('position', '');
        $('body').css('width', '');
        $(modalId).hide();
      }, 100);
    });

    // Show the modal with a small delay for mobile Safari
    setTimeout(() => {
      $(modalId).modal('show');
    }, 50);
  }

  archiveMessage(groupId) {
    this.commonServ.startLoading();
    this.request.sGROUPID = this.encrDecr.set(groupId.toString());
    this.mgsServ.archiveMessage(this.request).subscribe((p: any) => {
      this.request = {};
      if (p > 0) {
        this.mgsServ.getMessages().subscribe((p: any) => {
          this.listOfMessages = p;
        }, error => { });
        this.commonServ.stopLoading();
      }
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
    });
  }

  closeReadMgs(id, isRead, event?: Event) {
    const modalId = "#readMsg-" + id;

    // Prevent event bubbling and default behavior
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    // Enhanced modal closing with Safari-specific handling
    if (this.isMobileSafari()) {
      // Mobile Safari specific modal closing
      this.closeMobileSafariModal(modalId);
    } else if (this.isOtherMobile()) {
      // Other mobile devices (Android, etc.) - preserve existing mobile functionality
      this.closeOtherMobileModal(modalId);
    } else if (this.isSafariDesktop()) {
      // Safari desktop specific modal closing
      this.closeSafariModal(modalId);
    } else {
      // Standard modal hide for other desktop browsers
      this.closeStandardModal(modalId);
    }

    if (!isRead) {
      this.mgsServ.getMessages().subscribe((p: any) => {
        this.listOfMessages = p;
      }, error => { });
    }
  }

  // Safari desktop specific modal closing method
  closeSafariModal(modalId: string) {
    // Immediate actions to prevent interaction
    $(modalId).css('pointer-events', 'none');
    $('.modal-backdrop').css('pointer-events', 'none');

    // Force immediate modal hide
    $(modalId).removeClass('show');
    $(modalId).attr('aria-hidden', 'true');
    $(modalId).css('display', 'none');

    // Remove backdrop immediately
    $('.modal-backdrop').remove();

    // Reset body immediately
    $('body').removeClass('modal-open');
    $('body').css('overflow', '');
    $('body').css('padding-right', '');

    // Safari desktop cleanup with multiple attempts
    const cleanupAttempts = [100, 250];

    cleanupAttempts.forEach((delay) => {
      setTimeout(() => {
        // Aggressive cleanup for Safari desktop
        $('.modal-backdrop').remove();
        $(modalId).hide();
        $(modalId).removeClass('show');
        $(modalId).attr('aria-hidden', 'true');
        $(modalId).css('display', 'none');

        // Reset body state
        $('body').removeClass('modal-open');
        $('body').css('overflow', '');
        $('body').css('padding-right', '');

        // Remove any event listeners
        $(modalId).off('shown.bs.modal hidden.bs.modal');

        // Re-enable pointer events
        $(modalId).css('pointer-events', '');
        $('.modal-backdrop').css('pointer-events', '');
      }, delay);
    });
  }

  // Mobile Safari specific modal closing method
  closeMobileSafariModal(modalId: string) {
    // Immediate actions to prevent interaction
    $(modalId).css('pointer-events', 'none');
    $('.modal-backdrop').css('pointer-events', 'none');

    // Force immediate modal hide without animation
    $(modalId).removeClass('show');
    $(modalId).attr('aria-hidden', 'true');
    $(modalId).css('display', 'none');

    // Remove backdrop immediately
    $('.modal-backdrop').remove();

    // Reset body immediately
    $('body').removeClass('modal-open');
    $('body').css({
      'overflow': '',
      'padding-right': '',
      'position': '',
      'width': ''
    });

    // Use multiple cleanup attempts for mobile Safari
    const cleanupAttempts = [50, 150, 300];

    cleanupAttempts.forEach((delay) => {
      setTimeout(() => {
        // Aggressive cleanup for mobile Safari
        $('.modal-backdrop').remove();
        $(modalId).hide();
        $(modalId).removeClass('show');
        $(modalId).attr('aria-hidden', 'true');
        $(modalId).css('display', 'none');

        // Reset body state
        $('body').removeClass('modal-open');
        $('body').css({
          'overflow': '',
          'padding-right': '',
          'position': '',
          'width': ''
        });

        // Remove any event listeners
        $(modalId).off('shown.bs.modal hidden.bs.modal');

        // Force re-enable pointer events
        $(modalId).css('pointer-events', '');
        $('.modal-backdrop').css('pointer-events', '');

        // Mobile Safari viewport fixes
        if (delay === 300) {
          window.scrollTo(0, 0);
          // Force a repaint
          document.body.style.display = 'none';
          document.body.offsetHeight; // Trigger reflow
          document.body.style.display = '';
        }
      }, delay);
    });
  }

  // Standard modal closing method for non-Safari browsers
  closeStandardModal(modalId: string) {
    $(modalId).modal('hide');

    // Standard cleanup
    setTimeout(() => {
      $('.modal-backdrop').remove();
      $('body').removeClass('modal-open');
      $('body').css('overflow', '');
      $('body').css('padding-right', '');
      $(modalId).hide();
    }, 100);
  }

  // Other mobile devices modal closing method (Android, etc.)
  closeOtherMobileModal(modalId: string) {
    $(modalId).modal('hide');

    // Remove modal backdrop and reset body
    setTimeout(() => {
      $('.modal-backdrop').remove();
      $('body').removeClass('modal-open');
      $('body').css('overflow', '');
      $('body').css('padding-right', '');
      $(modalId).hide();
      $(modalId).removeClass('show');
      $(modalId).attr('aria-hidden', 'true');
    }, 100);
  }

  openNotes() {
    this.commonServ.startLoading();
    this.commonServ.getFacilities().subscribe((p: any) => {
      this.listOfFacilities = p;
      $('#sendMessagePop').modal('show');
      this.commonServ.stopLoading();
    }, error => { console.error(error.status); });
  }

}