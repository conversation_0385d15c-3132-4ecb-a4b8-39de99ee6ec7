import { Component, Input } from '@angular/core';
import { BillingService } from 'src/app/services/billing/billing.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { CommonService } from 'src/app/services/common/common.service';
import { BillingComponent } from './billing.component';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-mark-as-all-billed',
  templateUrl: './mark-as-all-billed.component.html',
  styles: []
})
export class MarkAsAllBilledComponent {
  @Input() lisfOfGroupEncounters: Array<any> = [];
  @Input() PatientObject: any;
  public request: any = {};
  public encounterIds: string = "";
  public sendtointegrationSystem = "";
  constructor(private readonly billingServ: BillingService, private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService, private readonly billComp: BillingComponent,
    private readonly toastr: ToastrService) { }

  confirmMarkAsAllBilled(lstEnvs, pObj) {
    let icdorcptcount: boolean = false;
    for (let item of lstEnvs) {
      if (item.listOfCpts.length == 0 || item.listOfIcds.length == 0) {
        icdorcptcount = true;
        break;
      }
    }
    if (icdorcptcount) {
      this.toastr.error('Atleast one CPT/HCPCS and ICD Codes required.', '', { timeOut: 1900 });
    }
    else {
      this.commonServ.startLoading();
      lstEnvs.forEach(x => {
        if (x.status == "0")
          this.encounterIds = this.encounterIds + "," + x.encounteR_ID;
      });

      if (lstEnvs[0].sendtointegrationSystem == null || lstEnvs[0].sendtointegrationSystem == "NO") {
        this.sendtointegrationSystem = "NO";
      }
      else {
        this.sendtointegrationSystem = lstEnvs[0].sendtointegrationSystem;
      }

      this.request.ENCOUNTERIDS = this.encrDecr.set(this.encounterIds);
      this.encounterIds = "";
      this.request.ACCOUNTNO = this.encrDecr.set(pObj.account_Number);
      this.request.IsEntsSendtoInteSystem = this.encrDecr.set(this.sendtointegrationSystem);
      this.request.strGroupID = this.encrDecr.set(lstEnvs[0].group_Id);
      this.billingServ.markAllAsBilled(this.request).subscribe((p: any) => {
        this.request = {};
        if (p > 0) {
          this.billComp.refreshEncountersByPatient(pObj, '');
        }
        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
    }
  }

}
