import { Component, OnInit } from '@angular/core';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { UserManagementService } from 'src/app/services/user-management/userManagement.service';

@Component({
  selector: 'app-add-modify-facility',
  templateUrl: './add-modify-facility.component.html',
  styles: []
})
export class AddModifyFacilityComponent implements OnInit {
  public listOfFacilities: Array<any> = [];
  public p: number;
  public search = '';
  public searchIsPrimeHospital = '';
  public searchIsCoordinatorApproval = '';
  public searchIsAuditorApproval = '';
  public searchIsAdmissionDateEdit = '';
  public request: any = {};
  public facilityObj: any = {};

  constructor(private readonly appComp: AppComponent, private readonly userSer: UserManagementService, private readonly commonServ: CommonService) { }

  ngOnInit(): void {
    this.appComp.loadPageName('Add/Modify Facility', 'usermanagementTab');
    this.fetchFacilities();
  }

  fetchFacilities() {
    this.commonServ.startLoading();
    this.userSer.FetchFacilities().subscribe((p: any) => {
      this.listOfFacilities = p.facilityInfos;
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  openEditpopup(item) {

    this.facilityObj = item;
  }

  openAddpopup() {
    this.facilityObj = {
      facilityName: '', facilityID: 0, timE_ZONE_NAME: '', city: '', statE_NAME: '', iS_PRIME: '', isPrimeHospital: false,
      coordinatorAppRequired: false,
      isAuditRequired: false,
      isAdissionDateEdit: false
    }
  }


}
