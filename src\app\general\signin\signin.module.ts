import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SigninComponent } from './signin.component';
import { SigninRoutingModule } from './signin-routing.module';
import {CarouselModule}  from 'ngx-bootstrap/carousel';

@NgModule({
  imports: [
    CommonModule,
    SigninRoutingModule,
    CarouselModule.forRoot()
  ],
  exports: [
    SigninComponent
  ],
  declarations: [
    SigninComponent
  ],
  providers: [
  ],
})
export class SigninModule { }
