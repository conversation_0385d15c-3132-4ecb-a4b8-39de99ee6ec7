<div *ngFor="let mainItem of lisfOfEncounters; let i= index;" id="accordion-{{i}}" class="p-2 bg-card{{i}} mb-2">
    <div class="row mx-auto">
        <div class="col-12 col-md-6 px-0">
            <h6 class="font-weight-bold text-dark font-size-large">{{mainItem.groupName}}
                <a class="deletepointer"
                    *ngIf="!(groupAlerts.rule1Status&&groupAlerts.rule2Status&&groupAlerts.rule3Status&&groupAlerts.rule4Status) && (mainItem.listOfEncounters[0].isAlertCheck)"
                    (click)="getAlerts(PatientObject,mainItem.groupName,mainItem.listOfEncounters)">
                    <img alt=' ' src="../../../assets/img/alerts.gif" width="20px" height="20px" class="mx-1" />
                </a>
            </h6>
        </div>
        <div class="col-12 col-md-6 ml-auto text-right">
            <a *ngIf="mainItem.marAsUnApproved>0&&mainItem.allCount>1&&mainItem.coordinatorApprovalNotRequired==0"
                class="mx-1" (click)="markAsAllApproved(mainItem.listOfEncounters,PatientObject)">
                <img alt=' ' src="../../../assets/img/markpin.png" title="Mark all as Approved" width="26px"
                    data-target="#markAsAllApproved" data-toggle="modal">
            </a>
        </div>
    </div>
    <div class="card" *ngFor="let item of mainItem.listOfEncounters">
        <ng-container *ngIf="item.encounteR_ID!=''">
            <div class="card-header-new py-0 px-0 bill-inner position-relative" id="billing1-{{item.encounteR_ID}}"
                style="text-decoration: line-through;">
                <h5 class="mb-0">
                    <button class="btn btn-link bill-arrow text-white  collapsed align-items-center d-flex float-left"
                        data-toggle="collapse" attr.data-target="#bills-{{item.encounteR_ID}}" aria-expanded="true"
                        attr.aria-controls="bills-{{i}}">

                        <span class="pl-md-4" [class.strike]="item.status=='1'"><i>Encounter created at:</i>
                            {{item.encounterseendate}}--<span class="small">
                                <i>By <span class="text-color-green">{{item.posT_TO_BILLED_BY}}</span></i></span></span>
                        <span class="ml-1">[</span>
                        <div tooltip="{{convertCPTCodes(item.listOfCpts)}}" triggers="mouseenter mouseleave click"
                            class="w-fit-content">
                            <span>{{getTwoCPTCodes(item.listOfCpts)}}</span>
                        </div>
                        <span>]</span>
                        <span class="ml-2"  style="color:#b2d6cd;font-size:12px"
                            *ngIf="item.lastmodifiedby&&item.lastmodifieddate">* Modified By :{{item.lastmodifiedby}} on
                            {{item.lastmodifieddate}}</span>
                    </button>
                    <div class="dropdown no-arrow pl-3 py-0 pr-0 position-absolute no_top_right bill-inner">

                        <ng-container *ngIf="item.coordinator_Approval_Required ==1 && item.status =='0'">
                            <a *ngIf="item.coordinatorApproved ==0;else AldApp"
                                (click)="markAsApprovedUnApprovePopup(item)" class="mr-1" data-toggle="modal"
                                data-target="#confirmMarkAsApproved" title="Mark As Approved"><i
                                    class="far fa-2x fa-check-square text-light"></i></a>
                            <ng-template #AldApp>
                                <a class="mr-1" data-target="#confirmMarkAsUnApproved"
                                    (click)="markAsApprovedUnApprovePopup(item)" data-toggle="modal"
                                    title="Mark As UnApproved"><i class="fas fa-2x fa-check-square text-danger"></i></a>
                            </ng-template>
                        </ng-container>
                        <a *ngIf="item.status !='1' && mainItem.listOfEncounters.length > 1" data-dismiss="modal"
                            (click)="submitICDsMultiEcounters(item,item.encounteR_ID,PatientObject,mainItem.listOfEncounters)"
                            title="Replace ICD's for All Encounters" class="mr-1"><i
                                class="fas fa-window-restore fa-1x  p-1 text-white small" style="height:22px"></i></a>

                        <span (keyup)="saveChanges(item,PatientObject)" (click)="saveChanges(item,PatientObject)"
                            style="display:none" class="btnSave{{item.encounteR_ID}} mx-1 edit-ancor">
                            <img alt=' ' src="../../../assets/img/save.png" title="Save Edit Charges"></span>

                        <a class="dropdown-toggle" id="dropdownMenuLink-{{item.encounteR_ID}}" data-toggle="dropdown"
                            aria-haspopup="true" aria-expanded="false">
                            <i class="fa-ellipsis-v fa-2x fas fa-fw fa-sm fas pt-1 text-white-50"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated-fade-in"
                            attr.aria-labelledby="dropdownMenuLink-{{item.encounteR_ID}}">
                            <a class="dropdown-item deletepointer" *ngIf="item.status =='0'"
                                data-target="#updateSeenDate" data-toggle="modal"
                                (click)="editEncounterSeenDate(item,PatientObject)">Edit Encounter Seen Date</a>

                            <a class="dropdown-item deletepointer" data-toggle="modal" data-target="#viewHistory"
                                (click)="viewHistory(item.encounteR_ID)">View Edit History</a>
                            <a class="dropdown-item deletepointer" *ngIf="item.status =='0'"
                                (click)="deleteEncounter(item)" data-toggle="modal"
                                data-target="#deleteEncounter">Delete Encounter</a>
                        </div>
                    </div>
                </h5>
            </div>

            <div id="bills-{{item.encounteR_ID}}"
                [ngClass]="{'show isFirstClass' :item.encounteR_ID==firstEcounterIdToOpen}" class="collapse"
                attr.aria-labelledby="billing1-{{item.encounteR_ID}}" attr.data-parent="#accordion-{{i}}">
                <div class="card-body p-2 p-md-4">
                    <!-- inner accordion 1-->
                    <div id="accordion_inner-{{item.encounteR_ID}}" class="mb-2">
                        <div class="card">
                            <div class="card-header-new py-0 px-0 dan-blue" id="billing1-{{item.encounteR_ID}}">
                                <h5 class="mb-0 position-relative">
                                    <button
                                        class="btn btn-link bill-arrow collapsed align-items-center d-flex text-white"
                                        data-toggle="collapse" attr.data-target="#bills_inner-{{item.encounteR_ID}}"
                                        aria-expanded="true" attr.aria-controls="bills_inner-{{item.encounteR_ID}}">
                                        <span class="pl-4">CPT/HCPCS Codes</span>
                                    </button>
                                    <a *ngIf="item.status=='0'" (click)="getCPTData(item,item.physicianmailid,'Add')"
                                        class="float-right position-absolute top-right-10 blld-{{item.encounteR_ID}}"
                                        data-toggle="modal" data-target="#CPTData"><i
                                            class="fas fa-plus-circle fa-2x "></i></a>
                                </h5>
                            </div>

                            <div id="bills_inner-{{item.encounteR_ID}}"
                                [ngClass]="{'show isFirstClass' :item.encounteR_ID==firstEcounterIdToOpen}"
                                class="collapse" attr.aria-labelledby="bills_inner-{{item.encounteR_ID}}"
                                attr.data-parent="#accordion_inner-{{item.encounteR_ID}}">
                                <div class="card-body" id="cptDiv-{{item.encounteR_ID}}">
                                    <div class="row" *ngFor="let cpt of item.listOfCpts">
                                        <div *ngIf="item.status=='0'">
                                            <span class="mx-1 edit-ancor" data-toggle="modal" data-target="#CPTData"
                                                (keyup)="getCPTData(item,item.physicianmailid,cpt)"
                                                (click)="getCPTData(item,item.physicianmailid,cpt)">
                                                <i class="fas fa-edit" title="Edit"></i>
                                            </span>
                                            <span class="mx-1 edit-ancor" (keyup)="deleteCPTCode(item,cpt)"
                                                (click)="deleteCPTCode(item,cpt)">
                                                <i class="far fa-trash-alt" title="Remove"></i>
                                            </span>
                                        </div>
                                        <div class="col-9">
                                            <span class="cpt-colors" [class.strike]="item.status=='1'">{{cpt}}</span>
                                        </div>
                                        <div *ngIf="item.status=='0'">
                                            <a href="#" class="mx-1" data-toggle="modal" data-target="#ModifierData"
                                                (click)="getModifierData(item,cpt)">
                                                Add/Modify Modifiers</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- inner accordion end 1-->
                    <!-- inner accordion 2-->
                    <div id="accordion_inner1-{{item.encounteR_ID}}" class="mb-2">
                        <div class="card">
                            <div class="card-header-new py-0 px-0" id="billing2-{{item.encounteR_ID}}">
                                <h5 class="mb-0 position-relative dan-blue">
                                    <button
                                        class="btn btn-link bill-arrow  collapsed align-items-center d-flex text-white"
                                        data-toggle="collapse" attr.data-target="#bills_inner1-{{item.encounteR_ID}}"
                                        aria-expanded="true" attr.aria-controls="bills_inner1-{{item.encounteR_ID}}">
                                        <span class="pl-4">ICD Codes</span>
                                    </button>
                                    <a *ngIf="item.status=='0'" (click)="getICDData(item,item.physicianmailid,'Add')"
                                        class="float-right position-absolute top-right-10 blld-{{item.encounteR_ID}}"
                                        data-toggle="modal" data-target="#ICDData"><i
                                            class="fas fa-plus-circle fa-2x "></i></a>

                                </h5>
                            </div>

                            <div id="bills_inner1-{{item.encounteR_ID}}"
                                [ngClass]="{'show isFirstClass' :item.encounteR_ID==firstEcounterIdToOpen}"
                                class="collapse" attr.aria-labelledby="bills_inner1-{{item.encounteR_ID}}"
                                attr.data-parent="#accordion_inner1-{{item.encounteR_ID}}">
                                <div class="card-body col-11 float-left">
                                    <div class="selectrow" *ngFor="let icd of item.listOfIcds;let i = index"
                                        [class.active]="i == HighlightRow">
                                            <div *ngIf="item.status=='0'" class="blld-{{item.encounteR_ID}}">
                                                <span class="mx-1 edit-ancor" data-toggle="modal" data-target="#ICDData"
                                                    (keyup)="getICDData(item,item.physicianmailid,icd)"
                                                    (click)="getICDData(item,item.physicianmailid,icd)">
                                                    <i class="fas fa-edit" title="Edit"></i>
                                                </span>
                                                <span class="mx-1 edit-ancor" (keyup)="deleteICDCode(item,icd)"
                                                    (click)="deleteICDCode(item,icd)">
                                                    <i class="far fa-trash-alt" title="Remove"></i>
                                                </span>
                                            </div>
                                            <div class="col-8">
                                                <span class="cpt-colors"
                                                    [class.strike]="item.status=='1'" (keyup)="ClickedRow(i)" (click)="ClickedRow(i)">{{icd}}</span>
                                            </div>
                                    </div>

                                </div>
                                <div class="card-body col-1 float-lg-left text-center vertical-align-middle">
                                    <img id="up" [src]='img1' alt="uparrow" (keyup)="moveSelectedUp(item)"
                                        (click)="moveSelectedUp(item)" title="Up" class="cursor-pointer pr-1 up-arrow">
                                    <img id="down" [src]='img2' alt="downarrow" (keyup)="moveSelectedDown(item)"
                                        (click)="moveSelectedDown(item)" title="Down" class="cursor-pointer down-arrow">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- inner accordion end 2-->
                </div>

            </div>
            <div class="modal fade" id="confirmMarkAsApproved" style="z-index: 1500;" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header py-2">
                            <h5 class="modal-title" id="exampleModalCenterTitle">Mark As Approved</h5>
                        </div>
                        <div class="modal-body">
                            <div class="row mx-0 align-items-center">
                                <div class="col-12 text-center" style="color: black; font-size:medium;">
                                    Are you sure want to mark as approved?
                                </div>
                                <div class="col-12 text-center mt-4">
                                    <button class="btn btn-outline-info mr-3" data-dismiss="modal"
                                        (click)="markAsApproved(PatientObject)">Yes</button>
                                    <button class="btn btn-outline-info" data-dismiss="modal">No</button>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer py-2">
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal fade" id="confirmMarkAsUnApproved" style="z-index: 1500;" tabindex="-1"
                aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header py-2">
                            <h5 class="modal-title" id="exampleModalCenterTitle">Mark As Un Approved</h5>
                        </div>
                        <div class="modal-body">
                            <div class="row mx-0 align-items-center">
                                <div class="col-12 text-center" style="color: black; font-size:medium;">
                                    Are you sure want to mark as Unapproved?
                                </div>
                                <div class="col-12 text-center mt-4">
                                    <button class="btn btn-outline-info mr-3" data-dismiss="modal"
                                        (click)="markAsUnApproved(PatientObject)">Yes</button>
                                    <button class="btn btn-outline-info" data-dismiss="modal">No</button>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer py-2">
                        </div>
                    </div>
                </div>
            </div>
        </ng-container>
    </div>

</div>


<app-replace-icdcodes [listOfAllEncounters]="lisfOfAllEncounters" [listOfIcds]="listOfIcds"
    [listOfRemovedCpts]="listOfRemovedCpts" [listOfRemovedIcds]="listOfRemovedIcds" [PatientObject]="PatientObject"
    [encounterObj]="encounterObj" (eventGetEncountersByPatient)="getEncountersByPatient(PatientObject)"
    [userType]="'Coordinator'"></app-replace-icdcodes>

<!-- CPT Codes popup starts -->
<app-cpt-code [lisfOfCPTData]="lisfOfCPTData" [cptType]="cptType" [encounterObj]="encounterObj"
    (eventListOfRemovedCpts)="updateRemovedCptList(encounterObj)" (eventUpdateUpdatedCptDataList)="updateUpdatedCptDataList(encounterObj)"></app-cpt-code>
<!-- CPT Codes popup ends -->

<!-- ICD Codes popup starts -->
<app-icd-code [lisfOfICDData]="lisfOfICDData" [icdType]="icdType" [encounterObj]="encounterObj"
    (eventListOfRemovedIcds)="updateRemovedIcdList(encounterObj)" (eventUpdateUpdatedICDDataList)="updateUpdatedICDDataList(encounterObj)"></app-icd-code>
<!-- ICD Codes popup end -->

<!-- Cpt Modifier popup starts -->
<app-cpt-modifiers [listOfModifier]="listOfModifier" [encounterObj]="encounterObj"
    [cptType]="cptType"></app-cpt-modifiers>
<!-- Cpt Modifier popup end -->

<!-- Cpt Modifier popup starts -->
<app-remove-reasons-cptcomponent [lisfOfCPTData]="lisfOfCPTData" [cptCode]="cptCode"
    (eventRemoveReason)="updateDeleteReason($event)"
    (eventCancelRemoveReason)="updateCancelDeleteReason($event)"></app-remove-reasons-cptcomponent>
<!-- Cpt Modifier popup end -->