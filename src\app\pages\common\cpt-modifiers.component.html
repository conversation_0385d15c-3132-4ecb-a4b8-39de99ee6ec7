<div class="modal fade" id="ModifierData" tabindex="-1" aria-labelledby="exampleModalCenterTitle" maria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title" id="exampleModalCenterTitle">Assign Modifiers</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- search start -->

                <div class="row">
                    <div class="col-12">
                        <div class="card px-3 py-2 shadow tab-content" id="nav-tabContent1">

                            <div class="tab-pane fade show active scrolling" style="height:300px;overflow-x:scroll;"
                                id="nav-fav1">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="input-group">
                                            <input type="text" class="form-control small" maxlength="1000"
                                                placeholder="Search Here.." aria-label="Search"
                                                [(ngModel)]="searchModfier" [ngModelOptions]="{standalone: true}"
                                                aria-describedby="basic-addon2">
                                            <div class="input-group-append">
                                                <button class="bg-gradient-info btn text-white" type="button">
                                                    <i class="fas fa-search fa-sm"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12 my-2"
                                        *ngFor="let item of listOfModifier |gridFilter:{modifiersname:searchModfier}:false">
                                        <div class="custom-control custom-checkbox">
                                            <input *ngIf="item.isExist" checked type="checkbox"
                                                class="custom-control-input" id="{{item.modifierS_ID}}"
                                                value="{{item.modifiersname}}"
                                                (change)="chkChangeEvent(item.modifiersname, $event)">
                                            <input *ngIf="!item.isExist" type="checkbox" class="custom-control-input"
                                                id="{{item.modifierS_ID}}" value="{{item.modifiersname}}"
                                                (change)="chkChangeEvent(item.modifiersname, $event)">
                                            <label class="custom-control-label"
                                                for="{{item.modifierS_ID}}">{{item.modifiersname}}</label>
                                        </div>
                                    </div>

                                </div>

                            </div>

                        </div>
                        <!-- tabs end -->

                    </div>
                </div>

                <!-- search end -->
            </div>
            <div class="modal-footer py-2">
                <button class="btn btn-outline-info float-right" data-dismiss="modal"
                    (click)="addModifiers(encounterObj,cptType)">Save</button>
            </div>
        </div>
    </div>
</div>