<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new p-2" style="background: #0169ab;">
                    <div class="align-items-center row mx-auto">
                        <div class="col-12 px-0">
                            <div class="row my-1 mx-auto">
                                <div class="col-12 px-1 py-1"
                                    [ngClass]="{'col-md-3': this.listOfReports.length, 'col-md-4': !this.listOfReports.length}">
                                    <select id="ddlFacility" class="form-control"
                                        (change)="getAlertReportData($any($event.target).value)">
                                        <option value="">---Select Facility---</option>
                                        <option value="All">All Facilities</option>
                                        <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                                            <span>{{s.facilityName}}</span>
                                        </option>
                                    </select>
                                </div>
                                <div *ngIf="this.listOfReports.length>0" class="col-12 col-md-1 px-1 py-1 text-center">
                                    <img alt=' ' (keyup)="exportAsXLSX()" (click)="exportAsXLSX()" class="ml-2 shadow"
                                        [src]="img1" style="width:40px;cursor:pointer;" title="Export Excel">
                                </div>
                                <div class="col-12 col-md-8 px-1 py-1">
                                    <div class="input-group">
                                        <input type="text" class="form-control small" maxlength="1000"
                                            placeholder="Search for..." aria-label="Search"
                                            aria-describedby="basic-addon2" [(ngModel)]="searchByName"
                                            [ngModelOptions]="{standalone: true}">
                                        <div class="input-group-append">
                                            <button class="bg-gradient-light btn text-dark" type="button">
                                                <i class="fas fa-search fa-sm"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Card Body -->
                <div class="card-body px-2 py-1">
                    <div class="row mx-0">
                        <div class="col-12 px-0">
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered table-hover my-auto">
                                    <tr>
                                        <th>Account No#</th>
                                        <th>Patient Name</th>
                                        <th>MRN</th>
                                        <th>Facility Name</th>
                                        <th>Rule 1 Alert</th>
                                        <th>Rule 2 Alert</th>
                                        <th>Rule 3 Alert</th>
                                        <th>Rule 4 Alert</th>
                                    </tr>
                                    <tr
                                        *ngFor="let item of listOfReports|gridFilter:{patientaccountnumber:searchByName,patient_Name:searchByName,patient_MRN:searchByName,facility:searchByName,rule1Alert:searchByName,rule2Alert:searchByName,rule3Alert:searchByName,rule4Alert:searchByName,alertStatus:searchByName}:false| paginate: { itemsPerPage: 15, currentPage: p}">
                                        <td>{{item.patientaccountnumber}}</td>
                                        <td>{{item.patient_Name}}</td>
                                        <td>{{item.patient_MRN}}</td>
                                        <td>{{item.facility}}</td>
                                        <td>{{item.rule1Alert}}</td>
                                        <td>{{item.rule2Alert}}</td>
                                        <td>{{item.rule3Alert}}</td>
                                        <td>{{item.rule4Alert}}</td>
                                    </tr>
                                    <tr *ngIf="submitted&&listOfReports.length==0">
                                        <td class="text-center" colspan="7">This facility hasn't been configured for
                                            Alerts!!</td>
                                    </tr>
                                    <tr>
                                        <td colspan="10" class="m-0 p-0" style="background: white !important;">
                                            <pagination-controls previousLabel="" nextLabel=""
                                                (pageChange)="p = $event"></pagination-controls>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                </div>

            </div>
        </div>
        <!-- Area Chart Ends -->
    </div>
</div>
<!-- Begin Page Content Ends -->