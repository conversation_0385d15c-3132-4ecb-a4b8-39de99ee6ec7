import { Component, OnInit } from '@angular/core';
import { CommonService } from 'src/app/services/common/common.service';
import { ReportsService } from 'src/app/services/reports/reports.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { AppComponent } from 'src/app/app.component';
import { ExcelServices } from 'src/app/services/common/ExcelService';
import { ToastrService } from 'ngx-toastr';
declare let $: any;

@Component({
  selector: 'app-billed-encounter-report',
  templateUrl: './billed-encounter-report.component.html',
  styles: []
})
export class BilledEncounterReportComponent implements OnInit {
  public listOfFacilities: Array<any> = [];
  public listOfPhysicians: Array<any> = [];
  public listOfReports: Array<any> = [];
  public listOfReportsInitial: Array<any> = [];
  public FilterForm: FormGroup;
  public request: any = {};
  public submitted: boolean = false;
  public p: number;
  public searchByName: string;
  public selectedYear: string;
  public lastRefreshDate: string;
  public isFaiclityAccess: boolean = true;
  public listOfYears: Array<any> = [];
  public listOfMissMatchedEncountersByKareo: Array<any> = [];
  public selectedResonArray: Array<string> = [];
  public selectedRequest: any = {};
  public userAccess: any = {};
  public listOfMissMatchedEncountersByGR: Array<any> = [];
  public listOfEcountinKareo: Array<any> = [];
  orderBy = 'desc';
  orderByFacilityName = 'desc';
  orderByPhysician_name = 'desc';
  sortColumnBy = 'physician_name';
  public timeout: any = null;
  public encounterIDbyUser: any;
  public img1: string = '.././../../assets/img/excel.png';
  constructor(private readonly reportsServ: ReportsService, private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService,
    private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly excelService: ExcelServices, private readonly toastr: ToastrService) { }

  ngOnInit() {
    this.getYears();
    let currentYear = new Date().getFullYear();
    this.appComp.loadPageName('Kareo Encounters Report', 'reportsTab');
    this.selectedYear = `${currentYear}`;
    this.getLastCPTDataRefreshDate();
    this.getFacilities();
    this.FilterForm = this.fb.group({
      ddlFacility: ['All', Validators.required],
      ddlPhysician: ['All', Validators.required],
      ddlYear: [`${currentYear}`]
    });
    this.userAccess = this.appComp.userAccess;
    this.request.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.PhysicanName = this.encrDecr.set($('#ddlPhysician option:selected').text().trim());
    this.request.sYearvalue = this.encrDecr.set(this.FilterForm.value.ddlYear);
    this.request.DisplayType = this.encrDecr.set('View');
    this.request.CPTValue = this.encrDecr.set('');
    this.getReportBySelection(this.request);
  }
  getYears() {
    let max = new Date().getFullYear(),
      min = max - 10;
    let years: any[] = [];
    for (let i = min; i <= max; i++) {

      years.push(i);
    }
    this.listOfYears = years;
  }
  get f() { return this.FilterForm.controls; }

  getFacilities() {
    this.commonServ.getFacilitiesForKareo().subscribe((p: any) => {
      this.listOfFacilities = p;
    }, error => { console.error(error.status); });
  }

  getPhysiciansByFacility(facillity) {
    this.commonServ.startLoading();
    this.commonServ.getPhysiciansByFacilityForReports(facillity).subscribe((p: any) => {
      this.commonServ.stopLoading();
      this.listOfPhysicians = p;
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  exportAsXLSX(): void {
    let download = confirm("Do you want download?");

    if (download) {
      let fileName = "";
      fileName = "BILLED_REPORT_" + this.FilterForm.value.ddlFacility + '_' + $('#ddlPhysician option:selected').text() + '_' + $('#ddlYear option:selected').text();
      this.excelService.exportAsExcelFile(this.listOfReports, fileName);
    }
  }

  filterReport() {
    this.submitted = true;
    if (this.FilterForm.invalid) {
      return;
    }
    this.request.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.PhysicanName = this.encrDecr.set($('#ddlPhysician option:selected').text().trim());
    this.request.sYearvalue = this.encrDecr.set(this.FilterForm.value.ddlYear);
    this.request.DisplayType = this.encrDecr.set('View');
    this.request.CPTValue = this.encrDecr.set('');
    this.getReportBySelection(this.request);
  }

  getReportBySelection(request) {
    this.commonServ.startLoading();
    this.selectedYear = this.FilterForm.value.ddlYear;
    this.reportsServ.getEncounterStatusReportDetails(request).subscribe((p: any) => {
      if (p.listofData.length > 0) {
        this.listOfReports = p.listofData;
        this.listOfReportsInitial = p.listofData;
        this.listOfReports.forEach(report => {
          let ajan: number = 0;
          let ajanlnk: string = '';
          ajan = report.avjan.slice(0, -1);
          report.ajan = ajan;
          ajanlnk = report.avjan.slice(report.avjan.length - 1);
          report.janColorLink = ajanlnk;

          let afeb: number = 0;
          let afeblnk: string = '';
          afeb = report.avfeb.slice(0, -1);
          report.afeb = afeb;
          afeblnk = report.avfeb.slice(report.avfeb.length - 1);
          report.febColorLink = afeblnk;

          let amarch: number = 0;
          let amarchlnk: string = '';
          amarch = report.avmarch.slice(0, -1);
          report.amarch = amarch;
          amarchlnk = report.avmarch.slice(report.avmarch.length - 1);
          report.marchColorLink = amarchlnk;

          let aapril: number = 0;
          let aaprillnk: string = '';
          aapril = report.avapril.slice(0, -1);
          report.aapril = aapril;
          aaprillnk = report.avapril.slice(report.avapril.length - 1);
          report.aprilColorLink = aaprillnk;

          let amay: number = 0;
          let amaylnk: string = '';
          amay = report.avmay.slice(0, -1);
          report.amay = amay;
          amaylnk = report.avmay.slice(report.avmay.length - 1);
          report.mayColorLink = amaylnk;

          let ajune: number = 0;
          let ajunelnk: string = '';
          ajune = report.avjune.slice(0, -1);
          report.ajune = ajune;
          ajunelnk = report.avjune.slice(report.avjune.length - 1);
          report.juneColorLink = ajunelnk;

          let ajuly: number = 0;
          let ajulylnk: string = '';
          ajuly = report.avjuly.slice(0, -1);
          report.ajuly = ajuly;
          ajulylnk = report.avjuly.slice(report.avjuly.length - 1);
          report.julyColorLink = ajulylnk;

          let aaug: number = 0;
          let aauglnk: string = '';
          aaug = report.avaug.slice(0, -1);
          report.aaug = aaug;
          aauglnk = report.avaug.slice(report.avaug.length - 1);
          report.augColorLink = aauglnk;

          let asep: number = 0;
          let aseplnk: string = '';
          asep = report.avsep.slice(0, -1);
          report.asep = asep;
          aseplnk = report.avsep.slice(report.avsep.length - 1);
          report.sepColorLink = aseplnk;

          let aoct: number = 0;
          let aoctlnk: string = '';
          aoct = report.avoct.slice(0, -1);
          report.aoct = aoct;
          aoctlnk = report.avoct.slice(report.avoct.length - 1);
          report.octColorLink = aoctlnk;

          let anov: number = 0;
          let anovlnk: string = '';
          anov = report.avnov.slice(0, -1);
          report.anov = anov;
          anovlnk = report.avnov.slice(report.avnov.length - 1);
          report.novColorLink = anovlnk;

          let adec: number = 0;
          let adeclnk: string = '';
          adec = report.avdec.slice(0, -1);
          report.adec = adec;
          adeclnk = report.avdec.slice(report.avdec.length - 1);
          report.decColorLink = adeclnk;

          report.janColor = ajan < report.jan ? 'unapproved_count' : 'approved_count';
          report.febColor = afeb < report.feb ? 'unapproved_count' : 'approved_count';
          report.marchColor = amarch < report.march ? 'unapproved_count' : 'approved_count';
          report.aprilColor = aapril < report.april ? 'unapproved_count' : 'approved_count';
          report.mayColor = amay < report.may ? 'unapproved_count' : 'approved_count';
          report.juneColor = ajune < report.june ? 'unapproved_count' : 'approved_count';
          report.julyColor = ajuly < report.july ? 'unapproved_count' : 'approved_count';
          report.augColor = aaug < report.aug ? 'unapproved_count' : 'approved_count';
          report.sepColor = asep < report.sep ? 'unapproved_count' : 'approved_count';
          report.octColor = aoct < report.oct ? 'unapproved_count' : 'approved_count';
          report.novColor = anov < report.nov ? 'unapproved_count' : 'approved_count';
          report.decColor = adec < report.dec ? 'unapproved_count' : 'approved_count';
        });
      }
      else {
        this.listOfReports = [];
      }
      this.isFaiclityAccess = p.flag;
      this.request = {};
      this.commonServ.stopLoading();
    }, error => {
      this.commonServ.stopLoading();

    });
  }

  getLastCPTDataRefreshDate() {
    this.reportsServ.getLastCPTDataRefreshDate().subscribe((p: any) => {
      this.lastRefreshDate = p;
    }, error => { console.error(error.status); });
  }

  getMissMatchedEncountersByKareo(item, year, month) {
    this.commonServ.startLoading();
    let request = {
      strYear: this.encrDecr.set(year),
      strMonth: this.encrDecr.set(month),
      Facility: this.encrDecr.set(item.facilityName),
      Physician: this.encrDecr.set(item.physician_name)
    }
    this.selectedRequest = request;
    this.reportsServ.getMissMatchedEncountersByKareo(request).subscribe((p: any) => {
      this.commonServ.stopLoading();
      this.listOfMissMatchedEncountersByKareo = p;
      $("#mdlMissKEnvs").modal('show');
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  updateReasonCodeKareoEncounter(item) {
    if (this.selectedResonArray[item.encounterId] && this.encounterIDbyUser) {
      this.commonServ.startLoading();
      let request = {
        AccountNumber: this.encrDecr.set(item.accountNumber),
        PatientName: this.encrDecr.set(item.patientName),
        FacilityName: this.encrDecr.set(item.facilityName),
        PhysicianName: this.encrDecr.set(item.physicianName),
        strKareoPatientID: this.encrDecr.set(item.kareoPatientID),
        strEncounterId: this.encrDecr.set(item.encounterId),
        strEncounterCreatedDate: this.encrDecr.set(item.encounterCreatedDate),
        ReasonCode: this.encrDecr.set(this.selectedResonArray[item.encounterId]),
        CPTCode: this.encrDecr.set(item.cptCode),
        strKareoEncounterID: this.encrDecr.set(item.kareoEncounterID),
        strEncounterSeenDate: this.encrDecr.set(item.encounterSeenDate),
        strAdmissionDate: this.encrDecr.set(item.admissionDate),
        EncounterIDEnteredByUser: this.encrDecr.set(this.encounterIDbyUser)
      }
      this.reportsServ.updateReasonCodeKareoEncounter(request).subscribe((p: any) => {
        this.request.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
        this.request.PhysicanName = this.encrDecr.set($('#ddlPhysician option:selected').text().trim());
        this.request.sYearvalue = this.encrDecr.set(this.FilterForm.value.ddlYear);
        this.request.DisplayType = this.encrDecr.set('View');
        this.request.CPTValue = this.encrDecr.set('');
        this.encounterIDbyUser = 0;
        this.getReportBySelection(this.request);
        this.reportsServ.getMissMatchedEncountersByKareo(this.selectedRequest).subscribe((p: any) => {
          this.listOfMissMatchedEncountersByKareo = p;
          this.commonServ.stopLoading();
        }, error => {
          this.commonServ.stopLoading();
        });
      }, error => {
        this.commonServ.stopLoading();
      });
    }
    else if (!this.selectedResonArray[item.encounterId]) {
      this.toastr.error('Please select Reson to submit.', '', { timeOut: 1900 });
    }
    else if (!this.encounterIDbyUser) {
      this.toastr.error('Please Enter the Encounter ID.', '', { timeOut: 1900 });

    }
    else {
      this.toastr.error('Something went wrong!', '', { timeOut: 1900 });
    }
  }

  getMissMatchedEncountersByGR(item, year, month) {

    this.commonServ.startLoading();
    let request = {
      strYear: this.encrDecr.set(year),
      strMonth: this.encrDecr.set(month),
      Facility: this.encrDecr.set(item.facilityName),
      Physician: this.encrDecr.set(item.physician_name)
    }
    this.reportsServ.getMissMatchedEncountersByGR(request).subscribe((p: any) => {
      this.commonServ.stopLoading();
      this.listOfMissMatchedEncountersByGR = p;
      $("#mdlMissKEnvsByGR").modal('show');
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  modelClose() {
    this.selectedRequest = null;
  }

  sortColumn(columnBy) {
    this.sortColumnBy = columnBy;

    if (columnBy == 'facilityName') {
      this.orderByFacilityName = this.orderByFacilityName == 'asc' ? 'desc' : 'asc';
      this.orderBy = this.orderByFacilityName;
    }
    else if (columnBy == 'physician_name') {
      this.orderByPhysician_name = this.orderByPhysician_name == 'asc' ? 'desc' : 'asc';
      this.orderBy = this.orderByPhysician_name;
    }

    this.listOfReports = this.sortOrderBy(this.listOfReports, columnBy, this.orderBy)
  }

  sortOrderBy(list, sortBy, sortOrder) {
    let newArray = [];
    this.p = 1;
    if (sortOrder == 'asc') {
      newArray = list.sort(
        function (a, b) {
          let sortNest: number = (b[sortBy] > a[sortBy]) ? -1 : 0;
          let sort: number = (a[sortBy] > b[sortBy]) ? 1 : sortNest;
          return sort;
        }
      );
    }
    else if (sortOrder == 'desc') {
      newArray = list.sort(
        function (a, b) {
          let sortNest: number = (b[sortBy] > a[sortBy]) ? 1 : 0;
          let sort: number = (a[sortBy] > b[sortBy]) ? -1 : sortNest;
          return sort;
        });
    }
    return newArray;
  }

  search(searchKey) {
    clearTimeout(this.timeout);

    this.timeout = setTimeout(() => {
      if (this.listOfReports.length)
        this.listOfReports = this.filterByValue(this.listOfReportsInitial, searchKey)
    }, 2000);

  }

  filterByValue(array, string) {
    return array.filter(o => {
      return Object.keys(o).some(k => {
        if (typeof o[k] === 'string')
          return o[k].toLowerCase().includes(string.toLowerCase());
      });
    });
  }
  reasonChange(event) {
    if (event.target.value == "Duplicate") {
      this.encounterIDbyUser = -1;
    }
    else {
      this.encounterIDbyUser = null;
    }
  }


}
