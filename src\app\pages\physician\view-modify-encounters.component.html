<!-- Begin Page Content Starts -->
<div class="container-fluid" [ngClass]="{disabledNoOfCasesDiv: isDisabled}">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new p-2" style="background: #0169ab;" [formGroup]="FilterForm">
                    <div class="align-items-center row mx-auto">
                        <div class="col-12 px-0">
                            <div class="row mb-2 mt-1 mx-auto">
                                <div class="col-12 col-md-4 px-1 py-1">
                                    <select id="ddlFacility" class="form-control" formControlName="ddlFacility"
                                        (change)="getPatientsByFacility($any($event.target).value)"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                                        <option value="">---Select Facility---</option>
                                        <option value="All">All Facilities</option>
                                        <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                                            <span>{{s.facilityName}}</span>
                                        </option>
                                    </select>
                                    <div *ngIf="submitted && f['ddlFacility'].errors" class="invalid-feedback">
                                        <div *ngIf="f['ddlFacility'].errors['required']">Facility is required</div>
                                    </div>
                                </div>

                                <div class="col-12 col-md-3 px-1 py-1">
                                    <input title="Encounter Seen From Date" id="encounterSeenDateFrom" type="date"
                                        class="form-control" formControlName="encounterSeenDateFrom"
                                        iosDateInput placeholder="MM/DD/YYYY"
                                        [ngClass]="{ 'is-invalid': submitted && f['encounterSeenDateFrom'].errors}">
                                    <div *ngIf="submitted && f['encounterSeenDateFrom'].errors"
                                        class="invalid-feedback">
                                    </div>
                                </div>
                                <div class="col-12 col-md-3 px-1 py-1">
                                    <input title="Encounter Seen To Date" id="encounterSeenDateTo" type="date"
                                        class="form-control" formControlName="encounterSeenDateTo"
                                        iosDateInput placeholder="MM/DD/YYYY"
                                        [ngClass]="{ 'is-invalid': submitted && f['encounterSeenDateTo'].errors}">
                                    <div *ngIf="submitted && f['encounterSeenDateTo'].errors" class="invalid-feedback">
                                    </div>
                                </div>
                                <div class="col-12 px-1 py-1"
                                    *ngIf="FilterForm.value.encounterSeenDateFrom&&FilterForm.value.encounterSeenDateTo"
                                    [ngClass]="{'col-md-1': FilterForm.value.encounterSeenDateFrom||FilterForm.value.encounterSeenDateTo, 'col-md-2': !FilterForm.value.encounterSeenDateFrom&&!FilterForm.value.encounterSeenDateTo}">
                                    <button title="Submit Filter"
                                        class="btn btn-outline-info px-2 btn-block d-m-inline-block" type="submit"
                                        (click)="getPatientsViewCharges(1)">
                                        <i class="fa-filter fas font-size-13"></i>
                                        Filter
                                    </button>
                                </div>
                                <div class="col-12 col-md-1 px-1 py-1"
                                    *ngIf="FilterForm.value.encounterSeenDateFrom||FilterForm.value.encounterSeenDateTo||FilterForm.value.admissionDate">

                                    <button title="Clear Dates" class="btn btn-outline-info px-2 mr-2" type="submit"
                                        (click)="clearFilterBillerPatients()">
                                        <i class="fas fa-times"></i>
                                        Clear
                                    </button>

                                </div>

                            </div>

                        </div>


                    </div>
                    <div class="row mb-2 mt-1 mx-auto"
                        *ngIf="(submitted && (f['encounterSeenDateFrom'].errors&&f['encounterSeenDateFrom'].errors['endDateError']||f['encounterSeenDateTo'].errors&&f['encounterSeenDateTo'].errors['endDateError']))">
                        <div class="col text-danger px-1 py-1 text-center">Encounter Seen From date should be less than
                            Encounter Seen To date.</div>
                    </div>

                    <div class="row mb-2 mt-1 mx-auto">
                        <div class="col-12 col-md-12 px-1 py-1">
                            <div class="input-group">
                                <input type="text" class="form-control small" (keyup)="SearchPatientData()"
                                    maxlength="1000" placeholder="Search for Patient Name, Account No, MRN or Room"
                                    aria-label="Search" aria-describedby="basic-addon2" [(ngModel)]="searchByName"
                                    [ngModelOptions]="{standalone: true}">
                                <div class="input-group-append">
                                    <button class="bg-white btn text-dark"><i class="fas fa-search fa-sm"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Card Body -->
                <div class="card-body px-2 py-1">
                    <div class="row mx-0">
                        <div class="col-12 px-0">
                            <!-- Desktop View -->
                            <div class="tablechange table-responsive" *ngIf="!device">
                                <table class="table table-borderless">
                                    <thead>
                                        <tr>
                                            <th class="pb-0 width-13per">Patient Name</th>
                                            <th class="width-10per text-center pb-0">Account No.</th>
                                            <th class="width-10per text-center pb-0">MRN</th>
                                            <th class="width-8per text-center pb-0">DOB</th>
                                            <th class="width-4per text-center pb-0">Sex</th>
                                            <th class="width-8per text-center pb-0">SSN</th>
                                            <th class="text-center pb-0 width-6per">Facility</th>
                                            <th class="width-6per text-center pb-0">Room</th>
                                            <th class="width-11per small-scroll mr-1 pb-0">Admission Date</th>
                                            <th class="width-11per small-scroll ml-1 pb-0">Discharge Date</th>
                                            <th class="pb-0 width-10per small-scroll-2 small-scroll ml-2">Primary Coverage</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <ng-container
                                            *ngFor="let item of listOfPatients | paginate: { itemsPerPage: 14, currentPage: p,totalItems:totalCount}; let i= index;">
                                            <tr *ngIf="item.account_Number!=''" class="desktop-row">
                                                <td (keyup)="getEncountersByPatient(item,true)"
                                                    (click)="getEncountersByPatient(item,true)"
                                                    class="bill-arrow collapsed width-13per text-nowrap position-relative" data-toggle="collapse"
                                                    attr.data-target="#comaintable{{i}}" aria-expanded="false">
                                                    <span
                                                        class="overflow-hidden pl-3 text-nowrap overflow-text cursor-pointer font-weight-bold">{{item.patient_Name}}</span>
                                                    <span *ngIf="item.discharge_Date" class="ml-1"><i class="fa fa-bed"
                                                            title="Discharged"></i></span>
                                                    <span *ngIf="item.billervisitstatus=='0'" class="ml-1"><i
                                                            class="fa fa-user-plus" title="New"></i></span>
                                                </td>
                                                <td class="width-10per text-center">{{item.account_Number}}</td>
                                                <td class="width-10per text-center">{{item.mrn}}</td>
                                                <td class="width-8per text-center">{{item.dob}}</td>
                                                <td class="width-4per text-center">{{item.sex}}</td>
                                                <td class="width-8per text-center"><span
                                                        *ngIf="item.ssn">******</span>{{item.ssn | slice:6:9}}
                                                </td>
                                                <td class="text-center width-6per">{{item.facility_Name}}</td>
                                                <td class="width-6per text-center">{{item.room_Number}}</td>
                                                <td class="width-6per text-left">{{item.admission_Date}}</td>
                                                <td class="width-6per text-center">{{item.discharge_Date}}</td>
                                                <td class="pb-0 width-10per small-scroll-2 small-scroll ml-2">
                                                    {{item.primary_Coverage}}
                                                </td>
                                            </tr>
                                            <tr *ngIf="item.account_Number==envAccountNo&&item.mrn==envMrnNo&&item.facility_Name==envFacility"
                                                id="comaintable{{i}}" class="collapse">
                                                <td colspan="11" class="inner-table-boder" style="background: #fff !important;">
                                                    <div class="row mx-0 innerscroll">
                                                        <div class="col-12 px-md-1 px-0">
                                                            <!-- innerconetct start -->
                                                            <div class="row mx-0">
                                                                <div class="col-12 ml-auto text-right pb-1 px-0">
                                                                    <app-actions-of-view-modify-encounter [PatientObject]="PatientObject" [userType]="'PHYSICIAN'"
                                                                        [listOfFacilities]="listOfFacilities"></app-actions-of-view-modify-encounter>
                                                                </div>
                                                            </div>
                                                            <!-- encounter starts -->
                                                            <app-viewedit-encounter
                                                                [lisfOfEncounters]="lisfOfEncounters"
                                                                [PatientObject]="item"
                                                                [firstEcounterIdToOpen]="firstEcounterIdToOpen"
                                                                [groupAlerts]="groupAlerts">
                                                            </app-viewedit-encounter>
                                                            <!-- encouter ends -->
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        </ng-container>
                                    </tbody>
                                    <tr *ngIf="totalCount==0">
                                        <td colspan="11" class="text-center">No patients found</td>
                                    </tr>
                                    <tfoot>
                                        <tr>
                                            <td colspan="11" class="m-0 p-0 pagination-cell">
                                                <pagination-controls previousLabel="" nextLabel=""
                                                    (pageChange)="getPatientsViewCharges($event)"></pagination-controls>
                                            </td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>

                            <!-- Mobile View -->
                            <div *ngIf="device" class="mobile-view-container">
                                <ng-container *ngFor="let item of listOfPatients | paginate: { itemsPerPage: 14, currentPage: p,totalItems:totalCount}; let i= index;">
                                    <div class="mobile-card mb-3" *ngIf="item.account_Number!=''">
                                        <!-- Patient Header Section -->
                                        <div class="card-header p-2"
                                             (click)="getEncountersByPatient(item,true)"
                                             [attr.data-target]="'#mobileComaintable' + i"
                                             data-toggle="collapse">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0 font-weight-bold"
                                                    [ngClass]="{'hasTodayEncounter': (item.hasPreviousEncounter=='1' && item.resetPriorEncounterStatus=='1'),
                                                               'hasDraftEncounter': (item.hasPreviousEncounter=='1' && item.encounteR_ID!='0' && item.encounteR_ID!=null),
                                                               'hasPriorEncounter': (item.hasPreviousEncounter=='1' && item.encounteR_ID==null && item.resetPriorEncounterStatus==null),
                                                               'hasNoEncounter': (item.hasPreviousEncounter=='0')}">
                                                    {{item.patient_Name}}
                                                    <span *ngIf="item.discharge_Date" class="ml-1"><i class="fa fa-bed" title="Discharged"></i></span>
                                                    <span *ngIf="item.billervisitstatus=='0'" class="ml-1"><i class="fa fa-user-plus" title="New"></i></span>
                                                </h6>
                                                <i class="fas fa-chevron-down"></i>
                                            </div>
                                        </div>

                                        <!-- Patient Details Section -->
                                        <div class="card-body p-2">
                                            <div class="row">
                                                <div class="col-6">
                                                    <p class="mb-1"><strong>Account:</strong> {{item.account_Number}}</p>
                                                    <p class="mb-1"><strong>MRN:</strong> {{item.mrn}}</p>
                                                    <p class="mb-1"><strong>DOB:</strong> {{item.dob}}</p>
                                                    <p class="mb-1"><strong>Sex:</strong> {{item.sex}}</p>
                                                </div>
                                                <div class="col-6">
                                                    <p class="mb-1"><strong>SSN:</strong> <span *ngIf="item.ssn">******</span>{{item.ssn | slice:6:9}}</p>
                                                    <p class="mb-1"><strong>Facility:</strong> {{item.facility_Name}}</p>
                                                    <p class="mb-1"><strong>Room:</strong> {{item.room_Number}}</p>
                                                    <p class="mb-1"><strong>Primary Coverage:</strong> {{item.primary_Coverage}}</p>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Encounter Details Section -->
                                        <div [id]="'mobileComaintable' + i" class="collapse">
                                            <div class="card-body border-top p-2">
                                                <div class="row mx-0">
                                                    <div class="col-12 ml-auto text-right pb-1 px-0">
                                                        <app-actions-of-view-modify-encounter
                                                            [PatientObject]="PatientObject"
                                                            [userType]="'PHYSICIAN'"
                                                            [listOfFacilities]="listOfFacilities">
                                                        </app-actions-of-view-modify-encounter>
                                                    </div>
                                                </div>
                                                <app-viewedit-encounter
                                                    [lisfOfEncounters]="lisfOfEncounters"
                                                    [PatientObject]="item"
                                                    [firstEcounterIdToOpen]="firstEcounterIdToOpen"
                                                    [groupAlerts]="groupAlerts">
                                                </app-viewedit-encounter>
                                            </div>
                                        </div>
                                    </div>
                                </ng-container>

                                <!-- Mobile Pagination -->
                                <div class="d-flex justify-content-center">
                                    <pagination-controls
                                        previousLabel=""
                                        nextLabel=""
                                        (pageChange)="getPatientsViewCharges($event)">
                                    </pagination-controls>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Common Modals/Popups with Mobile Optimizations -->
    <div class="modal-container" [ngClass]="{'mobile-modal': device}">
        <!-- View History -->
        <app-view-encounter-history [listOfHistory]='listOfHistory'></app-view-encounter-history>

        <!-- Delete Encounter -->
        <app-delete-encounter [encounterObj]="encounterObj" [userType]="'Physician'"></app-delete-encounter>

        <!-- Edit Encounter Seen Date -->
        <app-edit-encounter-seen-date
            [encounterObj]="encounterObj"
            [PatientObject]="PatientObject"
            [userType]="'PHYSICIAN'">
        </app-edit-encounter-seen-date>
    </div>

    <!-- Mobile styles are now in mobile-responsive.css -->
