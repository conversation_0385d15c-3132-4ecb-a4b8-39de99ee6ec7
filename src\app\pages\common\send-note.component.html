<div class="modal fade" id="mdlNotes" tabindex="-1" aria-labelledby="coNotesLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content" [formGroup]="noteForm">
            <div class="modal-header">
                <h5 class="modal-title" id="coNotesLabel">
                    {{PatientObject?.patient_Name}}-{{PatientObject?.account_Number}} Notes</h5>
                <i class="fa fa-times closePopup" data-dismiss="modal"></i>
            </div>
            <div class="modal-body min50v over-flow-y-hidden">
                <div class="form-group">
                    <label class="group-lbl" for="inputAddress">Users And Groups</label>
                    <ng-multiselect-dropdown id="inputAddress" [placeholder]="'--select user and group--'" [data]="listOfUsersAndGroups"
                        [(ngModel)]="selectedUsersAndGroups" [ngModelOptions]="{standalone: true}"
                        [settings]="mDdlUsersAndGroupsSettings" (onSelect)="onItemNoteSelect($event)">
                    </ng-multiselect-dropdown>
                    <div *ngIf="ddlVali" style="color: #e74a3b;">Users And Groups is required</div>
                </div>
                <div *ngFor="let item of lisfOfSentNotes" class="popupBody row mx-0">
                    <div class="col-8 px-1 py-1">
                        <h5 class="headerPopup pt-1 pb-0 mb-0 text-primary">{{item.fullName}}</h5>
                        <p class="subHeaderPopup pt-0 pb-1 mb-0">{{item.message}}</p>
                    </div>
                    <div class="col-3 text-nowrap px-1 py-1 contentPopup"><i class="fa fa-clock"
                            aria-hidden="true"></i>{{item.notescteateddate| date:'MMM d, y, h:mm:ss a'}}</div>
                </div>
            </div>
            <div class="modal-footer py-2">
                <input type="text" id="txtMessage" class="form-control" maxlength="1000" formControlName="txtMessage"
                    [ngClass]="{ 'is-invalid': submitted && f['txtMessage'].errors}">
                <div *ngIf="submitted && f['txtMessage'].errors" class="invalid-feedback">
                    <br />
                    <div *ngIf="f['txtMessage'].errors['required']">Message is required</div>
                    <div *ngIf="f['txtMessage'].errors['maxlength']">Max length is 400 characters.</div>
                </div>
                <button class="btn btn-outline-info float-right" type="submit"
                    (click)="insertNote(PatientObject)">Send</button>
            </div>
        </div>
    </div>
</div>
