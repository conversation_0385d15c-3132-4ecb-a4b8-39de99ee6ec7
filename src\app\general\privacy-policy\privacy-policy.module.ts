import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PrivacyPolicyRoutingModule } from './privacy-policy-routing.module';
import { PrivacyPolicyComponent } from './privacy-policy.component';
import { SubjectAccessComponent } from './subject-access.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NewPrivacyPolicyComponent } from './new-privacy-policy.component';


@NgModule({
  declarations: [
    PrivacyPolicyComponent,
    SubjectAccessComponent,
    NewPrivacyPolicyComponent
  ],
  imports: [
    CommonModule,
    PrivacyPolicyRoutingModule,
    FormsModule,
    ReactiveFormsModule
  ]
})
export class PrivacyPolicyModule { }
