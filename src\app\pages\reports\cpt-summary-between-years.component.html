<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new p-2" style="background: #0169ab;">
                    <div class="align-items-center row mx-auto">
                        <div class="col-12 px-0">
                            <div class="row my-1 mx-auto" [formGroup]="searchForm">
                                <div class="col-12 col-md-3 px-1 py-1">
                                    <select id="ddlFacility" class="form-control w-100"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}"
                                        formControlName="ddlFacility">
                                        <option value="">---Select Facility---</option>
                                        <option *ngFor="let s of listOfFacilities">
                                            {{s.facilityName}}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-12 col-md-3 px-1 py-1">
                                    <select class="form-control w-100" id="ddlType"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlType'].errors}"
                                        formControlName="ddlType">
                                        <option value=''>--Select Type--</option>
                                        <option value='IHV'>Initial Hospital Visits</option>
                                        <option value='SHV'>Subsequent Hospital Visits</option>
                                        <option value='CC'>Critical Care</option>
                                        <option value='OTHER'>Other</option>
                                    </select>
                                </div>
                                <div class="col-12 col-md-2 px-1 py-1">
                                    <select class="form-control w-100"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlFromYear'].errors}"
                                        formControlName="ddlFromYear">
                                        <option value=''>--Select Year--</option>
                                        <option [value]="year" *ngFor="let year of listOfYears">
                                            <span>{{year}}</span>
                                        </option>
                                    </select>
                                </div>
                                <div class="col-12 col-md-2 px-1 py-1">
                                    <select class="form-control w-100"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlToYear'].errors}"
                                        formControlName="ddlToYear">
                                        <option value="">---Select Year---</option>
                                        <option [value]="year" *ngFor="let year of listOfYears">
                                            <span>{{year}}</span>
                                        </option>
                                    </select>
                                </div>
                                <div class="col-12 col-md-2 px-1 py-1">
                                    <button class="btn btn-outline-info px-2 btn-block" type="submit"
                                        (click)="getReportSummary()">Search</button>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>

                <div class="card-body p-1">

                    <div class="row mx-auto">
                        <!-- column 1 Chart start-->
                        <div class="col-12 p-0">
                            <div class="card shadow mb-3">
                                <div class="card-header pl-0 position-relative bg-blue py-3">
                                    <ng-container *ngIf="this.lstOfReportData.length>0">
                                        <img alt=' ' [src]="img1" class="float-right" (keyup)="exportAsXLSX()"
                                            (click)="exportAsXLSX()"
                                            style="width: 24px;position: absolute;right: 10px;top: 6px;cursor: pointer;"
                                            title="Export Excel">
                                        <i class="fa-eye far text-info" data-toggle="modal"
                                            data-target="#viewReportData"
                                            style="width: 24px;position: absolute;right: 50px;top: 7px;cursor: pointer;font-size: 24px;color: white;"></i>
                                    </ng-container>
                                </div>
                                <div class="card-body">
                                    <div id="conatinerCPTSummaryBtYs"
                                        style="min-width: 1200px; height: 800px; max-width: 1200px; margin: 0 auto">
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
<!-- Begin Page Content Ends -->


<!-- model popups starts -->
<div class="modal fade" id="viewReportData" tabindex="-1" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog custom-modal modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title" id="exampleModalCenterTitle">View Data</h5>
            </div>
            <div class="modal-body p-2 modal-height">
                <div class="row mx-0">
                    <div class="col">
                        <div class="table-responsive">
                            <table class="table table-bordered" id="dataTable width-100per">
                                <thead>
                                    <tr>
                                        <th class="text-nowrap bg-gradient-primary text-white">Physician</th>
                                        <th class="text-nowrap bg-gradient-primary text-white">Year</th>
                                        <th class="text-nowrap bg-gradient-primary text-white"
                                            *ngIf="searchForm.value.ddlType=='IHV'">99221</th>
                                        <th class="text-nowrap bg-gradient-primary text-white"
                                            *ngIf="searchForm.value.ddlType=='IHV'">99222</th>
                                        <th class="text-nowrap bg-gradient-primary text-white"
                                            *ngIf="searchForm.value.ddlType=='IHV'">99223</th>
                                        <th class="text-nowrap bg-gradient-primary text-white"
                                            *ngIf="searchForm.value.ddlType=='SHV'">99231</th>
                                        <th class="text-nowrap bg-gradient-primary text-white"
                                            *ngIf="searchForm.value.ddlType=='SHV'">99232</th>
                                        <th class="text-nowrap bg-gradient-primary text-white"
                                            *ngIf="searchForm.value.ddlType=='SHV'">99233</th>
                                        <th class="text-nowrap bg-gradient-primary text-white"
                                            *ngIf="searchForm.value.ddlType=='CC'">99291</th>
                                        <th class="text-nowrap bg-gradient-primary text-white"
                                            *ngIf="searchForm.value.ddlType=='CC'">99292</th>
                                        <th class="text-nowrap bg-gradient-primary text-white"
                                            *ngIf="searchForm.value.ddlType=='OTHER'">CPT</th>
                                        <th class="text-nowrap bg-gradient-primary text-white"
                                            *ngIf="searchForm.value.ddlType=='OTHER'">Count</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let item of lstOfReportData">
                                        <td class="pname">{{item.physicianname}}</td>
                                        <td class="faci">{{item.yearvalue}}</td>
                                        <td class="faci" *ngIf="searchForm.value.ddlType=='IHV'">{{item.cpt_99221}}</td>
                                        <td class="faci" *ngIf="searchForm.value.ddlType=='IHV'">{{item.cpt_99222}}</td>
                                        <td class="faci" *ngIf="searchForm.value.ddlType=='IHV'">{{item.cpt_99223}}</td>
                                        <td class="faci" *ngIf="searchForm.value.ddlType=='SHV'">{{item.cpt_99231}}</td>
                                        <td class="faci" *ngIf="searchForm.value.ddlType=='SHV'">{{item.cpt_99232}}</td>
                                        <td class="faci" *ngIf="searchForm.value.ddlType=='SHV'">{{item.cpt_99233}}</td>
                                        <td class="faci" *ngIf="searchForm.value.ddlType=='CC'">{{item.cpt_99291}}</td>
                                        <td class="faci" *ngIf="searchForm.value.ddlType=='CC'">{{item.cpt_99292}}</td>
                                        <td class="faci" *ngIf="searchForm.value.ddlType=='OTHER'">{{item.cpt}}</td>
                                        <td class="faci" *ngIf="searchForm.value.ddlType=='OTHER'">
                                            {{item.cpT_Used_Count}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-2">

                <button class="btn btn-outline-info float-right" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<!-- model popups ends -->