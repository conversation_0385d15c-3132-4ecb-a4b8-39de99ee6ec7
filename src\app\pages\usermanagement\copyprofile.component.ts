import { Component, HostListener, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, Observable } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap, tap } from 'rxjs/operators';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { UserManagementService } from 'src/app/services/user-management/userManagement.service';
import { AddModifyUserComponent } from './add-modify-user.component';
declare let $;
@Component({
  selector: 'app-copyprofile',
  templateUrl: './copyprofile.component.html',
  styleUrls: ['./copyprofile.component.css']
})
export class CopyprofileComponent implements OnInit {
  public copyProfileform: FormGroup;
  public request: any = {};
  public submitted: boolean = false;
  public listofUsers: Array<any> = [];
  public mDdlfromProfile: any = {};
  public selectedFromProfile: any;
  public userDetails: any = {};
  public addorReplace: string = "";


  // search code starts
  public searchEmp = '';
  public emptyObser=new Observable<any>();
  public term$:BehaviorSubject<any> = new BehaviorSubject<any>([]);
  public results$: any = this.term$.pipe(
    debounceTime(1000),
    distinctUntilChanged(),
    tap(() => {
      const element = document.getElementById('page-top')!;
        element.classList.remove('loading');
    }),
    switchMap((term: string) => {
      if (term.length > 0) {
        return this.commonServ.getADUsers(term);
      }
      else {
        const element = document.getElementById('page-top')!;
        element.classList.remove('loading');
        return this.emptyObser;
      }
    }),
    tap(() => {
      const element = document.getElementById('page-top')!;
        element.classList.remove('loading');
    })
  );
  public seachClass: string;
  public isClicked = false;
  @HostListener('document:click')
  clickout() {
    if (!this.isClicked) {
      this.seachClass = 'hide';
    }
    this.isClicked = false;
  }
  showSearch() {
    this.seachClass = 'show';
  }
  // search code ends

  constructor(private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly encrDecr: EncrDecrServiceService,
    private readonly toastr: ToastrService, private readonly userSer: UserManagementService, private readonly commonServ: CommonService, private readonly addMdfuser: AddModifyUserComponent) { }

  ngOnInit(): void {
    this.fetchUsers();
    this.copyProfileform = this.fb.group({
      ddlfromProfile: ['', Validators.required],
      radioAdd: [true],
      txtUserName: ['', Validators.required],
      radioReplace: [false]

    });
    this.addorReplace = 'ADD';
    this.mDdlfromProfile = {
      singleSelection: true,
      idField: 'userEmailID',
      textField: 'userName',
      enableCheckAll: false,
      itemsShowLimit: 20,
      allowSearchFilter: true,
      closeDropDownOnSelection: true
    };

  }
  get f() { return this.copyProfileform.controls; }

  fetchUsers() {
    this.userSer.getUsers().subscribe((u: any) => {
      this.listofUsers = u.activeUsers;
    }, error => {
      console.error(error.status);
    });
  }

  chkChangeEvent(user) {
    $('#txtUserName').val(user.display_name);
    this.userDetails = user;
    this.seachClass = 'hide';
  }

  SaveCopyProfile() {

    this.submitted = true;
    if (this.copyProfileform.invalid) {
      return;
    }

    this.commonServ.startLoading();
    this.request.FromEmailID = this.encrDecr.set(this.selectedFromProfile[0].userEmailID);
    this.request.ToEmailID = this.encrDecr.set(this.userDetails.email_id);
    this.request.FirstName = this.encrDecr.set(this.userDetails.firstname);
    this.request.LastName = this.encrDecr.set(this.userDetails.lastname);
    this.request.DisplayName = this.encrDecr.set(this.userDetails.display_name);

    if (this.addorReplace == 'ADD') {
      this.request.Type = this.encrDecr.set('ADD');
    }
    else {
      this.request.Type = this.encrDecr.set('REPLACE');
    }

    this.userSer.CopyProfile(this.request).subscribe((p: any) => {
      this.request = {};
      this.submitted = false;
      this.commonServ.stopLoading();
      if (p == 'SUCCESS') {
        this.addorReplace = '';
        this.toastr.success('Profile copied successfully.', '', { timeOut: 2500 });
        $('#copyProfilepopup').modal('hide');
        this.addMdfuser.fetchActiveUsers();

      }
      else {
        this.toastr.error('Error while replacing profile ');
      }

    },
      error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
  }
  Cancel() {
    this.addorReplace = 'ADD';
  }



}
