import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PhysicianRoutingModule } from './physician-routing.module';
import { MyPatientsComponent } from './my-patients.component';
import { MyGroupPatientsComponent } from './my-group-patients.component';
import { HospitalCensusComponent } from './hospital-census.component';
import { DischargePatientsComponent } from './discharge-patients.component';
import { ViewModifyEncountersComponent } from './view-modify-encounters.component';
import { UnbilledEncountersComponent } from './unbilled-encounters.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { NgxPaginationModule } from 'ngx-pagination';
import { ChartModule } from 'angular-highcharts';
import { StartNewEncounterComponent } from './start-new-encounter.component';
import { CPTDataComponent } from './cptdata.component';
import { ICDDataComponent } from './icddata.component';
import { CptModifiersComponent } from './cptmodifiers.component';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { PopoverModule } from 'ngx-bootstrap/popover';
import { ViewHistoryComponent } from './view-history.component';
import { CommonTaskModule } from '../common/common.module';
import { PendingApprovalEncountersComponent } from './pending-approval-encounters.component';
import { AssignToOthersOrResidentsComponent } from './assign-to-others-or-residents.component';
import { VieweditEncounterComponent } from './viewedit-encounter.component';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatIconModule } from '@angular/material/icon';
import { NgxMultipleDatesModule } from 'ngx-multiple-dates'; // module import
import { MatFormFieldModule } from '@angular/material/form-field';
import { ApprovePendingEncounterComponent } from './approve-pending-encounter.component';
import { MtxNativeDatetimeModule } from '@ng-matero/extensions/core';
import { MtxDatetimepickerModule } from '@ng-matero/extensions/datetimepicker';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { SharedModule } from 'src/app/shared.module';
import { PatientsComponent } from './patients.component';
import { DirectivesModule } from 'src/app/directives/directives.module';

@NgModule({
  declarations: [
    MyPatientsComponent,
    MyGroupPatientsComponent,
    HospitalCensusComponent,
    DischargePatientsComponent,
    ViewModifyEncountersComponent,
    UnbilledEncountersComponent,
    StartNewEncounterComponent,
    CPTDataComponent,
    ICDDataComponent,
    CptModifiersComponent,
    ViewHistoryComponent,
    PendingApprovalEncountersComponent,
    AssignToOthersOrResidentsComponent,
    VieweditEncounterComponent,
    ApprovePendingEncounterComponent, PatientsComponent,
  ],
  imports: [
    CommonModule,
    PhysicianRoutingModule,
    ChartModule,
    FormsModule,
    ReactiveFormsModule,
    NgxPaginationModule,
    NgMultiSelectDropDownModule.forRoot(),
    TooltipModule.forRoot(),
    PopoverModule.forRoot(),
    CommonTaskModule,
    MatInputModule,
    MatNativeDateModule,
    MatDatepickerModule,
    MatIconModule,
    NgxMultipleDatesModule,
    MatFormFieldModule,
    MtxNativeDatetimeModule,
    MtxDatetimepickerModule,
    MatButtonModule,
    MtxDatetimepickerModule,
    SharedModule,
    DirectivesModule
  ],
  providers: [],
  bootstrap: [MyPatientsComponent]
})
export class PhysicianModule { }
