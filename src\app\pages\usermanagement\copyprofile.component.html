<div class="modal fade" id="copyProfilepopup" tabindex="-1" maria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg">
        <div class="modal-content" [formGroup]="copyProfileform">
            <div class="modal-header py-2">
                <h5 class="modal-title" id="exampleModalCenterTitle">Copy Profile</h5>
                <i class="fa fa-times closePopup" data-dismiss="modal"></i>
            </div>
            <div class="modal-body p-md-2 p-1">
                <div class="row mx-auto py-1">
                    <div class="col-12">
                        <div class="form-group">
                            <label class="my-auto" for="ddlfromProfile">Copy Profile From</label>
                            <ng-multiselect-dropdown formControlName="ddlfromProfile" [data]="listofUsers"
                                [settings]="mDdlfromProfile" [(ngModel)]="selectedFromProfile" id="ddlfromProfile"
                                [placeholder]="'--Choose from Profile--'">
                            </ng-multiselect-dropdown>
                            <div *ngIf="submitted && f['ddlfromProfile'].invalid" class="alert alert-danger">
                                <div *ngIf="f['ddlfromProfile'].errors&&f['ddlfromProfile'].errors['required']">Copy
                                    Profile From is required.</div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="row mx-auto py-1">
                    <div class="col-12">
                        <div class="form-group">
                            <span class="my-auto" for="txtUserName">Copy Profile To</span>
                            <span class="has-float-label w-100">
                                <input class="form-control input-border" (input)="term$.next($any($event.target).value)"
                                    formControlName="txtUserName" (keyup)="showSearch()" id="txtUserName" type="text"
                                    placeholder="AD User Id / Display Name" autocomplete="off">
                            </span>
                            <div *ngIf="results$ | async as users"
                                class="row mx-0 position-absolute empSerach {{seachClass}}">
                                <div class="col-12 px-0 py-0">
                                    <div class="row mx-0 align-items-center px-0 py-1 empSearchResults"
                                        *ngFor="let user of results$ | async">
                                            <div class="col-2 px-1">
                                                <div class="myaccount1">
                                                    <span class="mylabel1" (keyup)="chkChangeEvent(user)" (click)="chkChangeEvent(user)">{{user.email_id | slice:0:2}}</span>
                                                </div>
                                            </div>
                                            <div class="col-10 pl-0">
                                                <span (keyup)="chkChangeEvent(user)" (click)="chkChangeEvent(user)">{{user.display_name}}<br />{{user.email_id}}</span>
                                            </div>
                                    </div>
                                </div>
                                <div class="col-12 px-0 py-0">
                                    <div class="row mx-0 align-items-center px-0 py-1 empSearchResults">
                                        <div class="col-12 px-1 text-center">
                                            <span>No Results Found</span>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div *ngIf="submitted && f['txtUserName'].invalid" class="alert alert-danger">
                                <div *ngIf="f['txtUserName'].errors&&f['txtUserName'].errors['required']">Copy Profile
                                    To is required.</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mx-auto py-1 justify-content-end">

                    <div class="col-12 col-md-3">
                        <div class="form-group">

                            <div class="form-check d-inline-block">
                                <input type="radio" id="IsAdd" [(ngModel)]="addorReplace" name="addorReplaces"
                                    value="ADD" formControlName="radioAdd">
                                <label for="IsAdd" class="ml-1">Add</label>
                            </div>

                            <div class="form-check d-inline-block">
                                <input type="radio" id="IsReplace" [(ngModel)]="addorReplace" name="addorReplaces"
                                    value="REPLACE" formControlName="radioReplace">
                                <label for="IsReplace" class="ml-1">Replace</label>
                            </div>
                        </div>

                    </div>

                </div>
            </div>
            <div class="modal-footer py-2">
                <button class="btn btn-outline-info float-right" (click)="SaveCopyProfile()">Copy</button>
                <button class="btn btn-outline-info float-right" (click)="Cancel()" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>