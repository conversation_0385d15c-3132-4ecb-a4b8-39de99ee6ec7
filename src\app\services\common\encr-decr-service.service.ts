import { Injectable } from '@angular/core';
import * as CryptoJ<PERSON> from 'crypto-js';
import { AppConfiguration } from 'src/app/config/app-configuration';


@Injectable({
  providedIn: 'root'
})
export class EncrDecrServiceService {

  constructor(private readonly appConfig: AppConfiguration) { }

  //The set method is use for encrypt the value.
  set(value) {
    let key = CryptoJS.enc.Utf8.parse(this.appConfig.data1);
    let iv = CryptoJS.enc.Utf8.parse(this.appConfig.data2);
    let encrypted = CryptoJS.AES.encrypt(CryptoJS.enc.Utf8.parse(value.toString()), key,
      {
        keySize: 256 / 8,
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      });

    return encrypted.toString();
  }

  //The get method is use for decrypt the value. 
  get(value) {
    let key = CryptoJS.enc.Utf8.parse(this.appConfig.data1);
    let iv = CryptoJS.enc.Utf8.parse(this.appConfig.data2);
    let decrypted = CryptoJS.AES.decrypt(value, key, {
      keySize: 256 / 8,
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });

    return decrypted.toString(CryptoJS.enc.Utf8);
  }
}
