import { Component, EventEmitter, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';

@Component({
  selector: 'app-search-patients',
  templateUrl: './search-patients.component.html',
  styles: [
  ]
})
export class SearchPatientsComponent {
  public FilterForm: FormGroup;
  public listOfFacilities: Array<any> = [];
  public submitted: boolean = false;
  get f() { return this.FilterForm.controls; };
  @Output() eventGetPatientDetails=new EventEmitter<Array<any>>();
  constructor(private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService,private readonly fb: FormBuilder) { }

  ngOnInit() {
    this.getFacilities();
    this.FilterForm = this.fb.group({
      ddlFacility: ['', Validators.required],
      txtAcNo: ['', [Validators.required, Validators.maxLength(50)]]
    });
  }
  getFacilities() {
    this.commonServ.getFacilities().subscribe((p: any) => {
      this.listOfFacilities = p;
    }, error => { console.error(error.status); });
  }
  getPatientDetails(){
    this.submitted = true;
    if (this.FilterForm.invalid) {
      return;
    }
    
    let request: any = {
      PATIENTACCOUNTNUMBER: this.encrDecr.set(this.FilterForm.value.txtAcNo),
      FACILITYNAME: this.encrDecr.set(this.FilterForm.value.ddlFacility)
    };
    this.eventGetPatientDetails.emit(request);
    this.submitted = false;
    this.FilterForm.get('txtAcNo')?.setValue('');
    this.FilterForm.get('ddlFacility')?.setValue('');
  }
}
