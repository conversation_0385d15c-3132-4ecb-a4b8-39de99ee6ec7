import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { apiUrl } from 'src/app/config';
import { EncrDecrServiceService } from '../common/encr-decr-service.service';

@Injectable({
  providedIn: 'root'
})
export class CoordinatorService {

  constructor(private readonly http: HttpClient, private readonly encrDecr: EncrDecrServiceService) { }
  GetDepartments(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Coordinator/GetDepartments', request, { headers: headers });
  }
  GetGroups(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Coordinator/GetGroups', request, { headers: headers });
  }
  getCoordinatorPatients(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Coordinator/GetGroupPatientsForCoordinator', request, { headers: headers });
  }
  getPatientForCoordinatorForDownload(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Coordinator/GetPatientForCoordinatorForDownload', request, { headers: headers });
  }
  GetPatientsChargesDataCoordinator(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Coordinator/GetPatientsChargesDataCoordinator', request, { headers: headers });
  }
  getEncountersByPatient(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Coordinator/AsyncGetEncountersByPatientForCoordinator', request, { headers: headers });
  }
  editEncounterSeenDate(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Coordinator/EditEncounterSeenDate', request, { headers: headers });
  }

  markAsUnApprovedOrApproved(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Coordinator/ConfirmMarkAsApproved', request, { headers: headers });
  }
  markAllAsUnApprovedOrApproved(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Coordinator/ConfirmMarkAllAsApproved', request, { headers: headers });
  }
  editEncounter(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Coordinator/EditEncounter', request, { headers: headers });
  }
  getModifiers(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/GetModifiers', request, { headers: headers });
  }

  saveNotesGroupsAttendingPhysician(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Coordinator/SaveNotesGroupsAttendingPhysician', request, { headers: headers });
  }
  getDepartmentsGroupsPhysiciansByFacility(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Coordinator/GetDepartmentsGroupsPhysiciansByFacility', request, { headers: headers });
  }
  getAlerts(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/GetAlerts', request, { headers: headers });
  }
  getNonprimeFacilities(userType) {
    const body = { ParamOne: this.encrDecr.set(userType) };
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Coordinator/GetNonprimeFacilities', body, { headers: headers });
  }
  swapPhysician(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Coordinator/SwapPhysician', request, { headers: headers });
  }
  GetPhysiciansNonPrimeFacility(facillity) {
    const body = { FacilityName: this.encrDecr.set(facillity) };
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Coordinator/GetPhysiciansNonPrimeFacility', body, { headers: headers });
  }


  GetDischargePatients(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Coordinator/GetDischargePatients', request, { headers: headers });
  }


  EditMultipleEncounters(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Billing/EditMultipleEncounters', request, { headers: headers });
  }

  saveCoordinatorAssignments(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Coordinator/SaveCoordinatorAssignments', request, { headers: headers });
  }

  getCoordinatorAssignmentHistory(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Coordinator/GetCoordinatorAssignmentHistory', request, { headers: headers });
  }

  getDataCalendarView(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Coordinator/GetDataCalendarView', request, { headers: headers });
  }

  getPatientDataMonthView(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Coordinator/GetPatientDataMonthView', request, { headers: headers });
  }
}
