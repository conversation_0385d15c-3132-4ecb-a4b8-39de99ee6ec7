<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">

        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new px-0 py-2" style="background: #0169ab;">
                    <div class="align-items-center mx-0 row">
                        <div class="col-12 col-md- py-1 pl-2 pr-1 position-relative">
                            <div class="input-group">
                                <input type="text" class="form-control small" maxlength="1000"
                                    placeholder="Search for facility name, facility full name,time zone, address, state,Cith and ZipCode"
                                    aria-label="Search" aria-describedby="basic-addon2" [(ngModel)]="search"
                                    [ngModelOptions]="{standalone: true}">
                                <div class="input-group-append">
                                    <button class="bg-gradient-light btn text-dark" type="button">
                                        <i class="fas fa-search fa-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Card Body -->
                <div class="card-body px-2 py-1">
                    <div class="row mx-0">
                        <div class="col-12 px-0">

                            <table class="table table-striped table-bordered table-hover">
                                <tr>
                                    <th class="text-center">Facility Name</th>
                                    <th class="text-center">Facility Full Name</th>
                                    <th class="text-center">Time Zone</th>
                                    <th class="text-center">Address</th>
                                    <th class="text-center">State</th>
                                    <th class="text-center">City</th>
                                    <th class="text-center">ZipCode</th>
                                </tr>
                                <tr
                                    *ngFor="let item of listOfFacilities|gridFilter:{facilityFullName:search,facilityName:search,timeZoneName:search,facilityAddress:search,stateName:search,city:search,zipCode:search}:false |paginate:{ itemsPerPage:10,currentPage:p};let i=index">
                                    <td class="text-center">{{item.facilityName}}</td>
                                    <td class="text-center">{{item.facilityFullName}}</td>
                                    <td class="text-center">{{item.timeZoneName}}</td>
                                    <td class="text-center">{{item.facilityAddress}}</td>
                                    <td class="text-center">{{item.stateName}}</td>
                                    <td class="text-center">{{item.city}}</td>
                                    <td class="text-center">{{item.zipCode}}</td>
                                </tr>
                                <tr>
                                    <td colspan="10" class="m-0 p-0" style="background: white !important;">
                                        <pagination-controls previousLabel="" nextLabel="" (pageChange)="p=$event"
                                            (pageBoundsCorrection)="p=$event"></pagination-controls>
                                    </td>
                                </tr>
                            </table>

                        </div>
                    </div>

                </div>

            </div>
        </div>
        <!-- Area Chart Ends -->
    </div>
</div>
<!-- Begin Page Content Ends -->