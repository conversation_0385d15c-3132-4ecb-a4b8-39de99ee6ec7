import { Component, OnInit } from '@angular/core';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { UserManagementService } from 'src/app/services/user-management/userManagement.service';

@Component({
  selector: 'app-add-modify-groups',
  templateUrl: './add-modify-groups.component.html',
  styles: []
})
export class AddModifyGroupsComponent implements OnInit {
  public listOfGroups: Array<any> = [];
  public p: number;
  public search = '';
  public filterObj: any = {};
  public group: any = {
    groupName: "", groupID: 0, facilityName: "", facilityID: 0, groupHead: 0, isAlertCheck: false, status: 'Active', specialty: false,
    coordinatorAppRequired: false, isAuditRequired: false, integrationType: ""
  };

  constructor(private readonly appComp: AppComponent, private readonly userSer: UserManagementService, private readonly commonServ: CommonService) { }

  ngOnInit(): void {
    this.appComp.loadPageName('Add/Modify Groups', 'usermanagementTab');
    this.fetchGroups();
    if (history.state.filterObj) {
      this.filterObj = history.state.filterObj;
      this.p = this.filterObj.p;
      this.search = this.filterObj.searchKey;
    }
  }

  fetchGroups() {
    this.commonServ.startLoading();
    this.userSer.FetchGroups().subscribe((p: any) => {
      this.listOfGroups = p.groupInfos;
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

}
