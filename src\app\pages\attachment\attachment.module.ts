import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AttachmentRoutingModule } from './attachment-routing.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';
import { AttachmentComponent } from './attachment.component';
import { CommonTaskModule } from '../common/common.module';


@NgModule({
  declarations: [AttachmentComponent],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgxPaginationModule,
    AttachmentRoutingModule,
    CommonTaskModule
  ],
  providers: [],
  bootstrap: [AttachmentComponent]
})
export class AttachmentModule { }
