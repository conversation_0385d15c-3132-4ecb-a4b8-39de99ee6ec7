
import { NgModule } from '@angular/core';
import { ChartModule } from 'angular-highcharts';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';
import { CommonModule } from '@angular/common';
import { EncounterComponent } from './encounter.component';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { AuditorEditComponent } from '../auditor/auditor-edit.component';
import { AuditorRoutingModule } from './auditor-routing.module';
import { CommonTaskModule } from '../common/common.module';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { SharedModule } from 'src/app/shared.module';
import { NgxDaterangepickerMd } from 'ngx-daterangepicker-material';


@NgModule({
  declarations: [
    EncounterComponent,
    AuditorEditComponent
  ],
  imports: [
    CommonModule,
    AuditorRoutingModule,
    ChartModule,
    FormsModule,
    ReactiveFormsModule,
    NgxPaginationModule,
    NgMultiSelectDropDownModule.forRoot(),
    BsDatepickerModule.forRoot(),
    CommonTaskModule,
    TooltipModule.forRoot(),
    SharedModule,
    NgxDaterangepickerMd.forRoot()
  ],
  providers: [],
  bootstrap: [AuditorEditComponent]
})
export class AuditorModule { }