import { Output, Input, EventEmitter, Component } from '@angular/core';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { CoordinatorService } from 'src/app/services/coordinator/coordinator.service';
import { DatePipe } from '@angular/common';

declare let $: any;

@Component({
  selector: 'app-replace-icdcodes',
  templateUrl: './replace-icdcodes.component.html',
  styleUrls: ['./replace-icdcodes.component.css']
})
export class ReplaceICDCodesComponent {
  public request: any = {};
  @Input() listOfRemovedCpts: Array<any> = [];
  @Input() listOfRemovedIcds: Array<any> = [];
  @Input() listOfAllEncounters: Array<any> = [];
  public lisfOfEncounters: Array<any> = [];
  public isValidationMessage: boolean = false;
  public encounterIds: string = "";
  @Input() userType: string = '';
  @Input() encounterObj: any = {};
  @Input() PatientObject: any = {};
  @Input() listOfIcds: Array<any> = [];
  @Output() eventGetEncountersByPatient = new EventEmitter<Array<any>>();
  constructor(private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService, private readonly coorServ: CoordinatorService,
    public datepipe: DatePipe) { }

  confrimsubmitICDsAllEncouners(eObj, pObj, lstEnvs) {

    this.commonServ.startLoading();
    lstEnvs = lstEnvs.filter(x => x.checked);
    if (lstEnvs.length != 0) {
      this.isValidationMessage = false
      this.request.account_number = this.encrDecr.set(pObj.account_Number);
      this.request.sENCOUNTER_ID = this.encrDecr.set(eObj.encounteR_ID);
      lstEnvs.forEach(x => {
        if (eObj.encounteR_ID != x.encounteR_ID) {
          this.encounterIds = this.encounterIds + "," + x.encounteR_ID;
        }
      });
      this.request.sMARKASCODED = this.encrDecr.set('0');
      this.request.ROLE = this.encrDecr.set(this.userType);
      this.request.NEW_ICDArray = this.listOfIcds;
      this.request.DeletedCptsArray = [];
      this.request.DeletedIcdsArray = [];
      this.request.encounterIds = this.encrDecr.set(this.encounterIds);
      this.coorServ.EditMultipleEncounters(this.request).subscribe((p: any) => {
        if (p.status > 0) {
          eObj.lastmodifiedby = p.modifieD_BY;
          eObj.lastmodifieddate = p.modifiedDate;
        }
        this.encounterIds = "";
        $('#saveICDsAllEcounters').modal('hide');
        pObj.last_activity_date = this.datepipe.transform(new Date(), 'MM/dd/yyyy h:mm:ss a');
        this.eventGetEncountersByPatient.emit(this.lisfOfEncounters);

        this.request = {};
        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        this.commonServ.stopLoading();
      });
    }
    else {
      this.commonServ.stopLoading();
      this.isValidationMessage = true;
    }
  }

  chkChangeEvent(event) {
    for (let item of this.listOfAllEncounters) {
      if (item.encounteR_ID == event.target.id) {
        item.checked = event.target.checked;
        this.isValidationMessage = false;
      }
    }
    if (this.listOfAllEncounters.filter(x => x.encounteR_ID != this.encounterObj.encounteR_ID).length == this.listOfAllEncounters.filter(x => x.encounteR_ID != this.encounterObj.encounteR_ID && x.checked).length) {
      $('#chkAll').prop("checked", true);
    }
    else {
      $('#chkAll').prop("checked", false);
    }
  }

  selectAll(event) {
    for (let item of this.listOfAllEncounters) {
      if (item.encounteR_ID != this.encounterObj.encounteR_ID) {
        item.checked = event.target.checked;
      }
    }
  }

}
