<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new p-2" style="background: #0169ab;" [formGroup]="FilterForm">
                    <div class="align-items-center row mx-auto">
                        <div class="col-12 px-0">

                            <div class="row mt-1 mx-auto">
                                <div class="col-12 col-md-3 px-1 py-1">
                                    <select id="ddlFacility" style="height: 90%" class="form-control"
                                        formControlName="ddlFacility"
                                        (change)="getPhysiciansByFacility($any($event.target).value)"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                                        <option value="">---Select Facility---</option>
                                        <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                                            <span>{{s.facilityName}}</span>
                                        </option>
                                    </select>
                                    <div *ngIf="submitted && f['ddlFacility'].errors" class="invalid-feedback">
                                        <div *ngIf="f['ddlFacility'].errors['required']">Facility is required</div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-3 px-1 py-1 multiselect-absolute">
                                    <ng-multiselect-dropdown class="report-multiselect form-control"
                                        [placeholder]="'--select groups--'" [data]="listOfGroups"
                                        [(ngModel)]="selectedListOfGroups" [settings]="mDdlGroupsSettings"
                                        formControlName="ddlGroups"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlGroups'].errors}"
                                        (onSelect)="onGroupSelect($event)" (onDeSelect)="onGroupSelect($event)"
                                        (onSelectAll)="onGroupSelectAll($event,'S')"
                                        (onDeSelectAll)="onGroupSelectAll($event,'D')">
                                    </ng-multiselect-dropdown>
                                    <div *ngIf="submitted && f['ddlGroups'].errors" class="invalid-feedback">
                                        <div *ngIf="f['ddlGroups'].errors['required']">Group is required</div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-3 px-1 py-1 multiselect-absolute">
                                    <ng-multiselect-dropdown class="report-multiselect form-control"
                                        [placeholder]="'--select physicians--'" [data]="listOfPhysicians"
                                        [(ngModel)]="selectedListOfPhysicians" [settings]="mDdlPhysicianSettings"
                                        formControlName="ddlPhysician"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlPhysician'].errors}">
                                    </ng-multiselect-dropdown>
                                    <div *ngIf="submitted && f['ddlPhysician'].errors" class="invalid-feedback">
                                        <div *ngIf="f['ddlPhysician'].errors['required']">Physician is required</div>
                                    </div>
                                </div>
                            </div>
                            <div class="row my-1 mx-auto">
                                <div class="col-12 col-md-3 px-1 py-1">
                                    <input id="encounterSeenDateFrom" style="height: 90%" type="date"
                                        class="form-control" formControlName="encounterSeenDateFrom"
                                        title="Encounter Seen Date From"
                                        [ngClass]="{ 'is-invalid': submitted && f['encounterSeenDateFrom'].errors}">
                                    <div *ngIf="submitted && f['encounterSeenDateFrom'].errors"
                                        class="invalid-feedback">
                                        <div *ngIf="f['encounterSeenDateFrom'].errors['required']">Encounter Seen Date
                                            From is required</div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-3 px-1 py-1">
                                    <input id="encounterSeenDateTo" style="height: 90%" type="date" class="form-control"
                                        formControlName="encounterSeenDateTo" title="Encounter Seen Date To"
                                        [ngClass]="{ 'is-invalid': submitted && f['encounterSeenDateTo'].errors}">
                                    <div *ngIf="submitted && f['encounterSeenDateTo'].errors" class="invalid-feedback">
                                        <div *ngIf="f['encounterSeenDateTo'].errors['required']">Encounter Seen Date To
                                            is required</div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-3 px-1 py-1">
                                    <select id="ddlBill" style="height: 90%" class="form-control custom-control"
                                        formControlName="ddlBill">
                                        <option value="">All</option>
                                        <option value="BILLED">Billed</option>
                                        <option value="UNBILLED">Unbilled</option>
                                    </select>
                                </div>
                                <div class="col-12 px-2 py-1"
                                    [ngClass]="{'col-md-1': this.listOfReports.length, 'col-md-2': !this.listOfReports.length}">
                                    <button class="btn btn-outline-info px-2 btn-block" type="submit"
                                        (click)="filterReport()">
                                        <i class="fa-filter fas font-size-13"></i>
                                        Filter
                                    </button>
                                </div>
                                <div *ngIf="this.listOfReports.length>0" class="col-12 col-md-1 px-1 py-1 text-center">
                                    <img alt=' ' (keyup)="exportAsXLSX()" (click)="exportAsXLSX()" class="ml-2 shadow"
                                        [src]="img1" style="width:40px;cursor:pointer;" title="Export Excel">
                                </div>
                            </div>

                            <div class="row my-1 mx-auto align-items-center">
                                <div class="col-10 col-md-7 px-1 py-1">
                                    <div class="input-group">
                                        <input type="text" class="form-control small" maxlength="1000"
                                            placeholder="Search for..." aria-label="Search"
                                            aria-describedby="basic-addon2" [(ngModel)]="searchByName"
                                            [ngModelOptions]="{standalone: true}" (keyup)="search(searchByName)">
                                        <div class="input-group-append">
                                            <button class="bg-gradient-light btn text-dark" type="button">
                                                <i class="fas fa-search fa-sm"></i>
                                            </button>
                                        </div>
                                    </div>

                                </div>
                                <div class="col-10 col-md-3 px-1 py-1 text-center">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="removedFromBilligQueue"
                                            value="removedFromBilligQueue" formControlName="removedFromBilligQueue">
                                        <label for="removedFromBilligQueue"
                                            class="form-check-label text-white small">Get Patients Removed From Billing
                                            Queue Only
                                        </label>
                                    </div>
                                </div>
                                <div class="col-10 col-md-2 px-1 py-1 text-center">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="discharged"
                                            value="discharged" formControlName="discharged">
                                        <label for="discharged" class="form-check-label text-white small">Get Patients
                                            Discharged Only
                                        </label>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <!-- Card Body -->
                <div class="card-body px-2 py-1">
                    <div class="row mx-0">
                        <div class="col-12 overflow-auto px-0">
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered table-hover my-auto">
                                    <tr style="font-size:14px;">
                                        <th class="text-center pb-0 cursor-pointer" (keyup)="sortColumn('patienT_NAME')"
                                            (click)="sortColumn('patienT_NAME')">
                                            Patient
                                            <span class="float-right" tooltip="Sort By Patient"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'patienT_NAME' && orderByPatient == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'patienT_NAME' && orderByPatient == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'patienT_NAME'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer" (keyup)="sortColumn('dob')"
                                            (click)="sortColumn('dob')">
                                            DOB
                                            <span class="float-right" tooltip="Sort By dob"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'dob' && orderByPatientMRNDOB == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'dob' && orderByPatientMRNDOB == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'dob'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer"
                                            (keyup)="sortColumn('patientaccountnumber')"
                                            (click)="sortColumn('patientaccountnumber')">
                                            Account#
                                            <span class="float-right" tooltip="Sort By Account#"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'patientaccountnumber' && orderByAccount == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'patientaccountnumber' && orderByAccount == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'patientaccountnumber'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer"
                                            (keyup)="sortColumn('physiciaN_NAME')"
                                            (click)="sortColumn('physiciaN_NAME')">
                                            Physician
                                            <span class="float-right" tooltip="Sort By Physician"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'physiciaN_NAME' && orderByPhysician == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'physiciaN_NAME' && orderByPhysician == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'physiciaN_NAME'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0">
                                            Final CPT
                                        </th>
                                        <th class="text-center pb-0">
                                            Final ICD
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer" (keyup)="sortColumn('createdDate')"
                                            (click)="sortColumn('createdDate')">
                                            Encounter Created Date
                                            <span class="float-right" tooltip="Sort By Encounter Created Date"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'createdDate' && orderBycreatedDate == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'createdDate' && orderBycreatedDate == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'createdDate'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>

                                        <th class="text-center pb-0 cursor-pointer"
                                            (keyup)="sortColumn('encounterseendate')"
                                            (click)="sortColumn('encounterseendate')">
                                            Encounter Seen Date
                                            <span class="float-right" tooltip="Sort By Encounter Seen Date"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'encounterseendate' && orderByEncounterSeenDate == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'encounterseendate' && orderByEncounterSeenDate == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'encounterseendate'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0">
                                            Auditor Audit Required
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer"
                                            (keyup)="sortColumn('auditorAuditRequired')"
                                            (click)="sortColumn('auditorAuditRequired')">
                                            Auditor Coded
                                            <span class="float-right" tooltip="Sort By Auditor Coded"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'auditorAuditRequired' && orderByAuditorAuditRequired == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'auditorAuditRequired' && orderByAuditorAuditRequired == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'auditorAuditRequired'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer"
                                            (keyup)="sortColumn('auditorAuditDate')"
                                            (click)="sortColumn('auditorAuditDate')">
                                            Auditor Coded Date
                                            <span class="float-right" tooltip="Sort By Auditor Coded Date"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'auditorAuditDate' && orderByAuditorAuditDate == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'auditorAuditDate' && orderByAuditorAuditDate == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'auditorAuditDate'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer"
                                            (keyup)="sortColumn('coordinatorApprovalRequired')"
                                            (click)="sortColumn('coordinatorApprovalRequired')">
                                            Coordinator Approval Required
                                            <span class="float-right" tooltip="Sort By Coordinator Approved Required"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'coordinatorApprovalRequired' && orderByCoordinatorApprovalRequired == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'coordinatorApprovalRequired' && orderByCoordinatorApprovalRequired == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'coordinatorApprovalRequired'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer"
                                            (keyup)="sortColumn('coordinatorApproval')"
                                            (click)="sortColumn('coordinatorApproval')">
                                            Coordinator Approved
                                            <span class="float-right" tooltip="Sort By Coordinator Approved"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'coordinatorApproval' && orderByCoordinatorApproval == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'coordinatorApproval' && orderByCoordinatorApproval == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'coordinatorApproval'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer"
                                            (keyup)="sortColumn('coordinatorApprovedDate')"
                                            (click)="sortColumn('coordinatorApprovedDate')">
                                            Coordinator Approved Date
                                            <span class="float-right" tooltip="Sort By Coordinator Approved Date"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'coordinatorApprovedDate' && orderByCoordinatorApprovedDate == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'coordinatorApprovedDate' && orderByCoordinatorApprovedDate == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'coordinatorApprovedDate'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer"
                                            (keyup)="sortColumn('markeD_AS_BILL_BY')"
                                            (click)="sortColumn('markeD_AS_BILL_BY')">
                                            Mark As Billed By
                                            <span class="float-right" tooltip="Sort By Mark As Billed By"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'markeD_AS_BILL_BY' && orderByMarkeD_AS_BILL_BY == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'markeD_AS_BILL_BY' && orderByMarkeD_AS_BILL_BY == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'markeD_AS_BILL_BY'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer"
                                            (keyup)="sortColumn('markeD_AS_BILLED_DATE')"
                                            (click)="sortColumn('markeD_AS_BILLED_DATE')">
                                            Mark As Billed On
                                            <span class="float-right" tooltip="Sort By Mark As Billed On"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'markeD_AS_BILLED_DATE' && orderByMarkeD_AS_BILLED_DATE == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'markeD_AS_BILLED_DATE' && orderByMarkeD_AS_BILLED_DATE == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'markeD_AS_BILLED_DATE'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer"
                                            (keyup)="sortColumn('recorD_REMOVED_BY')"
                                            (click)="sortColumn('recorD_REMOVED_BY')">
                                            Removed From Billing Queue By
                                            <span class="float-right" tooltip="Sort By Removed From Billing Queue By"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'recorD_REMOVED_BY' && orderByRecorD_REMOVED_BY == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'recorD_REMOVED_BY' && orderByRecorD_REMOVED_BY == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'recorD_REMOVED_BY'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer"
                                            (keyup)="sortColumn('recorD_REMOVED_DATE')"
                                            (click)="sortColumn('recorD_REMOVED_DATE')">
                                            Removed From Billing Queue On
                                            <span class="float-right" tooltip="Sort By Removed From Billing Queue On"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'recorD_REMOVED_DATE' && orderByRecorD_REMOVED_DATE == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'recorD_REMOVED_DATE' && orderByRecorD_REMOVED_DATE == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'recorD_REMOVED_DATE'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer" (keyup)="sortColumn('deleted_On')"
                                            (click)="sortColumn('deleted_On')">
                                            Deleted On
                                            <span class="float-right" tooltip="Sort By Deleted On"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'deleted_On' && orderBydeleted_On == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'deleted_On' && orderBydeleted_On == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'deleted_On'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer" (keyup)="sortColumn('deleted_By')"
                                            (click)="sortColumn('deleted_By')">
                                            Deleted By
                                            <span class="float-right" tooltip="Sort By Deleted By"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'deleted_By' && orderBydeleted_By == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'deleted_By' && orderBydeleted_By == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'deleted_By'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer"
                                            (keyup)="sortColumn('deleted_Reason')"
                                            (click)="sortColumn('deleted_Reason')">
                                            Deleted Reason
                                            <span class="float-right" tooltip="Sort By Deleted Reason"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'deleted_Reason' && orderBydeleted_Reason == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'deleted_Reason' && orderBydeleted_Reason == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'deleted_Reason'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer" (keyup)="sortColumn('deleted_Role')"
                                            (click)="sortColumn('deleted_Role')">
                                            Deleted Role
                                            <span class="float-right" tooltip="Sort By Deleted Role"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'deleted_Role' && orderBydeleted_Role == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'deleted_Role' && orderBydeleted_Role == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'deleted_Role'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                    </tr>
                                    <tr
                                        *ngFor="let item of listOfReports| paginate: { itemsPerPage: 8, currentPage: p}">
                                        <td>{{item.patienT_NAME}}</td>
                                        <td>{{item.dob}}</td>
                                        <td>{{item.patientaccountnumber}}</td>
                                        <td>{{item.physiciaN_NAME}}</td>
                                        <td class="width-10per">{{item.listOfCpts[0]}}
                                            <span *ngIf="item.listOfCpts.length>1"
                                                style="color:#191414;font-size: 10px;">
                                                and {{item.listOfCpts.length-1}} more
                                            </span>
                                        </td>
                                        <td class="width-11per">{{item.listOfIcds[0]}}
                                            <span *ngIf="item.listOfIcds.length>1"
                                                style="color:#191414;font-size: 10px;">
                                                and {{item.listOfIcds.length-1}} more
                                            </span>
                                        </td>
                                        <td>{{item.createdDate }}</td>
                                        <td>{{item.encounterseendate}}</td>
                                        <td>{{item.auditorAuditRequired}}</td>
                                        <td>{{item.auditorAudit}}</td>
                                        <td>{{item.auditorAuditDate}}</td>
                                        <td>{{item.coordinatorApprovalRequired}}</td>
                                        <td>{{item.coordinatorApproval}}</td>
                                        <td>{{item.coordinatorApprovedDate}}</td>
                                        <td>{{item.markeD_AS_BILL_BY}}</td>
                                        <td>{{item.markeD_AS_BILLED_DATE}}</td>
                                        <td>{{item.recorD_REMOVED_BY}}</td>
                                        <td>{{item.recorD_REMOVED_DATE}}</td>
                                        <td>{{item.deleted_On}}</td>
                                        <td>{{item.deleted_By}}</td>
                                        <td>{{item.deleted_Reason}}</td>
                                        <td>{{item.deleted_Role}}</td>
                                    </tr>
                                    <tr>
                                        <td colspan="17" class="m-0 p-0" style="background: white !important;">
                                            <pagination-controls previousLabel="" nextLabel=""
                                                (pageChange)="p = $event"></pagination-controls>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                </div>

            </div>
        </div>
        <!-- Area Chart Ends -->

    </div>


</div>
<!-- Begin Page Content Ends -->