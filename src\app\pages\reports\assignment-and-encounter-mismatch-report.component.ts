import { Component, OnInit } from '@angular/core';
import { CommonService } from 'src/app/services/common/common.service';
import { ReportsService } from 'src/app/services/reports/reports.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { AppComponent } from 'src/app/app.component';
import { ExcelServices } from 'src/app/services/common/ExcelService';
import { DatePipe } from '@angular/common';
declare let $: any;

@Component({
  selector: 'app-assignment-and-encounter-mismatch-report',
  templateUrl: './assignment-and-encounter-mismatch-report.component.html',
  styles: []
})
export class AssignmentAndEncounterMismatchReportComponent implements OnInit {
  public listOfFacilities: Array<any> = [];
  public listOfPhysicians: Array<any> = [];
  public listOfReports: Array<any> = [];
  public listOfReportsInitial: Array<any> = [];
  public FilterForm: FormGroup;
  public request: any = {};
  public submitted: boolean = false;
  public p: number;
  public searchByName: string;
  public searchMissMatchedByName: string;
  public selectedYear: string;
  public lastRefreshDate: string;
  public isFaiclityAccess: boolean = true;
  public listOfYears: Array<any> = [];
  public listOfMissMatchedEncounters:Array<any>=[];
  public listOfMissMatchedEncountersInitial: Array<any> = [];
  public selectedResonArray: Array<string> = [];
  public selectedRequest: any = {};
  public userAccess: any = {};
  public listOfAccountNumbers: any = {};
  public listOfAssignmentBy:Array<any> = [];
  orderBy = 'desc';
  orderByFacilityName = 'desc';
  orderByPhysician_name = 'desc';
  sortColumnBy = 'physician_name';
  orderByAccountNumber = 'desc';
  orderByPhysicianname = 'desc';
  orderByAssignmentDate = 'desc';
  orderByAssignmentBy = 'desc';
  sortMMColumnBy = 'Account_Number';
  sortCMColumnBy = 'Account_Number';
  popupTitle="";
  public timeout: any = null;
  public encounterIDbyUser:any;
  public DetailFilterForm: FormGroup;
  public startDate: any;
  public endDate: any;
  public img1:string='.././../../assets/img/excel.png';

  constructor(private readonly reportsServ: ReportsService, private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService,
    private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly excelService: ExcelServices,private readonly datePipe: DatePipe) { }


  ngOnInit(): void {
    this.getYears();
    let currentYear = new Date().getFullYear();
    this.appComp.loadPageName('Assignment And Encounter Mismatch Report ', 'reportsTab');
    this.selectedYear=`${currentYear}`;
    this.getFacilities();
    this.FilterForm = this.fb.group({
      ddlFacility: ['All', Validators.required],
      ddlPhysician: ['All', Validators.required],      
      ddlYear: [`${currentYear}`]
    });
    this.DetailFilterForm=this.fb.group({
      ddlAccountNumber: ['All', Validators.required],
      ddlAssignmentBy: ['All', Validators.required],      
      AssignmentDate: [`${currentYear}`]
    });
    this.userAccess = this.appComp.userAccess;
   
  }
  getYears() {
    let max = new Date().getFullYear(),
    min = max - 10;
    let years:any[] = [];
    for (let i = min; i <= max; i++) {

      years.push(i);
    }
    this.listOfYears = years;
  }
  get f() { return this.FilterForm.controls; }
  get g() { return this.DetailFilterForm.controls; }
  getFacilities() {
    this.commonServ.getFacilities().subscribe((p: any) => {
      this.listOfFacilities = p;
      if(this.listOfFacilities.length>0)
      {
        this.FilterForm.get('ddlFacility')?.setValue(this.listOfFacilities[0].facilityName);
      }     
      this.request.FACILITY_Name = this.encrDecr.set(this.FilterForm.value.ddlFacility);
      this.request.PHYSICIAN_NAME = this.encrDecr.set($('#ddlPhysician option:selected').text().trim());      
      this.request.YEAR = this.encrDecr.set(this.FilterForm.value.ddlYear);
      this.getReportBySelection(this.request);
    }, error => { console.error(error.status); });
  }

  getPhysiciansByFacility(facillity) {
    this.commonServ.startLoading();
    this.commonServ.getPhysiciansByFacility(facillity).subscribe((p: any) => {
      this.commonServ.stopLoading();
      this.listOfPhysicians = p;
    }, error => {
      this.commonServ.stopLoading();
    });
  }
  filterReport() {
    this.submitted = true;
    if (this.FilterForm.invalid) {
      return;
    }
    this.request.FACILITY_Name = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.PHYSICIAN_NAME = this.encrDecr.set($('#ddlPhysician option:selected').text().trim());
    this.request.YEAR = this.encrDecr.set(this.FilterForm.value.ddlYear);
    this.getReportBySelection(this.request);
  }

  filterReportDetails()
  {
    let accountNumber = this.DetailFilterForm.value.ddlAccountNumber;
    let assignmentBy = this.DetailFilterForm.value.ddlAssignmentBy;
    let assignmentDate =  this.datePipe.transform(this.DetailFilterForm.value.AssignmentDate, 'MM/dd/yyyy') ;
    let filterData = this.listOfMissMatchedEncountersInitial;
    if(accountNumber && accountNumber!="All")
    {
      filterData = this.filterByValue(filterData, accountNumber)
    }
    if(assignmentBy && assignmentBy!="All")
    {
      filterData = this.filterByValue(filterData, assignmentBy)
    }
    if(assignmentDate)
    {
      filterData = this.filterByValue(filterData, assignmentDate)
    }
    this.listOfMissMatchedEncounters = filterData;
  }

  getReportBySelection(request) {
    this.commonServ.startLoading();
    this.selectedYear = this.FilterForm.value.ddlYear;
    this.reportsServ.getMissMatchedEncountersReportData(request).subscribe((p: any) => { 
      this.commonServ.stopLoading();
      if (p.length > 0) {
        this.listOfReports = JSON.parse(JSON.stringify(p));
        this.listOfReportsInitial = JSON.parse(JSON.stringify(p));
        this.listOfReports.forEach(report => {
          let ajan: number = report.avjan;
          report.ajan = ajan;

          let afeb: number = report.avfeb;
          report.afeb = afeb;

          let amarch: number = report.avmarch;
          report.amarch = amarch;

          let aapril: number = report.avapril;
          report.aapril = aapril;

          let amay: number = report.avmay;
          report.amay = amay;

          let ajune: number = report.avjune;
          report.ajune = ajune;

          let ajuly: number = report.avjuly;
          report.ajuly = ajuly;

          let aaug: number = report.avaug;
          report.aaug = aaug;

          let asep: number = report.avsep;
          report.asep = asep;

          let aoct: number = report.avoct;
          report.aoct = aoct;

          let anov: number = report.avnov;
          report.anov = anov;

          let adec: number = report.avdec;
          report.adec = adec;

          let cjan: number = report.cvjan;
          report.cjan = cjan;

          let cfeb: number = report.cvfeb;
          report.cfeb = cfeb;

          let cmarch: number = report.cvmarch;
          report.cmarch = cmarch;

          let capril: number = report.cvapril;
          report.capril = capril;

          let cmay: number = report.cvmay;
          report.cmay = cmay;

          let cjune: number = report.cvjune;
          report.cjune = cjune;

          let cjuly: number = report.cvjuly;
          report.cjuly = cjuly;

          let caug: number = report.cvaug;
          report.caug = caug;

          let csep: number = report.cvsep;
          report.csep = csep;

          let coct: number = report.cvoct;
          report.coct = coct;

          let cnov: number = report.cvnov;
          report.cnov = cnov;

          let cdec: number = report.cvdec;
          report.cdec = cdec;

          report.janColor = ajan < report.jan ? 'unapproved_count' : 'approved_count';
          report.febColor = afeb < report.feb ? 'unapproved_count' : 'approved_count';
          report.marchColor = amarch < report.march ? 'unapproved_count' : 'approved_count';
          report.aprilColor = aapril < report.april ? 'unapproved_count' : 'approved_count';
          report.mayColor = amay < report.may ? 'unapproved_count' : 'approved_count';
          report.juneColor = ajune < report.june ? 'unapproved_count' : 'approved_count';
          report.julyColor = ajuly < report.july ? 'unapproved_count' : 'approved_count';
          report.augColor = aaug < report.aug ? 'unapproved_count' : 'approved_count';
          report.sepColor = asep < report.sep ? 'unapproved_count' : 'approved_count';
          report.octColor = aoct < report.oct ? 'unapproved_count' : 'approved_count';
          report.novColor = anov < report.nov ? 'unapproved_count' : 'approved_count';
          report.decColor = adec < report.dec ? 'unapproved_count' : 'approved_count';

        });
      }
      else {
        this.listOfReports = [];
      }
      this.isFaiclityAccess = true;
      this.request = {};
      this.commonServ.stopLoading();
      }, error => {
      this.commonServ.stopLoading();
    
    });
    }

    exportAsXLSX(): void {
      let download = confirm("Do you want download?");
  
      if (download) {
        let fileName = "";
        fileName = "ENCOUNTER_MISMATCH_REPORT_" + this.FilterForm.value.ddlFacility + '_' + $('#ddlPhysician option:selected').text() + '_' + $('#ddlYear option:selected').text();
        this.excelService.exportAsExcelFile(this.listOfReports, fileName);
      }
    }
  search(searchKey) {
    clearTimeout(this.timeout);
    
    this.timeout = setTimeout(() => {
      if (this.listOfReports.length)
        this.listOfReports = this.filterByValue(this.listOfReportsInitial, searchKey)
    }, 2000);
  
  }

  searchMissMatched(searchKey) {
    clearTimeout(this.timeout);
    
    this.timeout = setTimeout(() => {
      if (this.listOfMissMatchedEncountersInitial.length)
        this.listOfMissMatchedEncounters = this.filterByValue(this.listOfMissMatchedEncountersInitial, searchKey)
    }, 2000);
  
  }
  
modelClose()
{
  this.selectedRequest = null;
}

sortColumn(columnBy) {
  this.sortColumnBy = columnBy;

  if (columnBy == 'facilitY_Name') {
    this.orderByFacilityName = this.orderByFacilityName == 'asc' ? 'desc' : 'asc';
    this.orderBy = this.orderByFacilityName;
  }
  else if (columnBy == 'physiciaN_NAME') {
    this.orderByPhysician_name = this.orderByPhysician_name == 'asc' ? 'desc' : 'asc';
    this.orderBy = this.orderByPhysician_name;
  }
  
  this.listOfReports = this.sortOrderBy(this.listOfReports, columnBy, this.orderBy)
}

sortColumnMisMatchList(columnBy) {
  this.sortMMColumnBy = columnBy;

  if (columnBy == 'account_Number') {
    this.orderByAccountNumber = this.orderByAccountNumber == 'asc' ? 'desc' : 'asc';
    this.orderBy = this.orderByAccountNumber;
  }
  else if (columnBy == 'patient_Name') {
    this.orderByFacilityName = this.orderByFacilityName == 'asc' ? 'desc' : 'asc';
    this.orderBy = this.orderByFacilityName;
  }
  else if (columnBy == 'assignmentDate') {
    this.orderByAssignmentDate = this.orderByAssignmentDate == 'asc' ? 'desc' : 'asc';
    this.orderBy = this.orderByAssignmentDate;
  }
  else if (columnBy == 'assignmentBy') {
    this.orderByAssignmentBy = this.orderByAssignmentBy == 'asc' ? 'desc' : 'asc';
    this.orderBy = this.orderByAssignmentBy;
  }
  
  this.listOfMissMatchedEncounters = this.sortOrderBy(this.listOfMissMatchedEncounters, columnBy, this.orderBy)
}


sortOrderBy(list, sortBy, sortOrder) {
  let newArray = [];
  this.p = 1;
  if (sortOrder == 'asc') {
    newArray = list.sort(
      function (a, b) {
        let sortNest: number = (b[sortBy] > a[sortBy]) ? -1 : 0;
        let sort: number = (a[sortBy] > b[sortBy]) ? 1 : sortNest;
        return sort;
      }
    );
  }
  else if (sortOrder == 'desc') {
    newArray = list.sort(
      function (a, b) {
        let sortNest: number = (b[sortBy] > a[sortBy]) ? 1 : 0;
        let sort: number = (a[sortBy] > b[sortBy]) ? -1 : sortNest;
        return sort;
      });
  }
  return newArray;
}

filterByValue(array, string) {
  return array.filter(o => {
    return Object.keys(o).some(k => {
      if (typeof o[k] === 'string')
        return o[k].toLowerCase().includes(string.toLowerCase());
    });
  });
}
reasonChange(event)
{ 
  if(event.target.value == "Duplicate")
  {
    this.encounterIDbyUser =-1;
  }
  else{
    this.encounterIDbyUser=null;
  }
}
getMissMatchedEncountersDetails(item,year, month) {  
  this.searchMissMatchedByName="";
  this.popupTitle="Mismatched Encounters";
  this.DetailFilterForm.reset();

  this.commonServ.startLoading();
    let request = {
      YEAR:this.encrDecr.set(year),
      MONTH:this.encrDecr.set(month),
      FACILITY_Name:this.encrDecr.set(item.facilitY_Name),
      PHYSICIAN_NAME:this.encrDecr.set(item.physiciaN_NAME)
    }
    this.selectedRequest=request;
    this.reportsServ.getMissMatchedEncountersReportDetails(request).subscribe((p: any) => {
      this.commonServ.stopLoading();
      this.listOfMissMatchedEncounters = p;
      this.listOfMissMatchedEncountersInitial = JSON.parse(JSON.stringify(p));

 

      this.listOfAccountNumbers = this.listOfMissMatchedEncounters.map((item) => item.account_Number) .filter(
                    (value, index, current_value) => current_value.indexOf(value) === index
                );
      this.listOfAssignmentBy =    this.listOfMissMatchedEncounters.map((item) => item.assignmentBy) .filter(
        (value, index, current_value) => current_value.indexOf(value) === index
    );      
    this.startDate = this.datePipe.transform(new Date(year, month-1, 1), 'yyyy-MM-dd');
    this.endDate = this.datePipe.transform(new Date(year, month , 0), 'yyyy-MM-dd');
    this.DetailFilterForm.get('AssignmentDate')?.setValue('');
    this.DetailFilterForm.get('ddlAccountNumber')?.setValue('All');
    this.DetailFilterForm.get('ddlAssignmentBy')?.setValue('All');
    
      this.listOfMissMatchedEncountersInitial.forEach(element => {
        let toDate = new Date(element.assignmentDate);
        element.assignmentDate=(toDate.getMonth()+1)+"/"+(toDate.getDate())+"/"+toDate.getFullYear();
      });
      $("#mdlMissEnvs").modal('show');
    }, error => {
      this.commonServ.stopLoading();
    });
}          


getCreatedEncountersDetails(item,year, month) {  
  this.searchMissMatchedByName="";
  this.popupTitle="Created Encounters";
  this.DetailFilterForm.reset();
 
  this.commonServ.startLoading();
    let request = {
      YEAR:this.encrDecr.set(year),
      MONTH:this.encrDecr.set(month),
      FACILITY_Name:this.encrDecr.set(item.facilitY_Name),
      PHYSICIAN_NAME:this.encrDecr.set(item.physiciaN_NAME)
    }
    this.selectedRequest=request;
    this.reportsServ.getCreatedEncountersReportDetails(request).subscribe((p: any) => {
      this.commonServ.stopLoading();
      this.listOfMissMatchedEncounters = p;
      
      this.listOfMissMatchedEncountersInitial = JSON.parse(JSON.stringify(p));

      
      this.listOfAccountNumbers = this.listOfMissMatchedEncounters.map((item) => item.account_Number) .filter(
        (value, index, current_value) => current_value.indexOf(value) === index
    );
    this.listOfAssignmentBy =    this.listOfMissMatchedEncounters.map((item) => item.assignmentBy) .filter(
    (value, index, current_value) => current_value.indexOf(value) === index
    );      
    
    this.startDate = this.datePipe.transform(new Date(year, month-1, 1), 'yyyy-MM-dd');
    this.endDate = this.datePipe.transform(new Date(year, month , 0), 'yyyy-MM-dd');
    this.DetailFilterForm.get('AssignmentDate')?.setValue('');
    this.DetailFilterForm.get('ddlAccountNumber')?.setValue('All');
    this.DetailFilterForm.get('ddlAssignmentBy')?.setValue('All');
      this.listOfMissMatchedEncountersInitial.forEach(element => {
        let toDate = new Date(element.assignmentDate);
        element.assignmentDate=(toDate.getMonth()+1)+"/"+(toDate.getDate())+"/"+toDate.getFullYear();
      });
      $("#mdlMissEnvs").modal('show');
    }, error => {
      this.commonServ.stopLoading();
    });
} 

}
