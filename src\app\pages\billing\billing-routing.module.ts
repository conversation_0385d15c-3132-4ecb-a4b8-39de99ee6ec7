import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { MsalGuard } from '@azure/msal-angular';
import { AddPatientComponent } from '../common/add-patient.component';
import { SubmitMissingEncounterComponent } from '../common/submit-missing-encounter.component';
import { BillingComponent } from './billing.component';


const routes: Routes = [
  { path: 'index', component: BillingComponent, canActivate: [MsalGuard] },
  { path: 'submit-missing-encounter', component: SubmitMissingEncounterComponent, canActivate: [MsalG<PERSON>] },
  { path: "add-patient", component: AddPatientComponent, canActivate: [MsalG<PERSON>] },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class BillingRoutingModule { }
