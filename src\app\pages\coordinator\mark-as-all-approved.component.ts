import { Component, Input } from '@angular/core';
import { CoordinatorService } from 'src/app/services/coordinator/coordinator.service';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { CoordinatorviewEditComponent } from './coordinatorview-edit.component';

@Component({
  selector: 'app-mark-as-all-approved',
  templateUrl: './mark-as-all-approved.component.html'
})
export class MarkAsAllApprovedComponent {
  @Input() lisfOfGroupEncounters: Array<any> = [];
  @Input() PatientObject: any;
  public request: any = {};
  public encounterIds: string = "";
  public sendtointegrationSystem = "";
  constructor(private readonly coordServ: CoordinatorService, private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService, private readonly coordComp: CoordinatorviewEditComponent,
    private readonly toastr: ToastrService) { }

  confirmMarkAsAllApproved(lstEnvs, pObj) {
    let icdorcptcount: boolean = false;
    for (let item of lstEnvs) {
      if (item.listOfCpts.length == 0 || item.listOfIcds.length == 0) {
        icdorcptcount = true;
        break;
      }
    }
    if (icdorcptcount) {
      this.toastr.error('Atleast one CPT and ICD Codes required.', '', { timeOut: 1900 });
    }
    else {
      this.commonServ.startLoading();
      lstEnvs.forEach(x => {
        if (x.coordinatorApproved == "0")
          this.encounterIds = this.encounterIds + "," + x.encounteR_ID;
      });
      this.request.strEncounterID = this.encrDecr.set(this.encounterIds);
      this.encounterIds = "";
      this.request.status = this.encrDecr.set("1");
      this.request.CoordinatorStatus = this.encrDecr.set("Approved");
      this.coordServ.markAllAsUnApprovedOrApproved(this.request).subscribe((p: any) => {
        this.request = {};
        if (p > 0) {
          this.coordComp.getEncountersByPatient(pObj, false);
        }
        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
    }
  }

}
