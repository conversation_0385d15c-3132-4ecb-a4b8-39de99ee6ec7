<!-- Edit Patient Starts -->
<div class="modal fade" id="editPatient" tabindex="-1" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" [formGroup]="patientForm">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalCenterTitle">Edit Patient</h5>
                <i class="fa fa-times closePopup" data-dismiss="modal"></i>
            </div>
            <div class="modal-body">
                <div class="row mx-0 py-4">


                    <div class="col-4 py-2">
                        <span class="mr-2 text-dark">First Name</span>
                    </div>
                    <div class="col-8 py-2">
                        <input class="form-control input-border" type="text" formControlName="txtFirstName"
                            [(ngModel)]="patient.first_name">
                    </div>

                    <div class="col-4 py-2">
                        <span class="mr-2 text-dark">Last Name</span>
                    </div>
                    <div class="col-8 py-2">
                        <input class="form-control input-border disabled" type="text" formControlName="txtLastName"
                            [(ngModel)]="patient.last_name">
                    </div>

                    <div class="col-4 py-2">
                        <span class="mr-2 text-dark">Account Number</span>
                    </div>
                    <div class="col-8 py-2">
                        <input class="form-control input-border disabled" type="text" formControlName="txtAccountNo"
                            [(ngModel)]="patient.account_number">
                    </div>

                    <div class="col-4 py-2">
                        <span class="mr-2 text-dark">Patient MRN</span>
                    </div>
                    <div class="col-8 py-2">
                        <input class="form-control input-border disabled" type="text" formControlName="txtMRN"
                            [(ngModel)]="patient.patient_mrn">
                    </div>

                    <div class="col-4 py-2">
                        <span class="mr-2 text-dark">Date of Birth</span>
                    </div>
                    <div class="col-8 py-2">
                        <span class="form-control input-border disabled">{{patient.dob | date:
                            'MM/dd/yyyy'}}</span>
                    </div>

                    <div class="col-4 py-2">
                        <span class="mr-2 text-dark">Patient Class</span>
                    </div>
                    <div class="col-8 py-2">
                        <select formControlName="role" class="form-control"
                            [ngClass]="{ 'is-invalid': submitted && f['ddlPatientClass'].errors}"
                            formControlName="ddlPatientClass" [(ngModel)]="patient.patient_class">
                            <option [value]="null">Select Patient Class</option>
                            <option value="I"><span>InPatient</span></option>
                            <option value="O"><span>OutPatient</span></option>
                        </select>
                    </div>

                    <div class="col-4 py-2">
                        <span class="mr-2 text-dark">Facility Name</span>
                    </div>
                    <div class="col-8 py-2">
                        <input class="form-control input-border disabled" type="text" formControlName="ddlFacilityName"
                            [(ngModel)]="patient.facility_name">
                    </div>

                    <div class="col-4 py-2">
                        <span class="mr-2 text-dark">Admission Date</span>
                    </div>
                    <div class="col-8 py-2">
                        <ng-container *ngIf="patient.is_admission_date_editable==false; else admElse">
                            <mat-form-field>
                                <mtx-datetimepicker #datetimePicker5 [type]="type" [mode]="mode"
                                    [multiYearSelector]="multiYearSelector" [startView]="startView"
                                    [twelvehour]="twelvehour" [timeInterval]="timeInterval" [touchUi]="touchUi"
                                    [timeInput]="timeInput">
                                </mtx-datetimepicker>
                                <input [mtxDatetimepicker]="datetimePicker5" formControlName="txtAdmissionDate" matInput
                                    [(ngModel)]="patient.admit_datetime" class="form-control input-border disabled"
                                    [ngClass]="{ 'is-invalid': submitted && f['txtAdmissionDate'].errors}">
                                <mtx-datetimepicker-toggle [for]="datetimePicker5"
                                    matSuffix></mtx-datetimepicker-toggle>
                            </mat-form-field>
                        </ng-container>
                        <ng-template #admElse>
                            <input type="text" formControlName="txtAdmissionDate"
                                class="form-control input-border bg_color_gray" [(ngModel)]="patient.admit_datetime" />
                        </ng-template>
                    </div>

                    <div class="col-4 py-2">
                        <span class="mr-2 text-dark">Assign Patient To</span>
                    </div>
                    <div class="col-8 py-2">
                        <input class="form-control input-border" type="text" formControlName="txtAssignPatientTo"
                            [(ngModel)]="patient.physician_name">
                    </div>

                    <div class="col-4 py-2">
                        <span class="mr-2 text-dark">Department</span>
                    </div>
                    <div class="col-8 py-2">
                        <input class="form-control input-border disabled" type="text" formControlName="txtDepartment"
                            [(ngModel)]="patient.patient_location">
                    </div>

                    <div class="col-4 py-2">
                        <span class="mr-2 text-dark">Room Number</span>
                    </div>
                    <div class="col-8 py-2">
                        <input class="form-control input-border"
                            [ngClass]="{ 'is-invalid': submitted && f['txtRoomNo'].errors}" type="text"
                            formControlName="txtRoomNo" [(ngModel)]="patient.room_number">
                    </div>

                    <div class="col-4 py-2">
                        <span class="mr-2 text-dark">Bed Number</span>
                    </div>
                    <div class="col-8 py-2">
                        <input class="form-control input-border"
                            [ngClass]="{ 'is-invalid': submitted && f['txtBedNo'].errors}" type="text"
                            formControlName="txtBedNo" [(ngModel)]="patient.bed_number">
                    </div>

                    <div class="col-4 py-2">
                        <span class="mr-2 text-dark">SSN</span>
                    </div>
                    <div class="col-8 py-2">
                        <input class="form-control input-border" maxlength="9" type="text" formControlName="txtSSN"
                            [(ngModel)]="patient.ssn">
                        <div class="text-danger" *ngIf="submitted && f['txtSSN'].errors?.['maxLength']">Max length 9
                            characters</div>
                        <div class="text-danger" *ngIf="submitted && f['txtSSN'].errors?.['pattern']">SSN should be a
                            number</div>
                    </div>

                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary btn-sm" data-dismiss="modal">Close</button>
                <button class="btn btn-sm btn-outline-info float-right btn-style"
                    (click)="updatePatient(patient)">Submit</button>
            </div>
        </div>
    </div>
</div>
<!-- Edit Patient Ends -->