import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { UserManagementService } from 'src/app/services/user-management/userManagement.service';

@Component({
  selector: 'app-add-modify-role',
  templateUrl: './add-modify-role.component.html',
  styles: [
  ]
})
export class AddModifyRoleComponent implements OnInit {
  public userDetails: any = {};
  public selectedRole: any = null;
  public mDdlFacilitySettings: any = {};
  public mDdlGroupsSettings: any = {};
  public lstOfFacilities: Array<any> = [];
  public selectedLstOfFacilities: Array<any> = [];
  public lstOfGroups: Array<any> = [];
  public selectedLstOfGroups: Array<any> = [];
  public roleForm: FormGroup;
  public submitted: boolean = false;
  public groupValidationMessage: string = '';
  public filterObj: any = {};
  public kareoPhysicianName: string = '';
  public isKareoPhysicianGroup: boolean = false;
  public copylistofgrp: Array<any> = [];
  public adminModuleAccess: string = 'NO';
  constructor(private readonly appComp: AppComponent, private readonly userSer: UserManagementService, private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService,
    private readonly toastr: ToastrService, private readonly router: Router, private readonly fb: FormBuilder) { }

  ngOnInit(): void {
    this.appComp.loadPageName('Add/Modify Role', 'usermanagementTab');
    this.userDetails = history.state.user;
    this.filterObj = history.state.filterObj;
    this.adminModuleAccess = this.appComp.userAccess.adminModuleAccess;
    this.roleForm = this.fb.group({
      ddlRole: ['', Validators.required],
      ddlFacility: ['', Validators.required],
      ddlGroup: [''],
      txtKareoPhysicianName: ['']
    });
    this.mDdlFacilitySettings = {
      singleSelection: false,
      idField: 'facilityname',
      textField: 'facilityname',
      enableCheckAll: false,
      itemsShowLimit: 20,
      allowSearchFilter: true
    };
    this.mDdlGroupsSettings = {
      singleSelection: false,
      idField: 'group_id',
      textField: 'group_name',
      enableCheckAll: false,
      itemsShowLimit: 20,
      allowSearchFilter: true
    };
  }

  get f() { return this.roleForm.controls; }

  roleChange(role) {
    this.submitted = false;
    if (role) {
      this.commonServ.startLoading();
      let request: any = {
        user_role: this.encrDecr.set(role),
        user_email: this.encrDecr.set(this.userDetails.email_id)
      };
      this.userSer.getUserFacilitiesAndGroups(request).subscribe((p: any) => {
        this.lstOfFacilities = p.allFacilityList;
        this.selectedLstOfFacilities = p.selectedFacilityList;
        this.lstOfGroups = p.allGroupList;
        this.copylistofgrp = [...this.lstOfGroups];
        this.selectedLstOfGroups = p.selectedGroupList;

        if (role == 'PHYSICIAN') {
          this.kareoPhysicianName = p.kareoPhysician;
          if (this.selectedLstOfGroups.filter(x => x.integrationType == 'Kareo').length > 0) {
            this.isKareoPhysicianGroup = true;
          }
          else {
            this.isKareoPhysicianGroup = false;
          }

        }
        else {
          this.isKareoPhysicianGroup = false;
        }
        this.commonServ.stopLoading();
      }, error => {
        console.error(error.status);
        this.commonServ.stopLoading();
      });
    }
  }

  updateEvent() {
    this.roleForm.controls['ddlGroup'].updateValueAndValidity();
    this.submitted = true;
    if (this.roleForm.invalid || this.groupValidationMessage != '') {
      return;
    }

    this.commonServ.startLoading();

    if (!this.isKareoPhysicianGroup || this.roleForm.value.txtKareoPhysicianName == null || this.roleForm.value.txtKareoPhysicianName == '') {
      this.roleForm.get('txtKareoPhysicianName')?.setValue('');
    }

    let request: any = {
      UserEmailID: this.encrDecr.set(this.userDetails.email_id),
      Role: this.encrDecr.set(this.selectedRole),
      SelectedFacilities: this.selectedLstOfFacilities,
      SelectedGroups: this.selectedLstOfGroups,
      KareoPhysician: this.encrDecr.set(this.roleForm.value.txtKareoPhysicianName)
    };
    this.userSer.insertOrUpdateFacilitiesAndGroupsForUser(request).subscribe((p: any) => {
      this.toastr.success('Details Updated Successfully.', '', { timeOut: 2500 });
      this.getUserByUserId(this.userDetails.email_id);
      this.cancelEvent();
      this.submitted = false;
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  onFacilitySelectOrDeSelect(item: any, type) {
    if (this.selectedRole != 'USERMANAGEMENT') {
      this.commonServ.startLoading();
      let request: any = {
        SelectedFacilities: this.selectedLstOfFacilities
      };
      this.userSer.getGroupsByFacilities(request).subscribe((p: any) => {
        this.lstOfGroups = p.allGroupList;
        this.copylistofgrp = [...this.lstOfGroups];
        if (type == 'DeSelect') {
          this.selectedLstOfGroups = this.selectedLstOfGroups.filter(x => x.group_name.indexOf(item.facilityname) === -1);
        }

        this.checkKareoGroup();
        this.commonServ.stopLoading();
      }, error => {
        console.error(error.status);
        this.commonServ.stopLoading();
      });
    }
  }

  cancelEvent() {
    this.selectedRole = '';
    this.lstOfFacilities = [];
    this.selectedLstOfFacilities = [];
    this.lstOfGroups = [];
    this.selectedLstOfGroups = [];
    this.groupValidationMessage = '';
  }

  deassignRoleFacilityOrGroup(email_id, role, type, facility_name, group_name) {
    if (confirm('Do you want to delete this ' + type + '?')) {
      this.commonServ.startLoading();
      let request: any = {
        email_id: email_id,
        role: role,
        type: type,
        facility_name: facility_name,
        group_name: group_name
      }
      this.userSer.deassignRoleFacilityOrGroup(request).subscribe((p: any) => {
        this.toastr.success(type + ' Deleted Successfully.', '', { timeOut: 2500 });
        this.getUserByUserId(email_id);
        this.cancelEvent();
      }, error => {
        console.error(error.status);
        this.commonServ.stopLoading();
      });
    }
  }

  getUserByUserId(email_id) {
    this.userSer.getUserByUserId(email_id).subscribe((p: any) => {
      this.userDetails = p;
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  onGroupSelectOrDeSelect(item: any, type) {
    if (this.selectedRole == 'PHYSICIAN' || this.selectedRole == 'RESIDENT') {
      this.selectedLstOfFacilities.forEach(f => {
        let flrgrps = this.copylistofgrp.filter(g1 => g1.facilityName == f.facilityname);
        let selectedgrpCount = this.selectedLstOfGroups.filter(o1 => flrgrps.some(o2 => o1.group_name === o2.group_name));
        if (selectedgrpCount.length > 1) {
          this.groupValidationMessage = 'You can select only one group for a facility.';
        }
        else if (type == 'DeSelect') {
          this.groupValidationMessage = '';
        }
      });
    }

    this.checkKareoGroup();
  }

  checkKareoGroup() {

    if (this.selectedRole == 'PHYSICIAN') {
      let selectKareogrp: Array<any> = [];
      for (let i = this.lstOfGroups.length - 1; i >= 0; --i) {
        if (this.selectedLstOfGroups.find(x => x.group_id == this.lstOfGroups[i].group_id)) {
          selectKareogrp.push(this.lstOfGroups[i]);
        }
      }
      if (selectKareogrp.length > 0) {
        if (selectKareogrp.filter(x => x.integrationType == 'Kareo').length > 0) {
          this.isKareoPhysicianGroup = true;
        }
        else {
          this.isKareoPhysicianGroup = false;
        }
      }
    }
  }

  getUserAccesPermission(facility, facilityList) {
    if (this.adminModuleAccess == 'NO') {
      let flag = facilityList.find(x => x == facility);
      if (flag == undefined) {
        flag = false;
      }
      else {
        flag = true;
      }
      return flag;
    }
    else { return true }
  }

}
