export class EncounterModel {
autoposttobiller:string;
coordinatorApproved: string;
cptids: string;
discharge_Date: string;
encounteR_ID: string;
encounterseendate: string;
existing_Kareo_Batch_No: number;
existing_Kareo_ID: number;
factimezone: string;
group_Name: string;
icdids: string;
integrationType: string;
isAlertCheck: boolean;
isRemoveFromBillingQueue: number;
kareo_Batch_No: number;
kareo_ID: number;
lastmodifiedby: string;
lastmodifieddate: string;
listOfCpts: [];
listOfIcds: [];
markascoded: string;
markasseen: string;
notesCount: number;
physicianmailid: string;
posT_TO_BILLED_BY: string;
posttobiller: string;
sessionStatus: boolean;
sessionToken: string;
status: string;
statusCode: string;
statusMessage: string;
existing_RXNT_Batch_No: number;
existing_RXNT_ID: number;
RXNT_Batch_No: number;
RXNT_ID: number;
}