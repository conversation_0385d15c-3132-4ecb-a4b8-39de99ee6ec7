<div class="modal fade" id="discharge" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title" id="exampleModalCenterTitle">Update Discharge Date</h5>
            </div>
            <div class="modal-body" [formGroup]="disDateForm">
                <div class="row mx-auto">
                    <div class="col-12 col-md-9 p-1">
                        <input type="date" name="dischargeDate" id="dischargeDate" class="form-control"
                            formControlName="dischargeDate">
                        <div *ngIf="submitted && f['dischargeDate'].errors" class="text-danger">
                            <div *ngIf="f['dischargeDate'].errors['required']">Discharge Date is required</div>
                        </div>
                    </div>
                    <div class="col-12 col-md-3 p-1">
                        <button type="submit" class="btn btn-outline-info btn-block"
                            (click)="editDischargeDate(PatientObject)">Update</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>