<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new p-2" style="background: #0169ab;">
                    <div class="align-items-center row mx-auto">
                        <div class="col-12 px-0">

                            <div class="row my-1 mx-auto">
                                <div class="col-12 px-1 py-1"
                                    [ngClass]="{'col-md-8': this.listOfReports.length, 'col-md-9': !this.listOfReports.length}">
                                    <div class="input-group">
                                        <input type="text" class="form-control small" maxlength="1000"
                                            placeholder="Search for..." aria-label="Search"
                                            aria-describedby="basic-addon2" [(ngModel)]="searchByName"
                                            [ngModelOptions]="{standalone: true}">
                                        <div class="input-group-append">
                                            <button class="bg-gradient-light btn text-dark" type="button">
                                                <i class="fas fa-search fa-sm"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-3 px-1 py-1">
                                    <input type="text" name="dateRangeLable" ngxDaterangepickerMd [(ngModel)]="dateRangeLable" [showCustomRangeLabel]="true"
                                        [alwaysShowCalendars]="false" [ranges]="ranges" (change)="selectedDate($event)" 
                                        [showRangeLabelOnInput]="true" [showDropdowns]="true" title="Choose Date Range"
                                        placeholder="Choose Date Range" class="form-control btn-lavender" readonly />
                                </div>
                                <div *ngIf="this.listOfReports.length>0" class="col-12 col-md-1 px-1 py-1 text-center">
                                    <img alt=' ' (keyup)="exportAsXLSX()" (click)="exportAsXLSX()" class="ml-2 shadow"
                                        [src]="img1" style="width:40px;cursor:pointer;" title="Export Excel">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Card Body -->
                <div class="card-body px-2 py-1">
                    <div class="row mx-0">
                        <div class="col-12 px-0">

                            <table class="table table-striped table-bordered table-hover my-auto">
                                <tr>
                                    <th>Account No#</th>
                                    <th>Facility Name</th>
                                    <th>From</th>
                                    <th>Message</th>
                                    <th>Send Date</th>
                                </tr>
                                <tr
                                    *ngFor="let item of listOfReports|gridFilter:{patient_account_no:searchByName,facility_name:searchByName,from_name:searchByName,to_name:searchByName,send_date:searchByName}:false| paginate: { itemsPerPage: 25, currentPage: p}">
                                    <td>{{item.patient_account_no}}</td>
                                    <td>{{item.facility_name}}</td>
                                    <td>{{item.from_name}}</td>
                                    <td>{{item.message}}</td>
                                    <td>{{item.send_date}}</td>
                                </tr>
                                <tr *ngIf="submitted&&listOfReports.length==0">
                                    <td class="text-center" colspan="7">This facility hasn't been configured for
                                        Alerts!!</td>
                                </tr>
                                <tr>
                                    <td colspan="10" class="m-0 p-0" style="background: white !important;">
                                        <pagination-controls previousLabel="" nextLabel=""
                                            (pageChange)="p = $event"></pagination-controls>
                                    </td>
                                </tr>
                            </table>

                        </div>
                    </div>

                </div>

            </div>
        </div>
        <!-- Area Chart Ends -->
    </div>
</div>
<!-- Begin Page Content Ends -->
