<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new p-2" style="background: #0169ab;" [formGroup]="FilterForm">
                    <div class="align-items-center row mx-auto">
                        <div class="col-12 px-0">

                            <div class="row mt-1 mx-auto">
                                <div class="col-12 col-md-2 px-1 py-1">
                                    <select id="ddlFacility" class="form-control" formControlName="ddlFacility"
                                        (change)="getGroupsByFacility($any($event.target).value,true)"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                                        <option value="">---Select Facility---</option>
                                        <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                                            <span>{{s.facilityName}}</span>
                                        </option>
                                    </select>
                                    <div *ngIf="submitted && f['ddlFacility'].errors" class="invalid-feedback">
                                        <div *ngIf="f['ddlFacility'].errors['required']">Facility is required</div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-2 px-1 py-1">
                                    <select id="ddlPatientType" class="form-control custom-control"
                                        formControlName="ddlPatientType"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlPatientType'].errors}">
                                        <option value="">---Select Patient Type---</option>
                                        <option value="All">All</option>
                                        <option value="I">In Patient</option>
                                        <option value="O">Others</option>
                                    </select>
                                    <div *ngIf="submitted && f['ddlPatientType'].errors" class="invalid-feedback">
                                        <div *ngIf="f['ddlPatientType'].errors['required']">Patient Type is required
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-3 px-1 py-1 multiselect-absolute">
                                    <ng-multiselect-dropdown class="report-multiselect form-control"
                                        [placeholder]="'---Select Groups---'" [data]="listOfGroups"
                                        [(ngModel)]="selectedListOfGroups" [settings]="mDdlGroupsSettings"
                                        formControlName="ddlGroups"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlGroups'].errors}"
                                        (onSelect)="onGroupSelect($event)" (onDeSelect)="onGroupSelect($event)"
                                        (onSelectAll)="onGroupSelectAll($event,'S')"
                                        (onDeSelectAll)="onGroupSelectAll($event,'D')">
                                    </ng-multiselect-dropdown>
                                    <div *ngIf="submitted && f['ddlGroups'].errors" class="invalid-feedback">
                                        <div *ngIf="f['ddlGroups'].errors['required']">Group is required</div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-3 px-1 py-1">
                                    <ng-multiselect-dropdown class="report-multiselect form-control"
                                        [placeholder]="'---Select Physician---'" [data]="listOfPhysicians"
                                        [(ngModel)]="selectedListOfPhysicians" [settings]="mDdlPhysicianSettings"
                                        formControlName="ddlPhysician"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlPhysician'].errors}">
                                    </ng-multiselect-dropdown>
                                    <div *ngIf="submitted && f['ddlPhysician'].errors" class="invalid-feedback">
                                        <div *ngIf="f['ddlPhysician'].errors['required']">Physician is required</div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-1 px-1 py-1">
                                    <select id="ddlYear" class="form-control" formControlName="ddlYear">
                                        <option [value]="year" *ngFor="let year of listOfYears">
                                            <span>{{year}}</span>
                                        </option>
                                    </select>
                                    <div *ngIf="submitted && f['ddlYear'].errors" class="invalid-feedback">
                                        <div *ngIf="f['ddlYear'].errors['required']">Year is required</div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-1 px-1 py-1">
                                    <button class="btn btn-outline-info px-2 btn-block" type="submit"
                                        (click)="filterReport()">
                                        <i class="fa-filter fas font-size-13"></i>
                                        Filter
                                    </button>
                                </div>
                            </div>

                            <div class="row my-1 mx-auto align-items-center">
                                <div class="col-12 px-1 py-1 col-md-11">
                                    <div class="input-group">
                                        <input type="text" class="form-control small" maxlength="1000"
                                            placeholder="Search for..." aria-label="Search"
                                            aria-describedby="basic-addon2" [(ngModel)]="searchByName"
                                            [ngModelOptions]="{standalone: true}" (keyup)="search(searchByName)">
                                        <div class="input-group-append">
                                            <button class="bg-gradient-light btn text-dark" type="button">
                                                <i class="fas fa-search fa-sm"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div *ngIf="this.listOfReports.length>0" class="col-12 col-md-1 px-1 py-1 text-center">
                                    <img alt=' ' (keyup)="exportAsXLSX()" (click)="exportAsXLSX()" class="ml-2 shadow"
                                        [src]='img1' style="width:40px;cursor:pointer;" title="Export Excel">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Card Body -->
                <div class="card-body px-2 py-1">
                    <div class="row mx-0">
                        <div class="col-12 px-0 pull_right note_text">
                            <div class="display_inline approved_encounters">
                                <span><i class="fas fa-fw fa-circle"></i></span><span>Approved Encounters</span>
                            </div>
                            <div class="display_inline billed_encounters">
                                <span><i class="fas fa-fw fa-circle"></i></span><span>Billed Encounters</span>
                            </div>
                        </div>
                    </div>
                    <div class="row mx-0">
                        <div class="col-12 px-0">

                            <table class="table table-striped table-bordered table-hover billed_encounters_grid">
                                <thead>
                                    <tr class="text-center">
                                        <th class="text-center pb-0 cursor-pointer" (keyup)="sortColumn('facility')"
                                            (click)="sortColumn('facility')">
                                            Facility Name
                                            <span class="float-right" tooltip="Sort By Facility Name"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'facility' && orderByFacilityName == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'facility' && orderByFacilityName == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'facility'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer" (keyup)="sortColumn('physician')"
                                            (click)="sortColumn('physician')">
                                            Physician Name
                                            <span class="float-right" tooltip="Sort By Physician Name"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'physician' && orderByPhysician_name == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'physician' && orderByPhysician_name == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'physician'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th>Jan {{selectedYear}}</th>
                                        <th>Feb {{selectedYear}}</th>
                                        <th>Mar {{selectedYear}}</th>
                                        <th>Apr {{selectedYear}}</th>
                                        <th>May {{selectedYear}}</th>
                                        <th>Jun {{selectedYear}}</th>
                                        <th>Jul {{selectedYear}}</th>
                                        <th>Aug {{selectedYear}}</th>
                                        <th>Sep {{selectedYear}}</th>
                                        <th>Oct {{selectedYear}}</th>
                                        <th>Nov {{selectedYear}}</th>
                                        <th>Dec {{selectedYear}}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <ng-container *ngIf="isFaiclityAccess==true;else ElCase">
                                        <tr
                                            *ngFor="let item of listOfReports | paginate: { itemsPerPage: 10, currentPage: p} | gridFilter:{facility:searchByName,physician:searchByName}:false">
                                            <td>{{item.facility}}</td>
                                            <td>{{item.physician}}</td>
                                            <td>
                                                <div [ngClass]="item.grjan>item.ngjan?'unapproved_count':'approved_count'"
                                                    title="In Nextgen">
                                                    <span *ngIf="item.ngjan>0;else janEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,1,'GR')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,1,'GR')">{{item.ngjan}}</span>
                                                    </span>
                                                    <ng-template #janEls>
                                                        {{item.ngjan}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">
                                                    <span class='under-line-click' *ngIf="item.grjan>0;else grjanEls"
                                                        (keyup)="getMissMatchedEncounters(item,selectedYear,1,'GRAll')"
                                                        (click)="getMissMatchedEncounters(item,selectedYear,1,'GRAll')">{{item.grjan}}</span>
                                                    <ng-template #grjanEls>{{item.grjan}}</ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Nextgen">
                                                    <span *ngIf="item.ecjan !='0';else janEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,1,'NG')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,1,'NG')">{{item.ecjan}}</span>
                                                    </span>
                                                    <ng-template #janEcEls>
                                                        {{item.ecjan}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="deleted_count" title="Deleted in Grand Rounds">
                                                    <span *ngIf="item.dcjan>0;else dcjanEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,1,'DELETE')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,1,'DELETE')">{{item.dcjan}}</span>
                                                    </span>
                                                    <ng-template #dcjanEls>
                                                        {{item.dcjan}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                            <td>
                                                <div [ngClass]="item.grfeb>item.ngfeb?'unapproved_count':'approved_count'"
                                                    title="In Nextgen">
                                                    <span *ngIf="item.ngfeb>0;else febEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,2,'GR')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,2,'GR')">{{item.ngfeb}}</span>
                                                    </span>
                                                    <ng-template #febEls>
                                                        {{item.ngfeb}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">
                                                    <span class='under-line-click' *ngIf="item.grfeb>0;else grfebEls"
                                                        (keyup)="getMissMatchedEncounters(item,selectedYear,2,'GRAll')"
                                                        (click)="getMissMatchedEncounters(item,selectedYear,2,'GRAll')">{{item.grfeb}}</span>
                                                    <ng-template #grfebEls>{{item.grfeb}}</ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Nextgen">
                                                    <span *ngIf="item.ecfeb !='0';else febEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,2,'NG')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,2,'NG')">{{item.ecfeb}}</span>
                                                    </span>
                                                    <ng-template #febEcEls>
                                                        {{item.ecfeb}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="deleted_count" title="Deleted in Grand Rounds">
                                                    <span *ngIf="item.dcfeb>0;else dcfebEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,2,'DELETE')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,2,'DELETE')">{{item.dcfeb}}</span>
                                                    </span>
                                                    <ng-template #dcfebEls>
                                                        {{item.dcfeb}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                            <td>
                                                <div [ngClass]="item.grmar>item.ngmar?'unapproved_count':'approved_count'"
                                                    title="In Nextgen">
                                                    <span *ngIf="item.ngmar>0;else marEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,3,'GR')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,3,'GR')">{{item.ngmar}}</span>
                                                    </span>
                                                    <ng-template #marEls>
                                                        {{item.ngmar}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">
                                                    <span class='under-line-click' *ngIf="item.grmar>0;else grmarEls"
                                                        (keyup)="getMissMatchedEncounters(item,selectedYear,3,'GRAll')"
                                                        (click)="getMissMatchedEncounters(item,selectedYear,3,'GRAll')">{{item.grmar}}</span>
                                                    <ng-template #grmarEls>{{item.grmar}}</ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Nextgen">
                                                    <span *ngIf="item.ecmarch !='0';else marchEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,3,'NG')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,3,'NG')">{{item.ecmarch}}</span>
                                                    </span>
                                                    <ng-template #marchEcEls>
                                                        {{item.ecmarch}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="deleted_count" title="Deleted in Grand Rounds">
                                                    <span *ngIf="item.dcmar>0;else dcmarEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,3,'DELETE')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,3,'DELETE')">{{item.dcmar}}</span>
                                                    </span>
                                                    <ng-template #dcmarEls>
                                                        {{item.dcmar}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                            <td>
                                                <div [ngClass]="item.grapr>item.ngapr?'unapproved_count':'approved_count'"
                                                    title="In Nextgen">
                                                    <span *ngIf="item.ngapr>0;else aprilEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,4,'GR')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,4,'GR')">{{item.ngapr}}</span>
                                                    </span>
                                                    <ng-template #aprilEls>
                                                        {{item.ngapr}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">
                                                    <span class='under-line-click' *ngIf="item.grapr>0;else graprEls"
                                                        (keyup)="getMissMatchedEncounters(item,selectedYear,4,'GRAll')"
                                                        (click)="getMissMatchedEncounters(item,selectedYear,4,'GRAll')">{{item.grapr}}</span>
                                                    <ng-template #graprEls>{{item.grapr}}</ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Nextgen">
                                                    <span *ngIf="item.ecapril !='0';else aprilEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,4,'NG')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,4,'NG')">{{item.ecapril}}</span>
                                                    </span>
                                                    <ng-template #aprilEcEls>
                                                        {{item.ecapril}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="deleted_count" title="Deleted in Grand Rounds">
                                                    <span *ngIf="item.dcapr>0;else dcaprEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,4,'DELETE')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,4,'DELETE')">{{item.dcapr}}</span>
                                                    </span>
                                                    <ng-template #dcaprEls>
                                                        {{item.dcapr}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                            <td>
                                                <div [ngClass]="item.grmay>item.ngmay?'unapproved_count':'approved_count'"
                                                    title="In Nextgen">
                                                    <span *ngIf="item.ngmay>0;else mayEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,5,'GR')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,5,'GR')">{{item.ngmay}}</span>
                                                    </span>
                                                    <ng-template #mayEls>
                                                        {{item.ngmay}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">
                                                    <span class='under-line-click' *ngIf="item.grmay>0;else grmayEls"
                                                        (keyup)="getMissMatchedEncounters(item,selectedYear,5,'GRAll')"
                                                        (click)="getMissMatchedEncounters(item,selectedYear,5,'GRAll')">{{item.grmay}}</span>
                                                    <ng-template #grmayEls>{{item.grmay}}</ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Nextgen">
                                                    <span *ngIf="item.ecmay !='0';else mayEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,5,'NG')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,5,'NG')">{{item.ecmay}}</span>
                                                    </span>
                                                    <ng-template #mayEcEls>
                                                        {{item.ecmay}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="deleted_count" title="Deleted in Grand Rounds">
                                                    <span *ngIf="item.dcmay>0;else dcmayEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,5,'DELETE')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,5,'DELETE')">{{item.dcmay}}</span>
                                                    </span>
                                                    <ng-template #dcmayEls>
                                                        {{item.dcmay}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                            <td>
                                                <div [ngClass]="item.grjun>item.ngjun?'unapproved_count':'approved_count'"
                                                    title="In Nextgen">
                                                    <span *ngIf="item.ngjun>0;else juneEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,6,'GR')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,6,'GR')">{{item.ngjun}}</span>
                                                    </span>
                                                    <ng-template #juneEls>
                                                        {{item.ngjun}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">
                                                    <span class='under-line-click' *ngIf="item.grjun>0;else grjunEls"
                                                        (keyup)="getMissMatchedEncounters(item,selectedYear,6,'GRAll')"
                                                        (click)="getMissMatchedEncounters(item,selectedYear,6,'GRAll')">{{item.grjun}}</span>
                                                    <ng-template #grjunEls>{{item.grjun}}</ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Nextgen">
                                                    <span *ngIf="item.ecjune !='0';else juneEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,6,'NG')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,6,'NG')">{{item.ecjune}}</span>
                                                    </span>
                                                    <ng-template #juneEcEls>
                                                        {{item.ecjune}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="deleted_count" title="Deleted in Grand Rounds">
                                                    <span *ngIf="item.dcjun>0;else dcjunEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,6,'DELETE')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,6,'DELETE')">{{item.dcjun}}</span>
                                                    </span>
                                                    <ng-template #dcjunEls>
                                                        {{item.dcjun}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                            <td>
                                                <div [ngClass]="item.grjul>item.ngjul?'unapproved_count':'approved_count'"
                                                    title="In Nextgen">
                                                    <span *ngIf="item.ngjul>0;else julyEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,7,'GR')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,7,'GR')">{{item.ngjul}}</span>
                                                    </span>
                                                    <ng-template #julyEls>
                                                        {{item.ngjul}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">
                                                    <span class='under-line-click' *ngIf="item.grjul>0;else grjulEls"
                                                        (keyup)="getMissMatchedEncounters(item,selectedYear,7,'GRAll')"
                                                        (click)="getMissMatchedEncounters(item,selectedYear,7,'GRAll')">{{item.grjul}}</span>
                                                    <ng-template #grjulEls>{{item.grjul}}</ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Nextgen">
                                                    <span *ngIf="item.ecjuly !='0';else julyEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,7,'NG')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,7,'NG')">{{item.ecjuly}}</span>
                                                    </span>
                                                    <ng-template #julyEcEls>
                                                        {{item.ecjuly}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="deleted_count" title="Deleted in Grand Rounds">
                                                    <span *ngIf="item.dcjul>0;else dcjulEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,7,'DELETE')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,7,'DELETE')">{{item.dcjul}}</span>
                                                    </span>
                                                    <ng-template #dcjulEls>
                                                        {{item.dcjul}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                            <td>
                                                <div [ngClass]="item.graug>item.ngaug?'unapproved_count':'approved_count'"
                                                    title="In Nextgen">
                                                    <span *ngIf="item.ngaug>0;else augEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,8,'GR')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,8,'GR')">{{item.ngaug}}</span>
                                                    </span>
                                                    <ng-template #augEls>
                                                        {{item.ngaug}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">
                                                    <span class='under-line-click' *ngIf="item.graug>0;else graugEls"
                                                        (keyup)="getMissMatchedEncounters(item,selectedYear,8,'GRAll')"
                                                        (click)="getMissMatchedEncounters(item,selectedYear,8,'GRAll')">{{item.graug}}</span>
                                                    <ng-template #graugEls>{{item.graug}}</ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Nextgen">
                                                    <span *ngIf="item.ecaug !='0';else augEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,8,'NG')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,8,'NG')">{{item.ecaug}}</span>
                                                    </span>
                                                    <ng-template #augEcEls>
                                                        {{item.ecaug}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="deleted_count" title="Deleted in Grand Rounds">
                                                    <span *ngIf="item.dcaug>0;else dcaugEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,8,'DELETE')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,8,'DELETE')">{{item.dcaug}}</span>
                                                    </span>
                                                    <ng-template #dcaugEls>
                                                        {{item.dcaug}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                            <td>
                                                <div [ngClass]="item.grsep>item.ngsep?'unapproved_count':'approved_count'"
                                                    title="In Nextgen">
                                                    <span *ngIf="item.ngsep>0;else sepEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,9,'GR')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,9,'GR')">{{item.ngsep}}</span>
                                                    </span>
                                                    <ng-template #sepEls>
                                                        {{item.ngsep}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">
                                                    <span class='under-line-click' *ngIf="item.grsep>0;else grsepEls"
                                                        (keyup)="getMissMatchedEncounters(item,selectedYear,9,'GRAll')"
                                                        (click)="getMissMatchedEncounters(item,selectedYear,9,'GRAll')">{{item.grsep}}</span>
                                                    <ng-template #grsepEls>{{item.grsep}}</ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Nextgen">
                                                    <span *ngIf="item.ecsep !='0';else sepEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,9,'NG')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,9,'NG')">{{item.ecsep}}</span>
                                                    </span>
                                                    <ng-template #sepEcEls>
                                                        {{item.ecsep}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="deleted_count" title="Deleted in Grand Rounds">
                                                    <span *ngIf="item.dcsep>0;else dcsepEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,9,'DELETE')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,9,'DELETE')">{{item.dcsep}}</span>
                                                    </span>
                                                    <ng-template #dcsepEls>
                                                        {{item.dcsep}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                            <td>
                                                <div [ngClass]="item.groct>item.ngoct?'unapproved_count':'approved_count'"
                                                    title="In Nextgen">
                                                    <span *ngIf="item.ngoct>0;else octEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,10,'GR')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,10,'GR')">{{item.ngoct}}</span>
                                                    </span>
                                                    <ng-template #octEls>
                                                        {{item.ngoct}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">
                                                    <span class='under-line-click' *ngIf="item.groct>0;else groctEls"
                                                        (keyup)="getMissMatchedEncounters(item,selectedYear,10,'GRAll')"
                                                        (click)="getMissMatchedEncounters(item,selectedYear,10,'GRAll')">{{item.groct}}</span>
                                                    <ng-template #groctEls>{{item.groct}}</ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Nextgen">
                                                    <span *ngIf="item.ecoct !='0';else octEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,10,'NG')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,10,'NG')">{{item.ecoct}}</span>
                                                    </span>
                                                    <ng-template #octEcEls>
                                                        {{item.ecoct}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="deleted_count" title="Deleted in Grand Rounds">
                                                    <span *ngIf="item.dcoct>0;else dcoctEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,10,'DELETE')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,10,'DELETE')">{{item.dcoct}}</span>
                                                    </span>
                                                    <ng-template #dcoctEls>
                                                        {{item.dcoct}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                            <td>
                                                <div [ngClass]="item.grnov>item.ngnov?'unapproved_count':'approved_count'"
                                                    title="In Nextgen">
                                                    <span *ngIf="item.ngnov>0;else novEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,11,'GR')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,11,'GR')">{{item.ngnov}}</span>
                                                    </span>
                                                    <ng-template #novEls>
                                                        {{item.ngnov}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">
                                                    <span class='under-line-click' *ngIf="item.grnov>0;else grnovEls"
                                                        (keyup)="getMissMatchedEncounters(item,selectedYear,11,'GRAll')"
                                                        (click)="getMissMatchedEncounters(item,selectedYear,11,'GRAll')">{{item.grnov}}</span>
                                                    <ng-template #grnovEls>{{item.grnov}}</ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Nextgen">
                                                    <span *ngIf="item.ecnov !='0';else novEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,11,'NG')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,11,'NG')">{{item.ecnov}}</span>
                                                    </span>
                                                    <ng-template #novEcEls>
                                                        {{item.ecnov}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="deleted_count" title="Deleted in Grand Rounds">
                                                    <span *ngIf="item.dcnov>0;else dcnovEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,11,'DELETE')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,11,'DELETE')">{{item.dcnov}}</span>
                                                    </span>
                                                    <ng-template #dcnovEls>
                                                        {{item.dcnov}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                            <td>
                                                <div [ngClass]="item.grdec>item.ngdec?'unapproved_count':'approved_count'"
                                                    title="In Nextgen">
                                                    <span *ngIf="item.ngdec>0;else decEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,12,'GR')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,12,'GR')">{{item.ngdec}}</span>
                                                    </span>
                                                    <ng-template #decEls>
                                                        {{item.ngdec}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">
                                                    <span class='under-line-click' *ngIf="item.grdec>0;else grdecEls"
                                                        (keyup)="getMissMatchedEncounters(item,selectedYear,12,'GRAll')"
                                                        (click)="getMissMatchedEncounters(item,selectedYear,12,'GRAll')">{{item.grdec}}</span>
                                                    <ng-template #grdecEls>{{item.grdec}}</ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Nextgen">
                                                    <span *ngIf="item.ecdec !='0';else decEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,12,'NG')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,12,'NG')">{{item.ecdec}}</span>
                                                    </span>
                                                    <ng-template #decEcEls>
                                                        {{item.ecdec}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="deleted_count" title="Deleted in Grand Rounds">
                                                    <span *ngIf="item.dcdec>0;else dcdecEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncounters(item,selectedYear,12,'DELETE')"
                                                            (click)="getMissMatchedEncounters(item,selectedYear,12,'DELETE')">{{item.dcdec}}</span>
                                                    </span>
                                                    <ng-template #dcdecEls>
                                                        {{item.dcdec}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                        </tr>
                                    </ng-container>
                                    <ng-template #ElCase>
                                        <tr>
                                            <td class="text-center" colspan="16">Unable to Access this Facility</td>
                                        </tr>
                                    </ng-template>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="16" class="m-0 p-0" style="background: white !important;">
                                            <pagination-controls previousLabel="" nextLabel=""
                                                (pageChange)="p = $event"></pagination-controls>
                                        </td>
                                    </tr>
                            </table>

                        </div>
                    </div>

                </div>

            </div>
        </div>
        <!-- Area Chart Ends -->
    </div>
</div>
<!-- Begin Page Content Ends -->


<!-- Extra Count Nextgen Encounters popup starts -->
<div class="modal fade" id="mdlMissEnv_NG" tabindex="-1" aria-labelledby="mdlMissEnv_NGLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg max-width-80">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="mdlMissEnv_NGLabel">Extra Encounters in Nextgen
                    (<b>{{listOfMissMatchedEncounters.length}}</b>)
                    <img alt=' ' *ngIf="this.listOfMissMatchedEncounters.length>0" (keyup)="exportAsXLSXForDetails()"
                        (click)="exportAsXLSXForDetails()" class="ml-2 shadow" [src]='img1'
                        style="width:25px;cursor:pointer;" title="Export Excel">
                </h5>
                <h5 class="modal-title float-right">{{missmatchedItem.physician}} -
                    {{monthsList[month-1]}}/{{selectedYear}}</h5>
                <ng-container (click)="modelClose()" (keyup)="modelClose()">
                    <i class="fa fa-times closePopup" data-dismiss="modal"></i>
                </ng-container>
            </div>
            <div class="modal-body" [formGroup]="FilterForm">
                <div class="row mx-auto my-3">
                    <div class="col-12 col-md-12 px-md-1 py-1">
                        <table class="table table-striped table-bordered table-hover billed_encounters_grid">
                            <thead>
                                <tr class="text-center">
                                    <th>Nextgen Encounter Id</th>
                                    <th>Patient Name</th>
                                    <th>Patient MRN</th>
                                    <th>DOB</th>
                                    <th>Encounter Seen Date</th>
                                    <th>CPT Code</th>
                                    <th>Physician Name</th>
                                    <th>Facility Name</th>
                                    <th>Location</th>
                                    <th>Encounter Status</th>
                                    <th>Agent Name</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngFor="let item of listOfMissMatchedEncounters">
                                    <td class="p-2">{{item.nextgenEncounterID}}</td>
                                    <td class="p-2">{{item.patientName}}</td>
                                    <td class="p-2">{{item.mrn}}</td>
                                    <td class="p-2">{{item.dob | date: 'MM/dd/yyyy'}}</td>
                                    <td class="p-2">{{item.encounterSeenDate | date: 'MM/dd/yyyy' }}</td>
                                    <td class="p-2">{{item.cptCode}}</td>
                                    <td class="p-2">{{item.physicianName}}</td>
                                    <td class="p-2">{{item.facilityName}}</td>
                                    <td class="p-2">{{item.locationName}}</td>
                                    <td class="p-2">{{item.encounterStaus}}</td>
                                    <td class="p-2">{{item.agent_name}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Extra Count Nextgen Encounters popup ends -->

<!-- Deleted Grand Rounds Encounters popup starts -->
<div class="modal fade" id="mdlMissEnv_DELETE" tabindex="-1" aria-labelledby="mdlMissEnv_DELETELabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg max-width-80">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="mdlMissEnv_DELETELabel">Deleted Encounters in Grand Rounds
                    (<b>{{listOfMissMatchedEncounters.length}}</b>)
                    <img alt=' ' *ngIf="this.listOfMissMatchedEncounters.length>0" (keyup)="exportAsXLSXForDetails()"
                        (click)="exportAsXLSXForDetails()" class="ml-2 shadow" [src]='img1'
                        style="width:25px;cursor:pointer;" title="Export Excel">
                </h5>
                <h5 class="modal-title float-right">{{missmatchedItem.physician}} -
                    {{monthsList[month-1]}}/{{selectedYear}}</h5>
                <ng-container (keyup)="modelClose()" (click)="modelClose()">
                    <i class="fa fa-times closePopup" data-dismiss="modal"></i>
                </ng-container>
            </div>
            <div class="modal-body" [formGroup]="FilterForm">
                <div class="row mx-auto my-3">
                    <div class="col-12 col-md-12 px-md-1 py-1">
                        <table class="table table-striped table-bordered table-hover billed_encounters_grid">
                            <thead>
                                <tr class="text-center">
                                    <th>Patient Name</th>
                                    <th>Account Number</th>
                                    <th>Patient MRN</th>
                                    <th>DOB</th>
                                    <th>Encounter Seen Date</th>
                                    <th>Physician Name</th>
                                    <th>Facility Name</th>
                                    <th>Deleted By</th>
                                    <th>Deleted On</th>
                                    <th>Deleted Reason</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngFor="let item of listOfMissMatchedEncounters">
                                    <td class="p-2">{{item.patientName}}</td>
                                    <td class="p-2">{{item.accountNumber}}</td>
                                    <td class="p-2">{{item.mrn}}</td>
                                    <td class="p-2">{{item.dob | date: 'MM/dd/yyyy'}}</td>
                                    <td class="p-2">{{item.encounterSeenDate | date: 'MM/dd/yyyy'}}</td>
                                    <td class="p-2">{{item.physicianName}}</td>
                                    <td class="p-2">{{item.facilityName}}</td>
                                    <td class="p-2">{{item.deleteBy}}</td>
                                    <td class="p-2">{{item.deletedOn | date: 'MM/dd/yyyy'}}</td>
                                    <td class="p-2">{{item.deletedReason}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Deleted Grand Rounds Encounters popup ends -->

<!-- All Grand Rounds Encounters popup starts -->
<div class="modal fade" id="mdlMissEnv_GRAll" tabindex="-1" aria-labelledby="mdlMissEnv_GRAllLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg max-width-80">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="mdlMissEnv_GRAllLabel"> Encounters in Grand Rounds
                    (<b>{{listOfMissMatchedEncounters.length}}</b>)
                    <img alt=' ' *ngIf="this.listOfMissMatchedEncounters.length>0" (keyup)="exportAsXLSXForDetails()"
                        (click)="exportAsXLSXForDetails()" class="ml-2 shadow" [src]='img1'
                        style="width:25px;cursor:pointer;" title="Export Excel">
                </h5>
                <h5 class="modal-title float-right">{{missmatchedItem.physician}} -
                    {{monthsList[month-1]}}/{{selectedYear}}</h5>
                <ng-container (click)="modelClose()" (keyup)="modelClose()">
                    <i class="fa fa-times closePopup" data-dismiss="modal"></i>
                </ng-container>
            </div>
            <div class="modal-body" [formGroup]="FilterForm">
                <div class="row mx-auto my-3">
                    <div class="col-12 col-md-12 px-md-1 py-1">
                        <table class="table table-striped table-bordered table-hover billed_encounters_grid">
                            <thead>
                                <tr class="text-center">
                                    <th>Patient Name</th>
                                    <th>Account Number</th>
                                    <th>Patient MRN</th>
                                    <th>DOB</th>
                                    <th>Encounter Seen Date</th>
                                    <th>Physician Name</th>
                                    <th>Facility Name</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngFor="let item of listOfMissMatchedEncounters">
                                    <td class="p-2">{{item.patientName}}</td>
                                    <td class="p-2">{{item.accountNumber}}</td>
                                    <td class="p-2">{{item.mrn}}</td>
                                    <td class="p-2">{{item.dob | date: 'MM/dd/yyyy'}}</td>
                                    <td class="p-2">{{item.encounterSeenDate | date: 'MM/dd/yyyy'}}</td>
                                    <td class="p-2">{{item.physicianName}}</td>
                                    <td class="p-2">{{item.facilityName}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- All Grand Rounds Encounters popup ends -->

<!-- Mismatched Grandrounds Encounters popup starts -->
<div class="modal fade" id="mdlMissEnv_GR" tabindex="-1" aria-labelledby="mdlMissEnv_GRLable" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg max-width-80">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="mdlMissEnv_GRLable">{{missmatchedItem.physician}} -
                    {{monthsList[month-1]}}/{{selectedYear}}</h5>
                <i class="fa fa-times closePopup" data-dismiss="modal"></i>
            </div>
            <div class="modal-body" [formGroup]="FilterForm">
                <div class="row mx-auto my-3">
                    <div class="col-12 col-md-12 px-md-1 py-1">
                        <nav>
                            <div class="nav nav-tabs border-bottom-0" id="nav-tabpop" role="tablist">
                                <a class="nav-item nav-link pop-nav" [class.active]="type=='GR'">
                                    <span (keyup)="getMissMatchedEncounters(missmatchedItem,selectedYear,month,'GR')"
                                        (click)="getMissMatchedEncounters(missmatchedItem,selectedYear,month,'GR')">In
                                        Grandrounds but missing in NextGen</span>
                                    <span *ngIf="type=='GR'">(<b>{{listOfMissMatchedEncounters.length}}</b>)</span>
                                    <img alt=' ' *ngIf="this.listOfMissMatchedEncounters.length>0 && type=='GR'"
                                        (keyup)="exportAsXLSXForDetails()" (click)="exportAsXLSXForDetails()"
                                        class="ml-4 shadow export-exel" [src]='img1' title="Export Excel">
                                </a>
                                <a class="nav-item nav-link pop-nav" [class.active]="type=='NGAll'">
                                    <span (keyup)="getMissMatchedEncounters(missmatchedItem,selectedYear,month,'NGAll')"
                                        (click)="getMissMatchedEncounters(missmatchedItem,selectedYear,month,'NGAll')">Matched
                                        in Nextgen</span>
                                    <span *ngIf="type=='NGAll'">(<b>{{listOfMissMatchedEncounters.length}}</b>)</span>
                                    <img alt=' ' *ngIf="this.listOfMissMatchedEncounters.length>0 && type=='NGAll'"
                                        (keyup)="exportAsXLSXForDetails()" (click)="exportAsXLSXForDetails()"
                                        class="ml-4 shadow export-exel" [src]='img1' title="Export Excel">
                                </a>
                            </div>
                        </nav>
                        <div class="card px-3 py-2 shadow tab-content" id="nav-tabContentNextgen">
                            <div class="tab-pane scrolling" [ngClass]="{'show active':type=='GR'}">
                                <div class="row">
                                    <div class="col-12 my-2">
                                        <table
                                            class="table table-striped table-bordered table-hover billed_encounters_grid"
                                            *ngIf="this.listOfMissMatchedEncounters.length>0 else noData">
                                            <thead>
                                                <tr class="text-center">
                                                    <th>Patient Name</th>
                                                    <th>Account Number</th>
                                                    <th>Patient MRN</th>
                                                    <th>DOB</th>
                                                    <th>Encounter Seen Date</th>
                                                    <th>Physician Name</th>
                                                    <th>Facility Name</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr *ngFor="let item of listOfMissMatchedEncounters">
                                                    <td class="p-2">{{item.patientName}}</td>
                                                    <td class="p-2">{{item.accountNumber}}</td>
                                                    <td class="p-2">{{item.mrn}}</td>
                                                    <td class="p-2">{{item.dob | date: 'MM/dd/yyyy'}}</td>
                                                    <td class="p-2">{{item.encounterSeenDate | date: 'MM/dd/yyyy'}}</td>
                                                    <td class="p-2">{{item.physicianName}}</td>
                                                    <td class="p-2">{{item.facilityName}}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        <ng-template #noData>
                                            <b>No records found.</b>
                                        </ng-template>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane scrolling" [ngClass]="{'show active':type=='NGAll'}">
                                <div class="row">
                                    <div class="col-12 my-2">
                                        <table
                                            class="table table-striped table-bordered table-hover billed_encounters_grid">
                                            <thead>
                                                <tr class="text-center">
                                                    <th>Nextgen Encounter Id</th>
                                                    <th>Patient Name</th>
                                                    <th>Patient MRN</th>
                                                    <th>DOB</th>
                                                    <th>Encounter Seen Date</th>
                                                    <th>CPT Code</th>
                                                    <th>Physician Name</th>
                                                    <th>Facility Name</th>
                                                    <th>Location</th>
                                                    <th>Encounter Status</th>
                                                    <th>Agent Name</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr *ngFor="let item of listOfMissMatchedEncounters">
                                                    <td class="p-2">{{item.nextgenEncounterID}}</td>
                                                    <td class="p-2">{{item.patientName}}</td>
                                                    <td class="p-2">{{item.mrn}}</td>
                                                    <td class="p-2">{{item.dob | date: 'MM/dd/yyyy'}}</td>
                                                    <td class="p-2">{{item.encounterSeenDate | date: 'MM/dd/yyyy' }}
                                                    </td>
                                                    <td class="p-2">{{item.cptCode}}</td>
                                                    <td class="p-2">{{item.physicianName}}</td>
                                                    <td class="p-2">{{item.facilityName}}</td>
                                                    <td class="p-2">{{item.locationName}}</td>
                                                    <td class="p-2">{{item.encounterStaus}}</td>
                                                    <td class="p-2">{{item.agent_name}}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Mismatched Grandrounds Encounters popup ends -->