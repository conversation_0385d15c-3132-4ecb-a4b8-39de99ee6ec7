{"name": "prime-grand-rounds-angular-app", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:prod": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^15.2.0", "@angular/cdk": "^15.2.9", "@angular/common": "^15.2.0", "@angular/compiler": "^15.2.0", "@angular/core": "^15.2.0", "@angular/forms": "^15.2.0", "@angular/material": "^15.2.9", "@angular/platform-browser": "^15.2.0", "@angular/platform-browser-dynamic": "^15.2.0", "@angular/router": "^15.2.0", "@azure/msal-angular": "^3.0.23", "@azure/msal-browser": "^3.23.0", "@fortawesome/fontawesome-free": "^6.6.0", "@mobiscroll/angular-lite": "^4.10.9", "@ng-idle/core": "^12.0.4", "@ng-idle/keepalive": "^12.0.4", "@ng-matero/extensions": "^15.6.4", "@types/jquery": "^3.5.30", "angular-highcharts": "^15.0.1", "angular-user-idle": "^4.0.0", "bootstrap-icons": "^1.11.3", "crypto-js": "^4.2.0", "d3": "^7.9.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "glob": "^11.0.0", "guid-typescript": "^1.0.9", "highcharts": "^11.4.8", "jointjs": "^3.7.7", "jquery": "^3.7.1", "jquery-ui": "^1.14.0", "lodash": "^4.17.21", "moment": "^2.30.1", "ng-multiselect-dropdown": "^0.3.9", "ng5-slider": "^1.2.6", "ngx-bootstrap": "^10.2.0", "ngx-daterangepicker-material": "^6.0.4", "ngx-device-detector": "^5.0.1", "ngx-graph": "^0.9.2", "ngx-material-timepicker": "^13.1.1", "ngx-multiple-dates": "^15.0.1", "ngx-pagination": "^6.0.3", "ngx-toastr": "^16.1.0", "popper.js": "^1.16.1", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.12.0"}, "devDependencies": {"@angular-devkit/build-angular": "^15.2.11", "@angular/animations": "^15.2.10", "@angular/cdk": "^15.2.9", "@angular/cli": "~15.2.11", "@angular/compiler-cli": "^15.2.0", "@types/backbone": "^1.4.20", "@types/file-saver": "^2.0.7", "@types/jasmine": "^4.3.0", "@types/luxon": "^3.4.2", "jasmine-core": "~4.5.0", "karma": "~6.4.4", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "~4.9.4"}, "overrides": {"debug": "^4.4.0", "inflight": "1.0.6", "webpack": "5.97.1"}}