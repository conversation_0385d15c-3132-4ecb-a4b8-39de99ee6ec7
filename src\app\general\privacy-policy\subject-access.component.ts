import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { JsonAppConfigService } from 'src/app/config/json-app-config.service';

@Component({
  selector: 'app-subject-access',
  templateUrl: './subject-access.component.html',
  styleUrls: ['./subject-access.component.css']
})
export class SubjectAccessComponent implements OnInit {
  public reqiuestForm: FormGroup;
  public request: any = {};
  public submitted: boolean = false;
  constructor(private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly commonServ: CommonService,
    private readonly toastr: ToastrService
    , private readonly router: Router, private readonly jsonAppConfigService: JsonAppConfigService) { }

  ngOnInit(): void {
    this.appComp.loadPageName('', '');
    this.reqiuestForm = this.fb.group({
      txtWebsite: ['', [Validators.required]],
      txtName: ['', [Validators.required]],
      txtEmail: ['', [Validators.required, Validators.email]],
      rdoRequestType: ['Whose name appears above', Validators.required],
      txtComments: ['', [Validators.pattern('^[0-9a-zA-Z-’\'"“” ()_?!:;.,stn]+$')]],
      chkConfirm1: [false, Validators.requiredTrue],
      chkConfirm2: [false, Validators.requiredTrue],
      chkConfirm3: [false, Validators.requiredTrue]
    });
  }

  get f() { return this.reqiuestForm.controls; }

  submit() {
    this.submitted = true;
    if (this.reqiuestForm.invalid) {
      return;
    }
    this.commonServ.startLoading();
    this.request.web_site_name = this.reqiuestForm.value.txtWebsite;
    this.request.name = this.reqiuestForm.value.txtName;
    this.request.email = this.reqiuestForm.value.txtEmail;
    this.request.submitting_as = this.reqiuestForm.value.rdoRequestType;
    this.request.comments = this.reqiuestForm.value.txtComments;
    this.jsonAppConfigService.insertDataRequestForm(this.request);
    setTimeout(() => {
      this.commonServ.stopLoading();
      this.toastr.success('Request submitted successfully!.', '', { timeOut: 2500 });
      this.router.navigate(['/privacy']);
    }, 2000);
  }

}
