import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { NoAccessComponent } from './pages/no-access.component';
import { LogoutComponent } from './pages/logout.component';
import { CustomerrorComponent } from './pages/customerror.component';
import { NotFoundComponent } from './general/not-found/not-found.component';
import { HomeComponent } from './general/home/<USER>';
import { AccessDeniedComponent } from './general/access-denied/access-denied.component';

const routes: Routes = [
  {
    path: '', component: HomeComponent
  },
  {
    // Needed for hash routing
    path: 'code',
    component: HomeComponent
  },
  {
    path: "id_token",
    component: HomeComponent,
  },
  {
    path: 'signin',
    loadChildren: () => import('./general/signin/signin.module')
      .then(mod => mod.SigninModule)
  },
  {
    path: 'access-denied', pathMatch: 'full', component: AccessDeniedComponent
  },
  { path: 'no-access', component: NoAccessComponent },
  { path: 'logout', component: LogoutComponent },
  { path: 'error', component: CustomerrorComponent },
  { path: 'dashboard', loadChildren: () => import('./pages/dashboard/dashboard.module').then(mod => mod.DashboardModule) },
  { path: 'physician', loadChildren: () => import('./pages/physician/physician.module').then(mod => mod.PhysicianModule) },
  { path: 'coordinator', loadChildren: () => import('./pages/coordinator/coordinator.module').then(mod => mod.CoordinatorModule) },
  { path: 'auditor', loadChildren: () => import('./pages/auditor/auditor.module').then(mod => mod.AuditorModule) },
  { path: 'biller', loadChildren: () => import('./pages/billing/billing.module').then(mod => mod.BillingModule) },
  { path: 'report', loadChildren: () => import('./pages/reports/reports.module').then(mod => mod.ReportsModule) },
  { path: 'message-hub', loadChildren: () => import('./pages/message-hub/message-hub.module').then(mod => mod.MessageHubModule) },
  { path: 'attachments', loadChildren: () => import('./pages/attachment/attachment.module').then(mod => mod.AttachmentModule) },
  { path: 'historical-encounters', loadChildren: () => import('./pages/DeletedEncounters/deleted-encounters.module').then(mod => mod.DeletedEncountersModule) },
  { path: 'usermanagement', loadChildren: () => import('./pages/usermanagement/usermanagement.module').then(mod => mod.UsermanagementModule) },
  { path: 'privacy', loadChildren: () => import('./general/privacy-policy/privacy-policy.module').then(mod => mod.PrivacyPolicyModule) },
  { path: '**', component: NotFoundComponent }
];

const isIframe = window !== window.parent && !window.opener;

@NgModule({
  imports: [RouterModule.forRoot(routes, {
    useHash: true
  })],
  exports: [RouterModule]
})
export class AppRoutingModule { }
