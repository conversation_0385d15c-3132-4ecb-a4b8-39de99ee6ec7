import { Component, OnInit, Input } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { CoordinatorService } from 'src/app/services/coordinator/coordinator.service';
import { ViewModifyEncountersComponent } from './view-modify-encounters.component';
declare let $: any;

@Component({
  selector: 'app-viewedit-encounter',
  templateUrl: './viewedit-encounter.component.html',
  styles: []
})
export class VieweditEncounterComponent implements OnInit {

  @Input() lisfOfEncounters: Array<any> = [];
  @Input() PatientObject: any = {};
  @Input() Index: number;

  @Input() groupAlerts: any = {};
  public divMessages;
  public request: any = {};
  public listOfRemovedCpts: Array<any> = [];
  public listOfRemovedIcds: Array<any> = [];
  public listOfIcds: Array<any> = [];
  public listOfCpts: Array<any> = [];
  public oldlistOfCpts: Array<any> = [];
  public isFirst: boolean;
  public encounterIds: string = "";
  public encounterID: string = "";
  public isChange: boolean = false;
  public lisfOfAllEncounters: Array<any> = [];
  public encounterObj: any = {};
  public HighlightRow = -1;
  @Input() firstEcounterIdToOpen: string = "";
  public lisfOfCPTData: Array<any>;
  public cptType: string;
  public lisfOfICDData: Array<any>;
  public icdType: string;
  public listOfModifier: Array<any> = [];
  public newCptCode: string = "";
  public reason: any;
  public cptCode: string = "";
  public img1: string = '../../../assets/img/uparrow.png';
  public img2: string = '../../../assets/img/downarrow.png';

  constructor(private readonly commonServ: CommonService, private readonly viewModify: ViewModifyEncountersComponent, private readonly encrDecr: EncrDecrServiceService, private readonly coorServ: CoordinatorService
    , private readonly toastr: ToastrService) { }

  ngOnInit() {
    this.HighlightRow = -1;
  }

  getCPTData(item, phyEmailId, type) {

   // this.commonServ.startLoading();
    $("#favorite-tab").click();
    this.lisfOfCPTData = [];
    item.listOfRemovedCpts = [];
    this.oldlistOfCpts = [];
    this.viewModify.encounterObj = item;
    this.encounterObj = item;
    let existingArray: Array<any> = [];
    item.listOfCpts.forEach(a => {
      existingArray.push(a.split('(--')[0].trim());
      this.oldlistOfCpts.push(a);
    });
    this.cptType = type;
    this.request.PHYSICIANMAILID = this.encrDecr.set(phyEmailId);
    this.commonServ.getCPTData(this.request).subscribe((p: any) => {
      this.lisfOfCPTData = p;
      this.request = {};
      this.lisfOfCPTData.forEach(x => {
        x.deletedReason = "";
        x.isRemoved = false;
        x.isExist = existingArray.includes(x.cptname);
        x.isOld = existingArray.includes(x.cptname);
        let ittem=item.groupedListOfCpts.find(a=>a.cpt_name.split('(--')[0].trim()==x.cptname);
        x.cpt_count = ittem ? ittem?.cpt_count : 0;
      });
      //this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      //this.commonServ.stopLoading();
      console.error(error.status);
    });
  }

  getICDData(item, phyEmailId, type) {
    //this.commonServ.startLoading();
    $("#favorite-tab1").click();
    this.lisfOfICDData = [];
    item.listOfRemovedIcds = [];
    this.viewModify.encounterObj = item;
    this.encounterObj = item;
    let existingArray = item.listOfIcds;
    this.icdType = type;
    this.request.PHYSICIANMAILID = this.encrDecr.set(phyEmailId);
    this.commonServ.getICDData(this.request).subscribe((p: any) => {
      this.lisfOfICDData = p;
      this.request = {};
      this.lisfOfICDData.forEach(x => {
        if (existingArray.includes(x.icdname)) {
          x.isExist = true;
        }
        else {
          x.isExist = false;
        }
      });
      //this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      //this.commonServ.stopLoading();
      console.error(error.status);
    });
  }

  editEncounterSeenDate(item, pObj) {
    //this.commonServ.startLoading();
    this.viewModify.encounterObj = item;
    this.encounterObj = item;
    this.viewModify.PatientObject = pObj;
    this.commonServ.updateEncounterDate(item.factimezone).subscribe((p: any) => {
      $('#envSeenDate').val(p);
      //this.commonServ.stopLoading();
    }
      , error => {
        //this.commonServ.stopLoading();
        console.error(error.status);
      });
  }
  deleteCPTCodeAll(eObj, cptCode) {
    //this.commonServ.startLoading();
    const index: number = eObj.listOfCpts.indexOf(cptCode);
    if (index !== -1) {
      eObj.listOfCpts.splice(index, 1);
      this.listOfRemovedCpts.push(cptCode);
    }
    eObj.groupedListOfCpts = eObj.groupedListOfCpts.filter(x => x.cpt_name != cptCode);
    $(".btnSave" + eObj.encounteR_ID).show();
    //this.commonServ.stopLoading();
  }
  deleteCPTCode(eObj, cptCode) {
   // this.commonServ.startLoading();
    this.oldlistOfCpts = [];
    this.encounterObj = eObj;
    eObj.listOfCpts.forEach(a => {
      this.oldlistOfCpts.push(a);
    });
    const index: number = eObj.listOfCpts.indexOf(cptCode);
    if (index !== -1) {
      eObj.listOfCpts.splice(index, 1);
      this.cptCode = cptCode;
      let newGroup: any = eObj.groupedListOfCpts.find(x => x.cpt_name == cptCode);
      let oldGroup: any = eObj.groupedListOfCptsObj?.find(x => x.cpt_name == cptCode.split('(--')[0].trim());
      if (oldGroup?.cpt_count <= newGroup?.cpt_count) {
        this.listOfRemovedCpts.push(cptCode);
        this.newCptCode = cptCode;
        if (newGroup.cpt_count > 1) {
          newGroup.cpt_count = newGroup.cpt_count - 1;
        }
        else {
          eObj.groupedListOfCpts = eObj.groupedListOfCpts.filter(x => x.cpt_name != cptCode);
        }
        if (oldGroup.cpt_count > 1) {
          oldGroup.cpt_count = oldGroup.cpt_count - 1;
        }
        else {
          eObj.groupedListOfCptsObj = eObj.groupedListOfCptsObj.filter(x => x.cpt_name != cptCode);
        }
      } else {
        this.newCptCode = "";
      }
      $('#confirmAndReasonsDeleteCPT').modal('show');
      this.viewModify.isDisabled = true;
      $(".btnSave" + eObj.encounteR_ID).show();
    }
    //this.commonServ.stopLoading();
  }
  deleteICDCode(eObj, icdCode) {
    //this.commonServ.startLoading();
    const index: number = eObj.listOfIcds.indexOf(icdCode);
    if (index !== -1) {
      eObj.listOfIcds.splice(index, 1);
      const oldIndex: number = eObj.listOfIcdsObj.indexOf(icdCode);
      if (oldIndex !== -1) {
        this.listOfRemovedIcds.push(icdCode);
        eObj.listOfIcdsObj.splice(oldIndex, 1);
      }
      $(".btnSave" + eObj.encounteR_ID).show();
    }
    //this.commonServ.stopLoading();
  }
  saveChanges(eObj, pObj) {
    if (eObj.listOfCpts.length > 0 && eObj.listOfIcds.length > 0) {
      //this.commonServ.startLoading();
      this.isChange = true;
      this.request.sENCOUNTER_ID = this.encrDecr.set(eObj.encounteR_ID);
      this.request.sMARKASCODED = this.encrDecr.set('0');
      this.request.ROLE = this.encrDecr.set('Physician');
      this.request.NEW_CPTArray = eObj.listOfCpts;
      this.request.NEW_ICDArray = eObj.listOfIcds;
      this.listOfIcds = eObj.listOfIcds;
      this.request.DeletedCptsArray = this.listOfRemovedCpts;
      this.request.DeletedIcdsArray = this.listOfRemovedIcds;
      this.coorServ.editEncounter(this.request).subscribe((p: any) => {
        if (p.status > 0) {
          eObj.lastmodifiedby = p.modifieD_BY;
          eObj.lastmodifieddate = p.modifiedDate;
        }
        this.listOfRemovedCpts = [];
        this.listOfRemovedIcds = [];
        $(".btnSave" + eObj.encounteR_ID).hide();
        this.HighlightRow = -1;
        this.request = {};
        //this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        //this.commonServ.stopLoading();
      });
    }
    else {
      this.toastr.error('Atleast one CPT/HCPCS and ICD Codes required.', '', { timeOut: 1900 });
    }
  }

  deleteEncounter(item) {
    this.viewModify.encounterObj = item;
    this.encounterObj = item;
  }

  getModifierData(item, cpt) {
   // this.commonServ.startLoading();
    this.listOfModifier = [];
    this.viewModify.encounterObj = item;
    this.encounterObj = item;
    this.cptType = cpt;
    let existingModi: Array<any> = [];
    if (cpt.includes('(--,')) {
      let item = cpt.split('(--,')[1].replace(')', "");
      let arry;
      if (item.includes(',')) {
        arry = item.split(',');
        arry.forEach(b => {
          existingModi.push(b);
        });
      }
      else {
        existingModi.push(item);
      }
    }
    this.request.PhysicianEmailId = this.encrDecr.set(item.physicianmailid);
    this.coorServ.getModifiers(this.request).subscribe((p: any) => {
      this.listOfModifier = p;
      this.request = {};
      this.listOfModifier.forEach(x => {
        if (existingModi.includes(x.modifiersname.split('-')[0])) {
          x.isExist = true;
        }
        else {
          x.isExist = false;
        }
      });
      //this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      //this.commonServ.stopLoading();
      console.error(error.status);
    });
  }

  viewHistory(envId) {
    //this.commonServ.startLoading();
    this.request.sEncounterId = this.encrDecr.set(envId);
    this.commonServ.getEncouterHistory(this.request).subscribe((p: any) => {
      this.request = {};
      this.viewModify.listOfHistory = p;
      //this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      //this.commonServ.stopLoading();
      console.error(error.status);
    });
  }

  submitICDsMultiEcounters(eObj, encID, pObj, lstEnvs) {
    //this.commonServ.startLoading();
    this.encounterID = encID;
    this.listOfIcds = eObj.listOfIcds;
    this.PatientObject = pObj;
    this.lisfOfAllEncounters = lstEnvs;
    this.encounterObj = eObj;
    $('#saveICDsAllEcounters').modal('show');
    $('#chkAll').prop("checked", false);
    //this.commonServ.stopLoading();
  }
  getEncountersByPatient(pObj) {
    this.viewModify.getEncountersByPatient(pObj, false);
  }

  convertCPTCodes(list) {
    let newList = list.map((cpt) => {
      cpt = cpt.substring(0, 5);
      return cpt;
    });
    newList = newList.toString();
    return newList;
  }

  getTwoCPTCodes(list) {
    let newString = "";
    let newList = list.map((cpt, index) => {
      cpt = cpt.substring(0, 5);
      if (index == 1 || list.length == 1) {
        newString = newString.concat(cpt);
      } else if (index < 2) {
        newString = newString.concat(cpt + ",");
      }
      return cpt;
    });

    if (newList.length > 2) {
      newString = newString.concat("...");
    }
    return newString;
  }
  ClickedRow(rowIndex) {
    this.HighlightRow = rowIndex;
  }
  moveSelectedUp(eObj) {
    let newIndex = this.HighlightRow - 1;
    if (this.HighlightRow>-1 && newIndex >= 0 && newIndex <= eObj.listOfIcds.length - 1) {
      let currentICDs = eObj.listOfIcds[this.HighlightRow];
      eObj.listOfIcds[this.HighlightRow] = eObj.listOfIcds[newIndex];
      eObj.listOfIcds[newIndex] = currentICDs;
      this.HighlightRow = newIndex;
      $(".btnSave" + eObj.encounteR_ID).show();
    }
  }
  moveSelectedDown(eObj) {
    let newIndex = this.HighlightRow + 1;
    if (this.HighlightRow>-1 && newIndex >= 0 && newIndex <= eObj.listOfIcds.length - 1) {
      let currentICDs = eObj.listOfIcds[this.HighlightRow];
      eObj.listOfIcds[this.HighlightRow] = eObj.listOfIcds[newIndex];
      eObj.listOfIcds[newIndex] = currentICDs;
      this.HighlightRow = newIndex;
      $(".btnSave" + eObj.encounteR_ID).show();
    }
  }

  updateRemovedCptList(eObj) {
    if (eObj.listOfRemovedCpts.length > 0) {
      this.encounterObj = eObj;
      if (this.cptType == 'Add') {
        $(".btnSave" + eObj.encounteR_ID).show();
        this.listOfRemovedCpts = eObj.listOfRemovedCpts;
        this.viewModify.isDisabled = false;

      } else {
        this.cptCode = eObj.listOfRemovedCpts[0];
        $('#confirmAndReasonsDeleteCPT').modal('show');
        this.viewModify.isDisabled = true;
      }
    }
    else {
      eObj.listOfRemovedCpts.forEach((item: any) => {
        this.listOfRemovedCpts.push(item);
      });
    }
  }
  updateCancelDeleteReason(reason: string) {
    this.viewModify.isDisabled = false;
    this.encounterObj.listOfCpts = this.oldlistOfCpts;
    this.encounterObj.listOfRemovedCpts = [];
    this.listOfRemovedCpts = [];
  }
  updateDeleteReason(reason: string) {
    this.viewModify.isDisabled = false;
    const index: number = this.listOfRemovedCpts.indexOf(this.newCptCode);
    if (index == -1 || this.newCptCode == "") {
      this.encounterObj.listOfRemovedCpts.forEach((item: any) => {
        if (reason) {
          item = item + "||" + reason
        }
        this.listOfRemovedCpts.push(item);
      });
    }
    else {
      this.listOfRemovedCpts[index] = this.newCptCode + "||" + reason;
    }
  }
  updateRemovedIcdList(eObj) {
    eObj.listOfRemovedIcds.forEach((item: any) => {
      this.listOfRemovedIcds.push(item);
    });
  }
  updateUpdatedCptDataList(eObj) {
    //this.commonServ.startLoading();
    let existingArray: Array<any> = [];
    eObj.listOfCpts.forEach(a => {
      existingArray.push(a.split('(--')[0].trim());
    });
    this.request.PHYSICIANMAILID = this.encrDecr.set(eObj.physicianmailid);
    this.commonServ.getCPTData(this.request).subscribe((p: any) => {
      this.request = {};
      this.lisfOfCPTData = p;
      this.lisfOfCPTData.forEach(x => {
        x.deletedReason = "";
        x.isExist = existingArray.includes(x.cptname);
        x.isOld = existingArray.includes(x.cptname);
        let ittem=eObj.groupedListOfCpts.find(a=>a.cpt_name.split('(--')[0].trim()==x.cptname);
        x.cpt_count = ittem ? ittem?.cpt_count : 0;
      });
      //this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      //this.commonServ.stopLoading();
      console.error(error.status);
    });
  }

  updateUpdatedICDDataList(eObj) {
   // this.commonServ.startLoading();
    let existingArray = this.encounterObj.listOfIcds;
    this.request.PHYSICIANMAILID = this.encrDecr.set(eObj.physicianmailid);
    this.commonServ.getICDData(this.request).subscribe((p: any) => {
      this.lisfOfICDData = p;
      this.request = {};
      this.lisfOfICDData.forEach(x => {
        if (existingArray.includes(x.icdname)) {
          x.isExist = true;
        }
        else {
          x.isExist = false;
        }
      });
      //this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      //this.commonServ.stopLoading();
      console.error(error.status);
    });
  }

}
