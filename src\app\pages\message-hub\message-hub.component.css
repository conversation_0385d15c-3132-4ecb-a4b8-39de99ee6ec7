/* Mobile-specific styles for popup */
@media (max-width: 767.98px) {
    .mobile-cpt-modal {
        max-width: 95% !important;
        margin: 0 auto;
    }

    .mobile-cpt-modal .modal-content {
        border-radius: 8px;
        overflow: hidden;
    }

    .mobile-cpt-modal .modal-header {
        padding: 0.5rem 1rem;
        background-color: #336699;
        color: white;
    }

    .mobile-cpt-modal .modal-title {
        font-size: 1rem;
        font-weight: 600;
    }

    .mobile-cpt-modal .modal-body {
        padding: 0.75rem;
        max-height: 70vh;
        overflow-y: auto;
    }

    .mobile-cpt-modal .quantity {
        display: inline-flex;
        margin-bottom: 0.5rem;
    }

    .mobile-cpt-modal .quantity__input {
        width: 30px;
        text-align: center;
    }

    .mobile-cpt-modal .form-control {
        font-size: 14px;
    }

    .mobile-cpt-modal .text-danger {
        text-align: center;
        margin-top: 0.5rem;
        font-size: 14px;
    }

    .mobile-cpt-modal .modal-footer {
        padding: 0.5rem;
        justify-content: center;
    }

    .mobile-cpt-modal .btn {
        min-width: 100px;
    }
}

/* Mobile modal fixes for Message Hub */
@media only screen and (max-width: 767.98px) {
    /* Fix modal backdrop issues on mobile */
    .modal-backdrop {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        z-index: 1040 !important;
    }

    /* Improve close button touch interaction on mobile */
    .mobile-cpt-modal .close {
        padding: 0.75rem !important;
        font-size: 1.5rem !important;
        -webkit-tap-highlight-color: transparent !important;
        touch-action: manipulation !important;
    }

    /* Prevent body scroll when modal is open */
    body.modal-open {
        overflow: hidden !important;
    }
}

/* Safari desktop specific modal fixes */
@supports (-webkit-appearance: none) {
    @media only screen and (min-width: 768px) {
        /* Safari desktop modal backdrop fixes */
        .modal-backdrop {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            z-index: 1040 !important;
            background-color: rgba(0, 0, 0, 0.5) !important;
        }

        /* Safari desktop modal positioning */
        .modal {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            z-index: 1050 !important;
        }

        /* Safari desktop close button fixes */
        .close {
            position: relative !important;
            z-index: 1051 !important;
            cursor: pointer !important;
            -webkit-appearance: none !important;
        }

        /* Force proper modal hiding in Safari */
        .modal.fade:not(.show) {
            display: none !important;
        }

        /* Safari body scroll prevention */
        body.modal-open {
            overflow: hidden !important;
            position: fixed !important;
            width: 100% !important;
        }
    }
}

/* Mobile Safari specific modal fixes */
@supports (-webkit-appearance: none) {
    @media only screen and (max-width: 767.98px) {
        /* Mobile Safari modal backdrop fixes */
        .modal-backdrop {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            z-index: 1040 !important;
            background-color: rgba(0, 0, 0, 0.5) !important;
            -webkit-backface-visibility: hidden !important;
            backface-visibility: hidden !important;
        }

        /* Mobile Safari modal positioning */
        .mobile-cpt-modal {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            z-index: 1050 !important;
            -webkit-transform: none !important;
            transform: none !important;
        }

        /* Mobile Safari close button fixes */
        .mobile-cpt-modal .close {
            padding: 0.75rem !important;
            font-size: 1.5rem !important;
            -webkit-tap-highlight-color: transparent !important;
            -webkit-touch-callout: none !important;
            -webkit-user-select: none !important;
            touch-action: manipulation !important;
            cursor: pointer !important;
        }

        /* Force proper modal hiding in Mobile Safari */
        .modal.fade:not(.show) {
            display: none !important;
        }

        /* Mobile Safari body scroll prevention */
        body.modal-open {
            overflow: hidden !important;
            position: fixed !important;
            width: 100% !important;
            -webkit-overflow-scrolling: touch !important;
        }

        /* Mobile Safari viewport fixes */
        .modal-dialog {
            -webkit-transform: none !important;
            transform: none !important;
            margin: 0.5rem !important;
        }

        /* Mobile Safari modal content fixes */
        .modal-content {
            -webkit-backface-visibility: hidden !important;
            backface-visibility: hidden !important;
        }
    }
}