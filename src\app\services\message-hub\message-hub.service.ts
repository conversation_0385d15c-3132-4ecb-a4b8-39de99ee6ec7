import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { apiUrl } from 'src/app/config';

@Injectable({
  providedIn: 'root'
})
export class MessageHubService {

  constructor(private readonly http: HttpClient) { }

  getMessages() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.get(apiUrl + 'api/MessageHub/GetMessages', { headers: headers });
  }

  sendMessage(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/MessageHub/SendMessage', request, { headers: headers });
  }

  archiveMessage(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/MessageHub/ArchiveMessage', request, { headers: headers });
  }

  markAsAllRead(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/MessageHub/MarkAsAllRead', request, { headers: headers });
  }

}