import { Component, OnInit } from '@angular/core';
import * as Highcharts from 'highcharts';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { FacilityModel } from 'src/app/models/facility.model';
import { PhysicianModel } from 'src/app/models/physician.model';
import { ReportsService } from 'src/app/services/reports/reports.service';
import { FormGroup, Validators, FormBuilder } from '@angular/forms';
import { ExcelServices } from 'src/app/services/common/ExcelService';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { forkJoin } from 'rxjs';

declare let $: any;


@Component({
  selector: 'app-cpt-analysis',
  templateUrl: './cpt-analysis.component.html',
  styles: []
})
export class CptAnalysisComponent implements OnInit {
  public listOfFacilities = Array<FacilityModel>();
  public listOfPhysicians = Array<PhysicianModel>();
  public totalEncounterCount: number = 0;
  public totalCPTCount: number = 0;
  public envChartReqeust: any = {};
  public cptChartRequest: any = {};
  public top5EnvReqeust: any = {};
  public top5CPTRequest: any = {};
  public searchForm: FormGroup;
  public submitted: boolean = false;
  public lstOfEncounterReportData: Array<any> = [];
  public lstOfEncounterReportDataByPhysician: Array<any> = [];
  public lstOfCPTReportData: Array<any> = [];
  public lstOfTop5EncounterReportData: Array<any> = [];
  public lstOfTop5CPTReportData: Array<any> = [];
  public billedChartRequest: any = {};
  public totalBilledEncounterCount: number = 0;
  public listOfYears: Array<any> = [];
  public lstOfBilledReportData: Array<any> = [];
  public totalBilledCount: number = 0;
  public searchFormForKareo: FormGroup;
  public submittedKareo: boolean = false;
  public listOfFacilitiesForKareo = Array<FacilityModel>();
  public totalApprovedCount: number = 0;
  public totalUnApprovedCount: number = 0;
  public listOfPhysiciansForKareo = Array<PhysicianModel>();
  public loadEncounterRpt: boolean = false;
  public loadCptRpt: boolean = false;
  public loadTopEncounterRpt: boolean = false;
  public loadTopCptRpt: boolean = false;
  public loadKareoRpt: boolean = false;
  public userAccess: any = {};
  public listOfBillingPendingInGR: number = 0;
  public mDdlGroupsSettings: any = {};
  public listOfGroups: Array<any> = [];
  public selectedListOfGroups: Array<any> = [];
  public mDdlPhysicianSettings: any = {};
  public selectedListOfPhysicians: Array<any> = [];
  public showERD : boolean = true;
  public showERDwP : boolean = false;
  constructor(private readonly commonServ: CommonService, private readonly appComp: AppComponent, private readonly reptServ: ReportsService
    , private readonly fb: FormBuilder, private readonly excelService: ExcelServices, private readonly encrDecr: EncrDecrServiceService) {
    this.userAccess = this.appComp.userAccess;
  }

  ngOnInit() {
    this.getYears();
    let currentYear = new Date().getFullYear();
    this.appComp.loadPageName('Grand Rounds - CPT Analytics', 'dashboardTab');

    this.searchForm = this.fb.group({
      ddlFacility: ['All', Validators.required],
      ddlPhysican: [''],
      ddlGroups: [''],
      ddlYear: [`${currentYear}`, Validators.required]
    });
    this.searchFormForKareo = this.fb.group({
      kareoFacility: ['All', Validators.required],
      kareoPhysican: ['All', Validators.required],
      kareoYear: [`${currentYear}`, Validators.required]
    });

    this.mDdlGroupsSettings = {
      singleSelection: false,
      idField: 'group_id',
      textField: 'group_name',
      enableCheckAll: true,
      itemsShowLimit: 1,
      allowSearchFilter: true,
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
    };
    this.mDdlPhysicianSettings = {
      singleSelection: false,
      idField: 'physicianEmaiId',
      textField: 'physicianName',
      enableCheckAll: true,
      itemsShowLimit: 1,
      allowSearchFilter: true,
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
    };

    this.envChartReqeust.FacilityName = 'All';
    this.envChartReqeust.group_ids = 'All';
    this.envChartReqeust.PhysicanEmail = 'All';
    this.envChartReqeust.year = Number(this.searchForm.value.ddlYear);

    this.cptChartRequest.FacilityName = 'All';
    this.cptChartRequest.group_ids = 'All';
    this.cptChartRequest.PhysicanEmail = 'All';
    this.cptChartRequest.year = Number(this.searchForm.value.ddlYear);


    this.top5EnvReqeust.yearvalue = Number(this.searchForm.value.ddlYear);
    this.top5EnvReqeust.flag = "Encounters";
    this.top5EnvReqeust.facility = 'All';
    this.top5EnvReqeust.group_ids = 'All';

    this.top5CPTRequest.yearvalue = Number(this.searchForm.value.ddlYear);
    this.top5CPTRequest.flag = "CPT";
    this.top5CPTRequest.facility = 'All';
    this.top5CPTRequest.group_ids = 'All';

    this.billedChartRequest.FacilityName = this.encrDecr.set('All');
    this.billedChartRequest.PhysicanEmail = this.encrDecr.set('All');
    this.billedChartRequest.PhysicanName = this.encrDecr.set('All');
    this.billedChartRequest.sYearvalue = this.encrDecr.set(this.searchFormForKareo.value.kareoYear);
    this.billedChartRequest.DisplayType = this.encrDecr.set('Chart');
    this.billedChartRequest.CPTValue = this.encrDecr.set('');

    this.loadFacilities();
    this.loadEncounterChartsData(this.envChartReqeust);
    this.loadCPTUsedCountByFacility_Chart(this.cptChartRequest);
    this.load_TOP_ENCOUNTERS_USED(this.top5EnvReqeust);
    this.load_TOP_CPT_USED(this.top5CPTRequest);
    this.loadBilledEncountersChartsData(this.billedChartRequest);
    this.commonServ.stopLoading();

  }

  loadFacilities() {
    forkJoin(
      this.commonServ.getFacilities(),
      this.commonServ.getFacilitiesForKareo()
    ).subscribe(([facilities, facilitiesForKareo]) => {
      this.getFacilities(facilities);
      this.getFacilitiesForKareo(facilitiesForKareo);
    }, error => {
      console.error(error.status);
    });
  }

  loadEncounterChartsData(envChartReqeust) {
    this.loadEncounterRpt = false;
    this.reptServ.analtics_GET_ENCOUNTERS_DATA_BY_Facility(envChartReqeust).subscribe((envChart: any) => {
      this.getEncounterChartsData(envChart);
      this.loadEncounterRpt = true;
    }, error => {
      console.error(error.status);
      this.loadEncounterRpt = true;
    });
  }

  loadCPTUsedCountByFacility_Chart(cptChartRequest) {
    this.loadCptRpt = false;
    this.reptServ.getCPTUsedCountByFacility_Chart(cptChartRequest).subscribe((cptChart: any) => {
      this.getCPTChartsData(cptChart);
      this.loadCptRpt = true;
    }, error => {
      this.loadCptRpt = true;
    });
  }

  load_TOP_ENCOUNTERS_USED(top5EnvReqeust) {
    this.loadTopEncounterRpt = false;
    this.reptServ.get_TOP_ENCOUNTERS_CPT_USED(top5EnvReqeust).subscribe((top5Env: any) => {
      this.getTop5EncounterChartsData(top5Env);
      this.loadTopEncounterRpt = true;
    }, error => {
      this.loadTopEncounterRpt = true;
    });
  }

  load_TOP_CPT_USED(top5CPTRequest) {
    this.loadTopCptRpt = false;
    this.reptServ.get_TOP_ENCOUNTERS_CPT_USED(top5CPTRequest).subscribe((top5CPT: any) => {
      this.getTop5CPTChartsData(top5CPT);
      this.loadTopCptRpt = true;
    }, error => {
      this.loadTopCptRpt = true;
    });
  }

  loadBilledEncountersChartsData(billedChartRequest) {
    this.loadKareoRpt = false;
    this.reptServ.getKareoEncounterStatusReport(billedChartRequest).subscribe((billedChart: any) => {
      this.getBilledEncountersChartsData(billedChart);
      this.loadKareoRpt = true;
    }, error => {
      console.error(error.status);
      this.loadKareoRpt = true;
    });
  }

  getYears() {
    let max = new Date().getFullYear(),
      min = max - 10;
    let years: any[] = [];
    for (let i = min; i <= max; i++) {

      years.push(i);
    }
    this.listOfYears = years;

  }

  get f() { return this.searchForm.controls; };
  get fKareo() { return this.searchFormForKareo.controls; };

  getGetFilteredReport() {
    this.submitted = true;
    if (this.searchForm.invalid) {
      return;
    }

    let group_ids: string = '';
    if (this.selectedListOfGroups.length == 0) {
      group_ids = 'All';
    }
    else {
      this.selectedListOfGroups.forEach((grp: any) => {
        group_ids = group_ids + grp.group_id + ',';
      });
    }
    let physyn_emails: string = '';
    if (this.selectedListOfPhysicians.length == 0 || this.selectedListOfPhysicians.length == this.listOfPhysicians.length) {
      physyn_emails = 'All';
    }
    else {
      this.selectedListOfPhysicians.forEach((phy: any) => {
        physyn_emails = physyn_emails + phy.physicianEmaiId + ',';
      });
    }

    this.envChartReqeust.FacilityName = this.searchForm.value.ddlFacility;
    this.envChartReqeust.group_ids = group_ids;
    this.envChartReqeust.PhysicanEmail = physyn_emails;
    this.envChartReqeust.year = Number(this.searchForm.value.ddlYear);

    this.cptChartRequest.FacilityName = this.searchForm.value.ddlFacility;
    this.cptChartRequest.group_ids = group_ids;
    this.cptChartRequest.PhysicanEmail = physyn_emails;
    this.cptChartRequest.year = Number(this.searchForm.value.ddlYear);

    this.top5EnvReqeust.yearvalue = Number(this.searchForm.value.ddlYear);
    this.top5EnvReqeust.flag = "Encounters";
    this.top5EnvReqeust.facility = this.searchForm.value.ddlFacility;
    this.top5EnvReqeust.group_ids = group_ids;

    this.top5CPTRequest.yearvalue = Number(this.searchForm.value.ddlYear);
    this.top5CPTRequest.flag = "CPT";
    this.top5CPTRequest.facility = this.searchForm.value.ddlFacility;
    this.top5CPTRequest.group_ids = group_ids;

    this.loadEncounterChartsData(this.envChartReqeust);
    this.loadCPTUsedCountByFacility_Chart(this.cptChartRequest);
    this.load_TOP_ENCOUNTERS_USED(this.top5EnvReqeust);
    this.load_TOP_CPT_USED(this.top5CPTRequest);

  }

  getGetFilteredReportForKareo() {
    this.submittedKareo = true;
    if (this.searchFormForKareo.invalid) {
      return;
    }
    this.billedChartRequest.FacilityName = this.encrDecr.set(this.searchFormForKareo.value.kareoFacility);
    this.billedChartRequest.PhysicanName = this.encrDecr.set($('#kareoPhysican option:selected').text().trim());
    this.billedChartRequest.PhysicanEmail = this.encrDecr.set(this.searchFormForKareo.value.kareoPhysican);
    this.billedChartRequest.sYearvalue = this.encrDecr.set(this.searchFormForKareo.value.kareoYear);
    this.billedChartRequest.DisplayType = this.encrDecr.set('Chart');

    this.loadBilledEncountersChartsData(this.billedChartRequest);
  }

  getEncounterChartsData(p) {
    this.envChartReqeust = {};
    this.submitted = false;
    this.totalEncounterCount = p.totalEncounterCount ? p.totalEncounterCount : 0;
    this.lstOfEncounterReportData = p.listofData; 
    this.lstOfEncounterReportDataByPhysician = p.listofDataByPhysician;   
    let facilities: Array<any> = [];
    let seriesArray: Array<any> = [];

    let janArray: Array<any> = [];
    let febArray: Array<any> = [];
    let marArray: Array<any> = [];
    let aprArray: Array<any> = [];
    let mayArray: Array<any> = [];
    let junArray: Array<any> = [];
    let julArray: Array<any> = [];
    let augArray: Array<any> = [];
    let sepArray: Array<any> = [];
    let octArray: Array<any> = [];
    let novArray: Array<any> = [];
    let decArray: Array<any> = [];

    p.listOfFacilities.forEach(x => {
      facilities.push(x.facilityName);
    });

    p.listofData.forEach(x => {
      janArray.push(x.jan);
      febArray.push(x.feb);
      marArray.push(x.march);
      aprArray.push(x.april);
      mayArray.push(x.may);
      junArray.push(x.june);
      julArray.push(x.july);
      augArray.push(x.aug);
      sepArray.push(x.sep);
      octArray.push(x.oct);
      novArray.push(x.nov);
      decArray.push(x.dec);
    });

    let janObj: any = { name: 'JAN', data: janArray };
    seriesArray.push(janObj);
    let febObj: any = { name: 'FEB', data: febArray };
    seriesArray.push(febObj);
    let marObj: any = { name: 'MAR', data: marArray };
    seriesArray.push(marObj);
    let aprObj: any = { name: 'APR', data: aprArray };
    seriesArray.push(aprObj);
    let mayObj: any = { name: 'MAY', data: mayArray };
    seriesArray.push(mayObj);
    let junObj: any = { name: 'JUN', data: junArray };
    seriesArray.push(junObj);
    let julObj: any = { name: 'JUL', data: julArray };
    seriesArray.push(julObj);
    let aguObj: any = { name: 'AUG', data: augArray };
    seriesArray.push(aguObj);
    let sepObj: any = { name: 'SEP', data: sepArray };
    seriesArray.push(sepObj);
    let octObj: any = { name: 'OCT', data: octArray };
    seriesArray.push(octObj);
    let novObj: any = { name: 'NOV', data: novArray };
    seriesArray.push(novObj);
    let decObj: any = { name: 'DEC', data: decArray };
    seriesArray.push(decObj);

    Highcharts.setOptions({
      colors: ['#50B432', '#ED561B', '#DDDF00', '#24CBE5', '#64E572', '#FF9655', '#FFF263', '#6AF9C4', '#4B0082', '#2F4F4F', '#0000FF', '#6B8E23', '#FFFF00']
    });
    let optionEncounter: any = {

      chart: {
        type: 'column'
      },

      title: {
        text: 'Encounters Analysis - Jan to Dec ' + this.searchForm.value.ddlYear
      },
      xAxis: { categories: facilities },
      yAxis: {
        allowDecimals: false,
        min: 0,
        title: {
          text: 'Number of Encounters'
        }
      },
      tooltip: {
        pointFormat: '<span style="color:{series.color}">{series.name}</span>: <b>{point.y}</b> ({point.percentage:.1f}%)<br/>',
        shared: true
      },

      plotOptions: {
        column: {
          dataLabels: {
            enabled: true,
            align: 'right',
            format: '{point.percentage:.1f} %',

            style: {
              color: "#333333",
              fontSize: "10px"

            }
          },
          stacking: 'normal'
        }
      },
      series: seriesArray
    };
    Highcharts.chart('conatinerEncounterChart', optionEncounter);
  }

  getCPTChartsData(p) {
    this.cptChartRequest = {};
    this.submitted = false;
    this.totalCPTCount = p.totalCPTCount ? p.totalCPTCount : 0;
    this.lstOfCPTReportData = p.listofData;
    //this.lstOfEncounterReportDataByPhysician = p.listofDataByPhysician;
    let facilities: Array<any> = [];
    let seriesArray: Array<any> = [];

    let janArray: Array<any> = [];
    let febArray: Array<any> = [];
    let marArray: Array<any> = [];
    let aprArray: Array<any> = [];
    let mayArray: Array<any> = [];
    let junArray: Array<any> = [];
    let julArray: Array<any> = [];
    let augArray: Array<any> = [];
    let sepArray: Array<any> = [];
    let octArray: Array<any> = [];
    let novArray: Array<any> = [];
    let decArray: Array<any> = [];

    p.listOfFacilities.forEach(x => {
      facilities.push(x.facilityName);
    });

    p.listofData.forEach(x => {
      janArray.push(x.jan);
      febArray.push(x.feb);
      marArray.push(x.march);
      aprArray.push(x.april);
      mayArray.push(x.may);
      junArray.push(x.june);
      julArray.push(x.july);
      augArray.push(x.aug);
      sepArray.push(x.sep);
      octArray.push(x.oct);
      novArray.push(x.nov);
      decArray.push(x.dec);
    });

    let janObj: any = { name: 'JAN', data: janArray };
    seriesArray.push(janObj);
    let febObj: any = { name: 'FEB', data: febArray };
    seriesArray.push(febObj);
    let marObj: any = { name: 'MAR', data: marArray };
    seriesArray.push(marObj);
    let aprObj: any = { name: 'APR', data: aprArray };
    seriesArray.push(aprObj);
    let mayObj: any = { name: 'MAY', data: mayArray };
    seriesArray.push(mayObj);
    let junObj: any = { name: 'JUN', data: junArray };
    seriesArray.push(junObj);
    let julObj: any = { name: 'JUL', data: julArray };
    seriesArray.push(julObj);
    let aguObj: any = { name: 'AUG', data: augArray };
    seriesArray.push(aguObj);
    let sepObj: any = { name: 'SEP', data: sepArray };
    seriesArray.push(sepObj);
    let octObj: any = { name: 'OCT', data: octArray };
    seriesArray.push(octObj);
    let novObj: any = { name: 'NOV', data: novArray };
    seriesArray.push(novObj);
    let decObj: any = { name: 'DEC', data: decArray };
    seriesArray.push(decObj);

    Highcharts.setOptions({
      colors: ['#50B432', '#ED561B', '#DDDF00', '#24CBE5', '#64E572', '#FF9655', '#FFF263', '#6AF9C4', '#4B0082', '#2F4F4F', '#0000FF', '#6B8E23', '#FFFF00']
    });
    let optionEncounter: any = {

      chart: {
        type: 'column'
      },

      title: {
        text: 'CPT Analysis - Jan to Dec ' + this.searchForm.value.ddlYear
      },
      xAxis: { categories: facilities },
      yAxis: {
        allowDecimals: false,
        min: 0,
        title: {
          text: 'Number of CPT'
        }
      },
      tooltip: {
        pointFormat: '<span style="color:{series.color}">{series.name}</span>: <b>{point.y}</b> ({point.percentage:.1f}%)<br/>',
        shared: true
      },

      plotOptions: {
        column: {
          dataLabels: {
            enabled: true,
            align: 'right',
            format: '{point.percentage:.1f} %',

            style: {
              color: "#333333",
              fontSize: "10px"

            }
          },
          stacking: 'normal'
        }
      },
      series: seriesArray
    };
    Highcharts.chart('conatinerCPTChart', optionEncounter);
  }

  getTop5EncounterChartsData(p) {
    this.top5EnvReqeust = {};
    this.submitted = false;
    let seriesArray: Array<any> = [];
    // Top 5 Physicians by Encounters starts
    this.lstOfTop5EncounterReportData = p;
    p.forEach(x => {
      let obj = {
        name: x.physicianName,
        y: x.count,
        sliced: true,
        selected: true
      }
      seriesArray.push(obj);
    });
    let optionsPhybyEncounter: any = {
      chart: {
        plotBackgroundColor: null,
        plotBorderWidth: null,
        plotShadow: false,
        type: 'pie'
      },
      title: {
        text: 'Top 5 Physicians by Encounters - ' + this.searchForm.value.ddlYear
      },
      tooltip: {
        pointFormat: '<span style="color:{series.color}">{series.name}</span>: <b>{point.y}</b> ({point.percentage:.1f}%)'
      },
      plotOptions: {
        pie: {
          allowPointSelect: true,
          cursor: 'pointer',
          dataLabels: {
            enabled: true,
            align: 'right',
            format: '{point.percentage:.1f} %',

            style: {
              color: "#333333",
              fontSize: "10px"

            }
          },
          stacking: 'percent',
          showInLegend: true
        }
      },
      series: [{
        name: 'Encounters',
        colorByPoint: true,
        data: seriesArray
      }]
    }
    Highcharts.chart('containertop10phybyEnt', optionsPhybyEncounter);
  }

  getTop5CPTChartsData(p) {
    this.top5CPTRequest = {};
    this.submitted = false;
    let seriesArray: Array<any> = [];
    this.lstOfTop5CPTReportData = p;
    p.forEach(x => {
      let obj = {
        name: x.physicianName,
        y: x.count,
        sliced: true,
        selected: true
      }
      seriesArray.push(obj);
    });

    let optionsPhybyCPTs: any = {
      chart: {
        plotBackgroundColor: null,
        plotBorderWidth: null,
        plotShadow: false,
        type: 'pie'
      },
      title: {
        text: 'Top 5 Physicians by CPTs - ' + this.searchForm.value.ddlYear
      },
      tooltip: {
        pointFormat: '<span style="color:{series.color}">{series.name}</span>: <b>{point.y}</b> ({point.percentage:.1f}%)'
      },
      plotOptions: {
        pie: {
          allowPointSelect: true,
          cursor: 'pointer',
          dataLabels: {
            enabled: true,
            align: 'right',
            format: '{point.percentage:.1f} %',

            style: {
              color: "#333333",
              fontSize: "10px"

            }
          },
          stacking: 'percent',
          showInLegend: true
        }
      },
      series: [{
        name: 'CPT',
        colorByPoint: true,
        data: seriesArray
      }]
    }
    Highcharts.chart('containertop10phybyCPTs', optionsPhybyCPTs);
  }
  reduceFun(x, y) {
    return x + y;
  } 
  getBilledEncountersChartsData(p) {
    this.cptChartRequest = {};
    this.submitted = false;
    this.totalBilledCount = p.listofData.length && p.listofData[0].total ? p.listofData[0].total : 0;
    this.lstOfBilledReportData = p.listofData;
    this.lstOfBilledReportData.forEach(report => {
      report.janColor = report.ajan < report.jan ? 'unapproved_count' : 'approved_count';
      report.febColor = report.afeb < report.feb ? 'unapproved_count' : 'approved_count';
      report.marchColor = report.amarch < report.march ? 'unapproved_count' : 'approved_count';
      report.aprilColor = report.aapril < report.april ? 'unapproved_count' : 'approved_count';
      report.mayColor = report.amay < report.may ? 'unapproved_count' : 'approved_count';
      report.juneColor = report.ajune < report.june ? 'unapproved_count' : 'approved_count';
      report.julyColor = report.ajuly < report.july ? 'unapproved_count' : 'approved_count';
      report.augColor = report.aaug < report.aug ? 'unapproved_count' : 'approved_count';
      report.sepColor = report.asep < report.sep ? 'unapproved_count' : 'approved_count';
      report.octColor = report.aoct < report.oct ? 'unapproved_count' : 'approved_count';
      report.novColor = report.anov < report.nov ? 'unapproved_count' : 'approved_count';
      report.decColor = report.adec < report.dec ? 'unapproved_count' : 'approved_count';
    });
    let facilities: Array<any> = [];
    let seriesArray: Array<any> = [];

    let billedArray: Array<any> = [];
    let approvedArray: Array<any> = [];
    let unApprovedArray: Array<any> = [];


    p.listofData.forEach(x => {
      facilities.push(x.facilityName);
    });

    p.listofData.forEach(x => {
      let billedData = x.jan + x.feb + x.march + x.april + x.may + x.june + x.july + x.aug + x.sep + x.oct + x.nov + x.dec;
      let approvedData = x.ajan + x.afeb + x.amarch + x.aapril + x.amay + x.ajune + x.ajuly + x.aaug + x.asep + x.aoct + x.anov + x.adec;
      let unApprovedData = billedData - approvedData;
      billedArray.push(billedData);
      approvedArray.push(approvedData);
      unApprovedArray.push(unApprovedData);
    });

    this.totalBilledEncounterCount = billedArray.length ? billedArray.reduce(this.reduceFun,0) : 0;
    this.totalApprovedCount = approvedArray.length ? approvedArray.reduce(this.reduceFun,0) : 0;
    this.totalUnApprovedCount = unApprovedArray.length ? unApprovedArray.reduce(this.reduceFun,0) : 0;
    let totalEnvCount: number = p.totalEncounterCounts ? p.totalEncounterCounts : 0;
    this.listOfBillingPendingInGR = totalEnvCount - this.totalBilledEncounterCount;

    let approvedObj: any = { name: 'Approved', data: approvedArray };
    seriesArray.push(approvedObj);
    let unApproveObj: any = { name: 'Billed in Grand Rounds But Unbilled in Kareo', data: unApprovedArray };
    seriesArray.push(unApproveObj);

    Highcharts.setOptions({
      colors: ['#50B432', '#ED561B', '#DDDF00', '#24CBE5', '#64E572', '#FF9655', '#FFF263', '#6AF9C4', '#4B0082', '#2F4F4F', '#0000FF', '#6B8E23', '#FFFF00']
    });
    let optionEncounter: any = {

      chart: {
        type: 'column'
      },

      title: {
        text: 'Billed & Approved Encounters Analysis - ' + this.searchFormForKareo.value.kareoYear
      },
      xAxis: {
        categories: facilities,
        title: {
          text: 'Facilities'
        }
      },
      yAxis: {
        allowDecimals: false,
        min: 0,
        title: {
          text: 'Number of Encounters'
        }
      },
      tooltip: {
        pointFormat: '<span style="color:{series.color}">{series.name}</span>: <b>{point.y}</b> ({point.percentage:.1f}%)<br/>',
        shared: true
      },
      plotOptions: {
        column: {
          dataLabels: {
            enabled: true,
            align: 'right',
            format: '{point.percentage:.1f} %',

            style: {
              color: "#333333",
              fontSize: "10px"

            }
          },
          stacking: 'normal'
        }
      },
      series: seriesArray
    };
    Highcharts.chart('billedConatinerEncounterChart', optionEncounter);
  }
  removeCert() {
    setTimeout(function () { $('.highcharts-credits').hide(); }, 3000);
  }
  getFacilities(p) {
    this.listOfFacilities = p;
  }

  getFacilitiesForKareo(p) {
    this.listOfFacilitiesForKareo = p;
  }

  getPhysiciansByFacility(facillity) {
    this.commonServ.startLoading();
    this.commonServ.getPhysiciansByFacility(facillity).subscribe((p: any) => {
      this.listOfPhysicians = p;
      this.commonServ.stopLoading();
    }, error => {
      this.commonServ.stopLoading();
    });
  }
  getPhysiciansByFacilityForKareo(facillity) {
    this.commonServ.startLoading();
    this.commonServ.getPhysiciansByFacilityForReports(facillity).subscribe((p: any) => {
      this.listOfPhysiciansForKareo = p;
      this.commonServ.stopLoading();
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  exportAsXLSXEncounter(): void {
    let download = confirm("Do you want download?");

    if (download) {
      let fileName = "";
      fileName = 'Encounter_Report_' + this.searchForm.value.ddlFacility + '_' + this.searchForm.value.ddlYear;
      this.excelService.exportAsExcelFile(this.lstOfEncounterReportData, fileName);
    }
  }

  exportAsXLSXCPT(): void {
    let download = confirm("Do you want download?");

    if (download) {
      let fileName = "";
      fileName = 'CPT_Report_' + this.searchForm.value.ddlFacility + '_' + this.searchForm.value.ddlYear;
      this.excelService.exportAsExcelFile(this.lstOfCPTReportData, fileName);
    }
  };

  exportAsXLSXTop5Encounter(): void {
    let download = confirm("Do you want download?");

    if (download) {
      let fileName = "";
      fileName = 'Encounter_Top5_' + this.searchForm.value.ddlYear;
      this.excelService.exportAsExcelFile(this.lstOfTop5EncounterReportData, fileName);
    }
  }

  exportAsXLSXTop5CPT(): void {
    let download = confirm("Do you want download?");

    if (download) {
      let fileName = "";
      fileName = 'CPT_Top5_' + this.searchForm.value.ddlYear;
      this.excelService.exportAsExcelFile(this.lstOfTop5CPTReportData, fileName);
    }
  }

  billedExportAsXLSXEncounter(): void {
    let download = confirm("Do you want download?");

    if (download) {
      let fileName = "";
      fileName = 'BilledEncounter_Report_' + this.searchFormForKareo.value.kareoFacility + '_' + this.searchFormForKareo.value.kareoPhysican + '_' + this.searchFormForKareo.value.kareoYear;
      this.excelService.exportAsExcelFile(this.lstOfBilledReportData, fileName);
    }
  }

  getGroupsByFacility(facillity, type: boolean) {
    if (type) {
      this.commonServ.startLoading();
    }
    this.reptServ.getGroupNameByFacility(facillity).subscribe((p: any) => {
      this.selectedListOfGroups = [];
      this.listOfGroups = [];
      this.selectedListOfPhysicians = [];
      this.listOfPhysicians = [];
      this.listOfGroups = p;
      if (type) {
        this.commonServ.stopLoading();
      }
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  onGroupSelect(item: any) {
    this.commonServ.startLoading();
    let group_names: string = '';
    this.selectedListOfGroups.forEach((grp: any) => {
      group_names = group_names + grp.group_name + ',';
    });
    this.reptServ.getPhysiciansByGroup(group_names).subscribe((p: any) => {
      this.commonServ.stopLoading();
      this.selectedListOfPhysicians = [];
      this.listOfPhysicians = [];
      this.listOfPhysicians = p;
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  onGroupSelectAll(item: any, type: string) {
    this.commonServ.startLoading();
    let group_names: string = '';
    if (type == 'S') {
      this.listOfGroups.forEach((grp: any) => {
        group_names = group_names + grp.group_name + ',';
      });
      this.reptServ.getPhysiciansByGroup(group_names).subscribe((p: any) => {
        this.commonServ.stopLoading();
        this.selectedListOfPhysicians = [];
        this.listOfPhysicians = [];
        this.listOfPhysicians = p;
      }, error => {
        this.commonServ.stopLoading();
      });
    }
    else {
      this.commonServ.stopLoading();
      this.selectedListOfPhysicians = [];
      this.listOfPhysicians = [];
    }
  }
  expandTabs(first,sec)
  {
    this.showERD = !first;
    this.showERDwP = !sec;
  }
}
