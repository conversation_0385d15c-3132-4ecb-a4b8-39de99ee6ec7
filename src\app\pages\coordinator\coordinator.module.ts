
import { CoordinationRoutingModule } from '../coordinator/coordinator-routing.module'
import { NgModule } from '@angular/core';
import { ChartModule } from 'angular-highcharts';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';
import { CommonModule } from '@angular/common';
import { CoordinatorComponent } from './coordinator.component';
import { CoordinatorviewEditComponent } from './coordinatorview-edit.component';
import { EncounterComponent } from './encounter.component';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { UnDischargePatientComponent } from './un-discharge-patient.component';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { CommonTaskModule } from '../common/common.module';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { AssignmentsHistoryComponent } from './assignments-history.component';
import { MarkAsAllApprovedComponent } from './mark-as-all-approved.component';
import { AuditViewComponent } from './audit-view.component';
import { SharedModule } from 'src/app/shared.module';
import { NgxDaterangepickerMd } from 'ngx-daterangepicker-material';

@NgModule({
  declarations: [
    CoordinatorComponent,
    CoordinatorviewEditComponent,
    EncounterComponent,
    UnDischargePatientComponent,
    AssignmentsHistoryComponent,
    MarkAsAllApprovedComponent,
    AuditViewComponent
  ],
  imports: [
    CommonModule,
    CoordinationRoutingModule,
    ChartModule,
    FormsModule,
    ReactiveFormsModule,
    NgxPaginationModule,
    NgMultiSelectDropDownModule.forRoot(),
    BsDatepickerModule.forRoot(),
    CommonTaskModule,
    TooltipModule.forRoot(),
    SharedModule,
    NgxDaterangepickerMd.forRoot()
  ],
  providers: [],
  bootstrap: [CoordinatorComponent]
})
export class CoordinatorModule { }