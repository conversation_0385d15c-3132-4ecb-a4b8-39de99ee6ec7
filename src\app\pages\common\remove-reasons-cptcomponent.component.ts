import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
declare let $: any;

@Component({
  selector: 'app-remove-reasons-cptcomponent',
  templateUrl: './remove-reasons-cptcomponent.component.html',
  styles: []
})
export class RemoveReasonsCPTComponentComponent implements OnInit {
  @Input() lisfOfCPTData: any = {};
  @Input() cptCode: string = "";
  @Output() eventRemoveReason = new EventEmitter<any>();
  @Output() eventCancelRemoveReason = new EventEmitter<any>();

  reasonForm: any;
  public submitted: boolean = false;

  constructor(private readonly fb: FormBuilder) { }

  ngOnInit() {
    this.reasonForm = this.fb.group({
      txtMessage: ['', [Validators.required, Validators.maxLength(400), Validators.minLength(3)]]
    });
  }
  // convenience getter for easy access to form fields
  get f() { return this.reasonForm.controls; }

  saveReasons() {
    this.submitted = true;
    if (this.reasonForm.invalid) {
      return;
    }
    else {

      this.eventRemoveReason.emit(this.reasonForm.value.txtMessage);
      $('#confirmAndReasonsDeleteCPT').modal('hide');
      this.reasonForm.reset();
      this.submitted = false;
    }

  }
  cancelSaveReasons() {
    this.eventCancelRemoveReason.emit("CancelSaveReason");
    $('#confirmAndReasonsDeleteCPT').modal('hide');
    this.reasonForm.reset();
    this.submitted = false;
  }

}
