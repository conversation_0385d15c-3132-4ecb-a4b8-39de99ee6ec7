import { Component, OnInit } from '@angular/core';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { AppComponent } from 'src/app/app.component';
import { ToastrService } from 'ngx-toastr';
import { forkJoin } from 'rxjs';
import { Router } from '@angular/router';
declare let $: any;

@Component({
  selector: 'app-submit-missing-encounter',
  templateUrl: './submit-missing-encounter.component.html',
  styles: [
  ]
})
export class SubmitMissingEncounterComponent implements OnInit {

  public lisfOfMissingEncounters: Array<any> = [];
  public listOfPhysicians: Array<any> = [];
  public PatientObject: any;
  public userType: string = '';

  constructor(private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService,
    private readonly appComp: AppComponent, private readonly toastr: ToastrService, private readonly router: Router) {
    const navigation = this.router.getCurrentNavigation();
    if (!navigation) {
      this.router.navigate(['']);
    }
  }

  ngOnInit() {
    this.appComp.loadPageName(history.state.title, history.state.tabName);
    this.userType = history.state.userType;
  }

  getPatientDetails(request: any) {
    this.commonServ.startLoading();
    let request2: any = {
      AccountNumber: request.PATIENTACCOUNTNUMBER,
      FacilityName: request.FACILITYNAME
    }
    forkJoin(
      this.commonServ.getPatientDetails(request),
      this.commonServ.getMissingEncounters(request2),
      this.commonServ.getPhysiciansByUserType(this.encrDecr.get(request.FACILITYNAME), this.userType + '-ACTIVE')
    ).subscribe((p: any) => {
      if (p[0] != null) {
        this.PatientObject = p[0];
        $("#missingEn").modal('show');
        this.commonServ.stopLoading();
      }
      else {
        this.commonServ.stopLoading();
        this.toastr.error("No Patient Exist with this Account Number.");
      }
      this.lisfOfMissingEncounters = p[1];
      this.listOfPhysicians = p[2];
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

}
