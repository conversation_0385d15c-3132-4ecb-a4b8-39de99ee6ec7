import { Component, Input, EventEmitter, Output, OnInit } from '@angular/core';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { CommonService } from 'src/app/services/common/common.service';
declare let $: any;

@Component({
  selector: 'app-icd-code',
  templateUrl: './icd-code.component.html',
  styleUrls: ['./icd-code.component.css']
})
export class IcdCodeComponent implements OnInit {

  @Input() lisfOfICDData: Array<any> = [];
  @Input() icdType: string = "";
  @Input() encounterObj: any = {};
  public chkValuesArray: Array<any> = [];
  public editedValue: string;
  public searchICDs: string;
  public request: any = {};
  public lisfOfSearchICDsData: Array<any> = [];
  public filterICDs: string;
  public isSearch: boolean = false;
  public timeout: any = null;
  public device: boolean = false;
  @Output() eventListOfRemovedIcds = new EventEmitter<Array<any>>();
  @Output() eventUpdateUpdatedICDDataList = new EventEmitter<Array<any>>();
  constructor(private readonly encrDecr: EncrDecrServiceService, private readonly commonServ: CommonService) { }
  public isICDchange: string = "false";

  ngOnInit() {
    // Detect mobile devices
    if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
      this.device = true;
    } else {
      this.device = false;
    }
  }

  search() {
    this.lisfOfSearchICDsData = [];
    this.filterICDs = "";
    this.isSearch = true;
  }

  fav() {
    this.isSearch = false;
    this.eventUpdateUpdatedICDDataList.emit(this.encounterObj);
  }

  searchICDData(eObj) {
    clearTimeout(this.timeout);

    this.timeout = setTimeout(() => {
      this.commonServ.startLoading();
      this.request.ICDNAME = this.encrDecr.set(this.filterICDs);
      this.request.PHYSICIANMAILID = this.encrDecr.set(eObj.physicianmailid);
      this.commonServ.searchICDData(this.request).subscribe((p: any) => {
        this.lisfOfSearchICDsData = p;
        this.request = {};
        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        console.error(error.status);
      });
    }, 2000);
  }

  chkChangeEventAdd(event) {
    for (let item of this.lisfOfICDData) {
      if (item.icD_ID == event.target.id) {
        item.checked = event.target.checked;
      }
    }
  }

  chkChangeEventUpdate(val) {
    this.editedValue = val;
  }

  chkChangeEventSearchAdd(event) {
    for (let item of this.lisfOfSearchICDsData) {
      if ('sch-' + item.icD_ID == event.target.id) {
        item.checked = event.target.checked;
      }
    }
  }

  editICDData(eObj, cptCode) {
    this.commonServ.startLoading();
    if (this.editedValue) {
      const index: number = eObj.listOfIcds.indexOf(cptCode);
      if (index !== -1) {
        if (!eObj.listOfIcds.includes(this.editedValue)) {
          eObj.listOfIcds[index] = this.editedValue;
          eObj.listOfRemovedIcds.push(cptCode);
          $(".btnSave" + eObj.encounteR_ID).show();


        }
      }
    }
    this.eventListOfRemovedIcds.emit(eObj);
    this.commonServ.stopLoading();
  }



  addICDData(eObj) {
    this.commonServ.startLoading();
    this.lisfOfICDData.forEach(x => {
      if (!eObj.listOfIcds.includes(x.icdname) && x.checked) {
        eObj.listOfIcds.push(x.icdname);
        $(".btnSave" + eObj.encounteR_ID).show();
        this.isICDchange = "true";

      }
    });
    this.commonServ.stopLoading();
  }


  editICDDataSearch(eObj, icdCode) {
    this.commonServ.startLoading();
    if (this.editedValue) {
      const index: number = eObj.listOfIcds.indexOf(icdCode);
      if (index !== -1) {
        if (!eObj.listOfIcds.includes(this.editedValue)) {
          eObj.listOfIcds[index] = this.editedValue;
          eObj.listOfRemovedIcds.push(icdCode);
          $(".btnSave" + eObj.encounteR_ID).show();
          this.isICDchange = "true";

        }
      }
    }
    this.eventListOfRemovedIcds.emit(eObj);
    this.commonServ.stopLoading();
  }

  addICDDataSearch(eObj) {
    this.commonServ.startLoading();
    let codesAdded = false;
    this.lisfOfSearchICDsData.forEach(x => {
      if (!eObj.listOfIcds.includes(x.icdname) && x.checked) {
        eObj.listOfIcds.push(x.icdname);
        codesAdded = true;
      }
    });

    if (codesAdded) {
      $(".btnSave" + eObj.encounteR_ID).show();
      this.isICDchange = "true";
    } else {
      
    }

    this.commonServ.stopLoading();
  }

  addICDDataSearchMultiEncounters(eObj) {
    this.commonServ.startLoading();
    this.lisfOfSearchICDsData.forEach(x => {
      if (!eObj.listOfIcds.includes(x.icdname) && x.checked) {
        eObj.listOfIcds.push(x.icdname);

      }
    });
    this.commonServ.stopLoading();
  }

  favUnfavICDCodesAdd(peid, status, item) {
    let confirmMessage = 'Do you want to ' + (status == 0 ? 'unfavorite' : 'favorite') + ' this ICD Code?';
    if (confirm(confirmMessage)) {
      this.commonServ.startLoading();
      this.request.PHYSICIANMAILID = this.encrDecr.set(peid);
      this.request.STATUS = status;
      this.request.ICD_ID = item.icD_ID;
      this.commonServ.insertICDData(this.request).subscribe((p: any) => {
        this.request = {};
        if (p > 0) {
          item.status = status;
        }
        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
    }
  }

}
