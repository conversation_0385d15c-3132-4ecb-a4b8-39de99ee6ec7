<div class="modal fade" id="updateSeenDate" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title" id="exampleModalCenterTitle">Update Encounter Seen Date</h5>
                <i class="fa fa-times closePopup" data-dismiss="modal"></i>
            </div>
            <div class="modal-body" [formGroup]="sSeenDateForm">
                <div class="row mx-0 align-items-center">
                    <div class="col-9 p-1">
                        <mat-form-field>
                            <mtx-datetimepicker #datetimePicker4 [type]="type" [mode]="mode"
                                [multiYearSelector]="multiYearSelector" [startView]="startView"
                                [twelvehour]="twelvehour" [timeInterval]="timeInterval" [touchUi]="touchUi"
                                [timeInput]="timeInput">
                            </mtx-datetimepicker>
                            <input [mtxDatetimepicker]="datetimePicker4" formControlName="envSeenDate" matInput
                                required [max]="maxDateTime">
                            <mtx-datetimepicker-toggle [for]="datetimePicker4" matSuffix></mtx-datetimepicker-toggle>
                        </mat-form-field>
                        <div class="text-danger">
                            <div *ngIf="submitted && f['envSeenDate'].errors">Encounter Seen Date is required</div>
                            <div *ngIf="vali2">Encounter Seen Date Can't Be Less Than Admission Date</div>
                            <div *ngIf="vali3">Encounter Seen Date Can't Be a Future Date</div>
                        </div>
                    </div>
                    <div class="col-3 p-1">
                        <button type="submit" class="btn btn-outline-info btn-block"
                            (click)="editEncounterSeenDate(encounterObj)">Update</button>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-2">
            </div>
        </div>
    </div>
</div>