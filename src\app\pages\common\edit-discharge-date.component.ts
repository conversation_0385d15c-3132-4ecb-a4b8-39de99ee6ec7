import { Component, OnInit, Input } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { CommonService } from 'src/app/services/common/common.service';
declare let $: any;

@Component({
  selector: 'app-edit-discharge-date',
  templateUrl: './edit-discharge-date.component.html',
  styles: []
})
export class EditDischargeDateComponent implements OnInit {

  @Input() PatientObject: any = {};
  @Input() userType:string;
  public request: any = {};
  public disDateForm: FormGroup;
  public submitted: boolean = false;

  constructor(private readonly fb: FormBuilder,private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService) { }

  ngOnInit() {
    this.disDateForm = this.fb.group({
      dischargeDate: ['', Validators.required]
    });
  }
  get f() { return this.disDateForm.controls; }
  editDischargeDate(pObj) {
    this.submitted = true;
    if (this.disDateForm.invalid) {
      return;
    }
    this.commonServ.startLoading();
    this.request.Account_Number = this.encrDecr.set(pObj.account_Number);
    this.request.Facility_Name = this.encrDecr.set(pObj.facility_Name);
    this.request.Dischrage_Date = this.disDateForm.value.dischargeDate;
    this.commonServ.updateDischargeDate(this.request,this.userType).subscribe((p: any) => {
      this.request = {};
      if (p > 0) {
        let dDate = new Date(this.disDateForm.value.dischargeDate);
        let dDateInFormat = dDate.getMonth() + 1 + '/' + dDate.getDate() + '/' + dDate.getFullYear();
        pObj.discharge_Date = dDateInFormat;
        this.disDateForm.reset();
        this.submitted = false;
        $("#discharge").modal('hide');
      }
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
      console.error(error.status);
    });
  }

}
