import { Component, Input } from '@angular/core';
import { BillingService } from 'src/app/services/billing/billing.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { CommonService } from 'src/app/services/common/common.service';
import { BillingComponent } from './billing.component';
import { ToastrService } from 'ngx-toastr';
declare let $: any;

@Component({
  selector: 'app-mark-as-unbilled',
  templateUrl: './mark-as-unbilled.component.html',
  styles: []
})
export class MarkAsUnbilledComponent  {
  @Input() encounterObj:any;
  @Input() PatientObject:any;
 public request:any={};
  constructor(private readonly billingServ:BillingService,private readonly commonServ:CommonService,private readonly encrDecr: EncrDecrServiceService,private readonly billComp:BillingComponent,
    private readonly toastr: ToastrService) { }
 
  confirmMarkAsUnBilled(eObj,pObj){  
    this.commonServ.startLoading();
    this.request.EncounterId=this.encrDecr.set(eObj.encounteR_ID);
    this.request.AccountNumber=this.encrDecr.set(pObj.account_Number);
    this.request.strGroupID = this.encrDecr.set(eObj.group_Id);
    this.request.StrIsAlreadyBilled = this.encrDecr.set(false);
    this.billingServ.markAsUnBilled(this.request).subscribe((p: any) => {
      this.request={};
      if(p>0)
      {
        this.billComp.refreshEncountersByPatient(pObj,eObj.encounteR_ID);
      }
      this.commonServ.stopLoading();
    },error => {
      this.request={};
      this.commonServ.stopLoading();
      console.error(error.status);
    });
}

}
