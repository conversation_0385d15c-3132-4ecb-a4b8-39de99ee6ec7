<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">

        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new px-0 py-2" style="background: #0169ab;">
                    <div class="align-items-center mx-0 row">
                        <div class="col-12 col-md-10 py-1 pl-2 pr-1 position-relative">
                            <div class="input-group">
                                <input type="text" class="form-control small" maxlength="1000"
                                    placeholder="Search for..." aria-label="Search" aria-describedby="basic-addon2"
                                    [(ngModel)]="search" [ngModelOptions]="{standalone: true}">
                                <div class="input-group-append">
                                    <button class="bg-gradient-light btn text-dark" type="button">
                                        <i class="fas fa-search fa-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-2 py-1 pl-1 pr-2">
                            <button class="btn btn-outline-info btn-block"
                                [state]="{group:group,filterObj:{p:p,searchKey:search}}"
                                [routerLink]="['/usermanagement/add-edit-group']"><i class="fas fa-user-friends"></i>
                                Add Group</button>
                        </div>
                    </div>
                </div>
                <!-- Card Body -->
                <div class="card-body px-2 py-1">
                    <div class="row mx-0">
                        <div class="col-12 px-0">

                            <table class="table table-striped table-bordered table-hover">
                                <tr>
                                    <th class="text-center">Group Name</th>
                                    <th class="text-center">Facility Name</th>
                                    <th class="text-center">Created By</th>
                                    <th class="text-center">Created Date</th>
                                    <th class="text-center">Status</th>
                                    <th class="text-center">Action</th>

                                </tr>
                                <tr
                                    *ngFor="let item of listOfGroups|gridFilter:{groupName:search,facilityName:search,createdBy:search,status:search}:false |paginate:{ itemsPerPage:10,currentPage:p};let i=index">
                                    <td>{{item.groupName}}</td>
                                    <td class="text-center">{{item.facilityName}}</td>
                                    <td>{{item.createdBy}}</td>
                                    <td class="text-center">{{item.createdDate | date: 'MM/dd/yyyy'}}</td>
                                    <td class="text-center">{{item.status}}</td>
                                    <td class="text-center"> <button
                                            class="btn btn-sm btn-outline-info mb-1 text-nowrap"
                                            [state]="{group:item,filterObj:{p:p,searchKey:search}}"
                                            [routerLink]="['/usermanagement/add-edit-group']"><i
                                                class="fa-edit fas"></i> Edit Group</button></td>
                                </tr>
                                <tr>
                                    <td colspan="10" class="m-0 p-0" style="background: white !important;">
                                        <pagination-controls previousLabel="" nextLabel="" (pageChange)="p=$event"
                                            (pageBoundsCorrection)="p=$event"></pagination-controls>
                                    </td>
                                </tr>
                            </table>

                        </div>
                    </div>

                </div>

            </div>
        </div>
        <!-- Area Chart Ends -->
    </div>
</div>
<!-- Begin Page Content Ends -->