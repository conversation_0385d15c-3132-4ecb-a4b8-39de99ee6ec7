import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { PhysicianService } from 'src/app/services/physician/physician.service';
declare let $: any;

@Component({
  selector: 'app-assign-to-others-or-residents',
  templateUrl: './assign-to-others-or-residents.component.html',
  styles: []
})
export class AssignToOthersOrResidentsComponent implements OnInit {
  @Input() patient: any = {};
  @Input() listOfPhysicians: Array<any> = [];
  @Input() listOfProvider: Array<any> = [];
  @Output() eventListOfPatients = new EventEmitter<Array<any>>();
  public listOfPatients: Array<any> = [];
  public submitted: boolean = false;
  public submitted1: boolean = false;
  public request: any = {};
  public FilterPhyForm: FormGroup;
  public FilterResiForm: FormGroup;
  constructor(private readonly encrDecr: EncrDecrServiceService, private readonly toastr: ToastrService, private readonly physicianService: PhysicianService, private readonly commonServ: CommonService, private readonly fb: FormBuilder) { }

  ngOnInit(): void {
    this.FilterPhyForm = this.fb.group({
      ddlPhysician: ['', Validators.required]
    });

    this.FilterResiForm = this.fb.group({
      ddlResiPhysician: ['', Validators.required]
    });
  }
  get f1() { return this.FilterPhyForm.controls; }
  get f2() { return this.FilterResiForm.controls; }

  assignToOthersSave() {
    this.submitted = true;
    if (this.FilterPhyForm.invalid) {
      return;
    }
    this.commonServ.startLoading();
    this.request.Account_Number = this.encrDecr.set(this.patient.account_Number);
    this.request.FacilityName = this.encrDecr.set(this.patient.facility_Name);
    this.request.AttendingProvider = this.encrDecr.set(this.FilterPhyForm.value.ddlPhysician);
    this.physicianService.AssignToothersforPhyList(this.request).subscribe((p: any) => {
      this.request = {};
      this.submitted = false;
      this.eventListOfPatients.emit(this.listOfPatients);
      this.commonServ.stopLoading();
      $('#mdlPhysician').modal('hide');
      this.toastr.success("Patient reassigned successfully", '', { timeOut: 2500 });
    }, error => { console.error(error.status); }
    )
  }

  assignToResidentsSave() {
    this.submitted1 = true;
    if (this.FilterResiForm.invalid) {
      return;
    }
    this.commonServ.startLoading();
    this.request.Account_Number = this.encrDecr.set(this.patient.account_Number);
    this.request.FacilityName = this.encrDecr.set(this.patient.facility_Name);
    this.request.Residents = this.encrDecr.set(this.FilterResiForm.value.ddlResiPhysician);
    this.physicianService.AssignToResidentsforPhy(this.request).subscribe((p: any) => {
      this.request = {};
      this.submitted1 = false;
      this.eventListOfPatients.emit(this.listOfPatients);
      this.commonServ.stopLoading();
      $('#mdlResiPhysician').modal('hide');
      this.toastr.success("Patient reassigned successfully", '', { timeOut: 2500 });
    }, error => { console.error(error.status); }
    )
  }

}
