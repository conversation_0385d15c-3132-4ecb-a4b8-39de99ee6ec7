import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DashboardRoutingModule } from './dashboard-routing.module';
import { CptAnalysisComponent } from './cpt-analysis.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { SharedModule } from 'src/app/shared.module';


@NgModule({
  imports: [
    CommonModule,
    DashboardRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    NgxPaginationModule,
    NgMultiSelectDropDownModule.forRoot(),
    SharedModule
  ],
  exports: [
    CptAnalysisComponent,
  ],
  declarations: [
    CptAnalysisComponent,
  ],
  providers: [],
  bootstrap: [CptAnalysisComponent]
})
export class DashboardModule { }
