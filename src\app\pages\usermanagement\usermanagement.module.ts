import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { UsermanagementRoutingModule } from './usermanagement-routing.module';
import { AddModifyUserComponent } from './add-modify-user.component';
import { AddModifyFacilityComponent } from './add-modify-facility.component';
import { AddModifyGroupsComponent } from './add-modify-groups.component';
import { CopyprofileComponent } from './copyprofile.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { PopoverModule } from 'ngx-bootstrap/popover';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { NgxMultipleDatesModule } from 'ngx-multiple-dates';
import { NgxPaginationModule } from 'ngx-pagination';
import { CommonTaskModule } from '../common/common.module';
import { AddModifyRoleComponent } from './add-modify-role.component';
import { EditUserDetailsComponent } from './edit-user-details.component';
import { AddEditFacilityComponent } from './add-edit-facility.component';
import { AddEditGroupComponent } from './add-edit-group.component';
import { SharedModule } from 'src/app/shared.module';


@NgModule({
  declarations: [AddModifyUserComponent, AddModifyFacilityComponent, AddModifyGroupsComponent, CopyprofileComponent, AddModifyRoleComponent, EditUserDetailsComponent, AddEditFacilityComponent, AddEditGroupComponent],
  imports: [
    CommonModule,
    UsermanagementRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    NgxPaginationModule,
    NgMultiSelectDropDownModule.forRoot(),
    TooltipModule.forRoot(),
    PopoverModule.forRoot(),
    CommonTaskModule,
    MatNativeDateModule,
    MatDatepickerModule,
    MatIconModule,
    NgxMultipleDatesModule,
    MatFormFieldModule,
    SharedModule
  ]
})
export class UsermanagementModule { }
