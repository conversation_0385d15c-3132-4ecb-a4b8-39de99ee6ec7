import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { MsalGuard } from '@azure/msal-angular';
import { AddPatientComponent } from '../common/add-patient.component';
import { SubmitMissingEncounterComponent } from '../common/submit-missing-encounter.component';
import { CoordinatorComponent } from './coordinator.component';
import { CoordinatorviewEditComponent } from './coordinatorview-edit.component';
import { UnDischargePatientComponent } from './un-discharge-patient.component';
import { AssignmentsHistoryComponent } from './assignments-history.component';
import { AuditViewComponent } from './audit-view.component';

const routes: Routes = [
    { path: "search", component: CoordinatorComponent, canActivate: [MsalGuard] },
    { path: "coordinator-edit", component: CoordinatorviewEditComponent, canActivate: [MsalGuard] },
    { path: "submit-missing-encounter", component: SubmitMissingEncounterComponent, canActivate: [MsalGuard] },
    { path: "add-patient", component: AddPatientComponent, canActivate: [MsalGuard] },
    { path: "undo-discharge-patient", component: UnDischargePatientComponent, canActivate: [MsalGuard] },
    { path: "assignments-history", component: AssignmentsHistoryComponent, canActivate: [MsalGuard] },
    { path: "audit-view", component: AuditViewComponent, canActivate: [MsalGuard] },

]
@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class CoordinationRoutingModule { }
