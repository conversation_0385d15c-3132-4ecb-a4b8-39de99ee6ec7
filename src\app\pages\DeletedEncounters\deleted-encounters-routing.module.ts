import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { MsalGuard } from '@azure/msal-angular';
import { DeletedEncounterComponent } from './deleted-encounter.component';

const routes: Routes = [
  { path: '', component: DeletedEncounterComponent, canActivate: [MsalGuard] },

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DeletedEncountersRoutingModule { }
