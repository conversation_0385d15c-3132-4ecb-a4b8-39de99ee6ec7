import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DeletedEncountersRoutingModule } from './deleted-encounters-routing.module';
import { DeletedEncounterComponent } from './deleted-encounter.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonTaskModule } from '../common/common.module';
import { EncounterComponent } from './encounter.component';
import { TooltipModule } from 'ngx-bootstrap/tooltip';


@NgModule({
  declarations: [DeletedEncounterComponent, EncounterComponent],
  imports: [
    CommonModule,
    FormsModule,
    DeletedEncountersRoutingModule,
    ReactiveFormsModule,
    CommonTaskModule,
    TooltipModule.forRoot(),

  ],
  exports: [
    DeletedEncounterComponent
  ],
  providers: [],
  bootstrap: [DeletedEncounterComponent]
})
export class DeletedEncountersModule { }
