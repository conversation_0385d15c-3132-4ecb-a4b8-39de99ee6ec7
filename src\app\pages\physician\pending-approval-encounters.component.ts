import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { ToastrService } from 'ngx-toastr';
import { PhysicianService } from 'src/app/services/physician/physician.service';
import { ExcelServices } from 'src/app/services/common/ExcelService';
import { DatePipe } from '@angular/common';
declare let $: any;

@Component({
  selector: 'app-pending-approval-encounters',
  templateUrl: './pending-approval-encounters.component.html',
  styles: []
})
export class PendingApprovalEncountersComponent implements OnInit {
  public submitted: boolean = false;
  public listOfFacilities: Array<any> = [];
  public request: any = {};
  public p: number = 1;
  public listOfPatients: Array<any> = [];
  public searchByName: string = '';
  public searchByFacilityName: string = '';
  public searchByHide: string = '0';
  public is_check_box_selected: boolean = false;
  public isSelectAllChecked: boolean = false;
  public encounterIds: string = "";
  device = false;
  public userAccess: any = {};
  public filterObj: any = {};
  orderBy = 'desc';
  public timeout: any = null;
  public msrlistOfPatients: Array<any> = [];
  public img1: string = '.././../../assets/img/excel.png';

  sortColumnBy = 'room_Number';
  constructor(private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService
    , private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly toastr: ToastrService, private readonly physicianService: PhysicianService, private readonly excelService: ExcelServices
    , public datepipe: DatePipe) {
    this.userAccess = appComp.userAccess;
  }

  ngOnInit() {
    this.appComp.loadPageName('Pending Approval Encounters', 'physicianTab');
    if (history.state.filterObj) {
      this.filterObj = history.state.filterObj;
      this.searchByFacilityName = this.filterObj.searchByFacilityName;
      this.searchByHide = this.filterObj.searchByHide;
      this.searchByName = this.filterObj.searchByName;
      this.p = this.filterObj.p;

    }

    if (/Android|webOS|iPhone|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) && (window.innerWidth <= 575 || window.innerWidth >= 575)) {
      // some code..
      this.device = true;
    } else {
      this.device = false;
    }
    this.getFacilities();
    this.getPendingApprovalData();
  }

  getFacilities() {
    this.commonServ.getFacilitiesByUserType('PHYSICIAN').subscribe((p: any) => {
      this.listOfFacilities = p;
    }, error => { console.error(error.status); });
  }
  updateSortObj(obj: any) {
    this.sortColumnBy = obj.sortColumnBy;
    this.orderBy = obj.orderBy;
  }
  getPendingApprovalData() {
    this.commonServ.startLoading();
    this.physicianService.getPendingApprovalData().subscribe((p: any) => {
      if (p?.length) {
        this.listOfPatients = this.sortOrderBy(p, this.sortColumnBy, this.orderBy);
        this.msrlistOfPatients = [...this.listOfPatients];
        this.listOfPatients.forEach(element => {
          element.ischecked = false;
        });
      }
      else {
        this.listOfPatients = [];
        this.msrlistOfPatients = [];
      }
      this.commonServ.stopLoading();
    }, error => { console.error(error.status); }
    )
  }
  approveEncounter() {
    this.encounterIds = "";
    if (confirm('Do you want to approve this encounters?')) {
      this.commonServ.startLoading();
      let request: any = {};
      this.listOfPatients.forEach(x => {
        if (x.ischecked) {
          request.FacilityName = this.encrDecr.set(x.facility_Name);
          this.encounterIds = this.encounterIds + "," + x.encounteR_ID;
        }

      });
      request.strEnounterID = this.encrDecr.set(this.encounterIds);
      request.Flag = this.encrDecr.set('Approve');
      this.physicianService.approvePendingEncounters(request).subscribe((p: any) => {
        this.encounterIds = "";
        this.is_check_box_selected = false;
        this.commonServ.stopLoading();
        this.toastr.success("Selected Encounters are Approved Successfully", '', { timeOut: 2500 });
        this.getPendingApprovalData();
      }, error => {
        this.commonServ.stopLoading();
        this.encounterIds = "";
        this.is_check_box_selected = false;
        console.error(error.status);
      });
    }
  }
  chkAllForApprove(event: any) {
    let flag: boolean = event.target.checked;
    this.is_check_box_selected = flag;
    this.listOfPatients.forEach(element => {
      element.ischecked = flag;
    });
  }
  chkHaveanyToApprove() {
    let lstApproveEn = this.listOfPatients.filter(e => e.ischecked === true);
    if (lstApproveEn.length > 0) {
      this.is_check_box_selected = true;
    }
    else {
      this.is_check_box_selected = false;
    }
    let lstUnselectEn = this.listOfPatients.filter(e => e.ischecked === false);
    if (lstUnselectEn.length > 0 || lstApproveEn.length == 0) {
      this.isSelectAllChecked = false;
      $('#selectAll').prop('checked', false);
    }
    if (lstApproveEn.length > 0 && lstUnselectEn.length == 0) {
      this.isSelectAllChecked = true;
      $('#selectAll').prop('checked', true);
    }
  }


  exportAsXLSX(): void {
    this.commonServ.startLoading();
    let fileName = this.appComp.userName + "_PendingApprovalPatients_" + (this.datepipe.transform(new Date(), 'yyyy-MM-dd'));
    this.excelService.exportAsExcelFile(this.listOfPatients, fileName);
    this.commonServ.stopLoading();
  }

  sortOrderBy(list, sortBy, sortOrder) {
    let newArray = [];
    this.p = 1;
    if (sortOrder == 'asc') {
      newArray = list.sort(
        function (a, b) {
          let sortNest: number = (b[sortBy] > a[sortBy]) ? -1 : 0;
          let sort: number = (a[sortBy] > b[sortBy]) ? 1 : sortNest;
          return sort;
        }
      );
    }
    else if (sortOrder == 'desc') {
      newArray = list.sort(
        function (a, b) {
          let sortNest: number = (b[sortBy] > a[sortBy]) ? 1 : 0;
          let sort: number = (a[sortBy] > b[sortBy]) ? -1 : sortNest;
          return sort;
        });
    }
    return newArray;
  }

  search(searchKey, changestatus, facility) {

    clearTimeout(this.timeout);

    this.listOfPatients.forEach(element => {
      element.ischecked = false;
    });
    this.isSelectAllChecked = false;
    this.timeout = setTimeout(() => {
      if (this.msrlistOfPatients.length) {
        let filterbystatus: any[] = []
        if (facility == '') {
          filterbystatus = [...this.msrlistOfPatients].filter(v => v.isHide == changestatus);
        }
        else {
          filterbystatus = [...this.msrlistOfPatients].filter(v => v.isHide == changestatus && v.facility_Name == facility);
        }
        this.listOfPatients = this.filterByValue(filterbystatus, searchKey);
        this.chkHaveanyToApprove();
      }
    }, 2000);

  }

  filterByValue(array, string) {
    return array.filter(o => {
      return Object.keys(o).some(k => {
        if (typeof o[k] === 'string' || typeof o[k] === 'number')
          return o[k].toString().toLowerCase().includes(string.toLowerCase());
      });
    });
  }


}
