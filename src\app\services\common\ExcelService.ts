import { Injectable } from '@angular/core';
import * as ExcelJS from 'exceljs';
const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';
import { saveAs } from 'file-saver';

@Injectable({
  providedIn: 'root'
})
export class ExcelServices {
  constructor() { }
  public exportAsExcelFile(json: any[], excelFileName: string): void {
    const rowHeader: Array<any> = [];
    for (let key in json[0]) {
      let header: any = { key: key };
      rowHeader.push(header);
    }
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('data');

    const col: string[] = [];
    rowHeader.forEach(header => {
      col.push(header.key);
    });
    const columnHeaders: string[] = Object.values(col);
    worksheet.addRow(columnHeaders);
    worksheet.getRow(1).font = {
      bold: true,
    };
    json.forEach((item) => {
      const rowValues: string[] = Object.values(item);
      worksheet.addRow(rowValues);
    });

    workbook.xlsx.writeBuffer().then((buffer) => {
      const blob = new Blob([buffer], { type: EXCEL_TYPE });
      saveAs(blob, `${excelFileName}` + EXCEL_EXTENSION);
    });
  }
}
