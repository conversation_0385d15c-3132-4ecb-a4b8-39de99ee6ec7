<!-- Audit Coordinator popup starts -->
<div class="modal fade" id="viewHistory" tabindex="-1" aria-labelledby="exampleModalCenterTitle" maria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg"
        [ngClass]="{ 'modal-xl': listOfHistory.cptEditHistory?.length>1 }">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="dvHeder">Edit History</h5>
                <i class="fa fa-times closePopup" data-dismiss="modal"></i>
            </div>
            <div class="modal-body">
                <!-- cpt start -->
                <div class="row">
                    <div class="table-responsive">
                        <table class="table table-bordered width-100per">
                            <thead>
                                <tr>
                                    <th>Codes</th>
                                    <th *ngFor="let thData of listOfHistory.cptEditHistory"
                                        class="bg-gradient-primary text-white">{{thData.role}}
                                        {{thData.createddate|date: 'MM/dd/yyyy'}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Cpt Codes</td>
                                    <td *ngFor="let cptData of listOfHistory.cptEditHistory; let i=index">
                                        <ul class="ml-3">
                                            <li *ngFor="let cptData of listOfHistory.cptEditHistory[i]?.cptcodes"
                                                [style.color]="cptData.color" tooltip="{{cptData.deletedReson}}">
                                                {{cptData.code}}</li>
                                        </ul>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Icd Codes</td>
                                    <td *ngFor="let cptData of listOfHistory.icdEditHistory; let i=index">
                                        <ul class="ml-3">
                                            <li *ngFor="let icdData of listOfHistory.icdEditHistory[i]?.icdcodes"
                                                [style.color]="icdData.color">{{icdData.code}}</li>
                                        </ul>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- cpt end -->
            </div>
        </div>
    </div>
</div>
<!-- Audit Billing ends -->