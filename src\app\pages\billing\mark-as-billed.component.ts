import { Component, Input } from '@angular/core';
import { BillingService } from 'src/app/services/billing/billing.service';
import { BillingComponent } from './billing.component';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { CommonService } from 'src/app/services/common/common.service';
import { ToastrService } from 'ngx-toastr';
declare let $: any;

@Component({
  selector: 'app-mark-as-billed',
  templateUrl: './mark-as-billed.component.html',
  styles: []
})
export class MarkAsBilledComponent  {
 @Input() encounterObj:any;
 @Input() PatientObject:any;
 public request:any={};
  constructor(private readonly billingServ:BillingService,private readonly commonServ:CommonService,private readonly billComp:BillingComponent,private readonly encrDecr: EncrDecrServiceService
    ,private readonly toastr: ToastrService) { }
 
  confirmMarkAsBilled(eObj,pObj){
 if (eObj.listOfCpts.length > 0 && eObj.listOfIcds.length > 0) {
    this.commonServ.startLoading();
    if(eObj.sendtointegrationSystem == null || eObj.sendtointegrationSystem =="NO")
    eObj.sendtointegrationSystem="NO";
    this.request.EncounterId=this.encrDecr.set(eObj.encounteR_ID);
    this.request.AccountNumber=this.encrDecr.set(pObj.account_Number);
    this.request.IsEntsSendtoInteSystem=this.encrDecr.set(eObj.sendtointegrationSystem);
    this.request.strGroupID = this.encrDecr.set(eObj.group_Id);
    this.request.StrIsAlreadyBilled = this.encrDecr.set(false);
    this.billingServ.markAsBilled(this.request).subscribe((p: any) => {
      this.request={};
      if(p>0)
      {
        this.billComp.refreshEncountersByPatient(pObj,eObj.encounteR_ID);
      }
      this.commonServ.stopLoading();
    },error => {
      this.request={};
      this.commonServ.stopLoading();
      console.error(error.status);
    });
  }
  else {
    this.toastr.error('Atleast one CPT/HCPCS and ICD Codes required.', '', { timeOut: 1900 });
  }
}

}
