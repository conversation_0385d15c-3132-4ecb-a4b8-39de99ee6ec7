import { Component, OnInit } from '@angular/core';
import * as Highcharts from 'highcharts';
import { CommonService } from 'src/app/services/common/common.service';
import { DashboardService } from 'src/app/services/dashboard/dashboard.service';
import { AppComponent } from 'src/app/app.component';
import { FacilityModel } from 'src/app/models/facility.model';
import { PhysicianModel } from 'src/app/models/physician.model';
import { FormGroup, Validators, FormBuilder } from '@angular/forms';
import { ReportsService } from 'src/app/services/reports/reports.service';
import { ExcelServices } from 'src/app/services/common/ExcelService';
declare let $: any;

@Component({
  selector: 'app-cpt-summary',
  templateUrl: './cpt-summary.component.html',
  styles: []
})
export class CptSummaryComponent implements OnInit {
  public listOfFacilities = Array<FacilityModel>();
  public listOfPhysicians = Array<PhysicianModel>();
  public request: any = {};
  public searchForm: FormGroup;
  public submitted: boolean = false;
  public cPTReportSummaryData: any = {};
  public lstOfReportData: Array<any> = [];
  public listOfYears: Array<any> = [];
  public img1: string = '.././../../assets/img/excel.png';
  public colorsArray: Array<any> = [{ month: 'JAN', color: '#50B432' }, { month: 'FEB', color: '#ED561B' }, { month: 'MARCH', color: '#DDDF00' }, { month: 'APRIL', color: '#24CBE5' }, { month: 'MAY', color: '#64E572' }, { month: 'JUNE', color: '#6336d0' },
  { month: 'JULY', color: '#FF9655' }, { month: 'AUG', color: '#FFF263' }, { month: 'SEP', color: '#6AF9C4' }, { month: 'OCT', color: '#6e707e' }, { month: 'NOV', color: '#9dc6d0' }, { month: 'DEC', color: '#292625' }];
  constructor(private readonly commonServ: CommonService, private readonly dashServ: DashboardService, private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly reptServ: ReportsService
    , private readonly excelService: ExcelServices) { }

  ngOnInit() {
    this.getYears();
    this.getFacilities();

    this.appComp.loadPageName('CPT Summary', 'reportsTab');
    this.searchForm = this.fb.group({
      ddlFacility: ['', Validators.required],
      ddlPhysician: ['', Validators.required],
      ddlFromYear: ['', Validators.required]
    });
  }

  getYears() {
    let max = new Date().getFullYear(),
      min = max - 10;
    let years: any[] = [];
    for (let i = min; i <= max; i++) {

      years.push(i);
    }
    this.listOfYears = years;
  }

  get f() { return this.searchForm.controls; };

  getReportSummary() {
    this.submitted = true;
    if (this.searchForm.invalid) {
      return;
    }

    this.request.facilityName = this.searchForm.value.ddlFacility;
    this.request.physician = this.searchForm.value.ddlPhysician == 'All Physicians' ? null : this.searchForm.value.ddlPhysician;
    this.request.year = this.searchForm.value.ddlFromYear;
    this.reptServ.get_CPT_Summary_Chart(this.request).subscribe((p: any) => {
      let seriesArray: Array<any> = [];

      this.cPTReportSummaryData = p.graphData;
      this.lstOfReportData = p.listOfViewData;

      this.cPTReportSummaryData.forEach(y => {
        let reqColor = this.colorsArray.find(x => x.month == y.month_name);
        let data: any = { name: y.month_name, data: [y.cpT_99221, y.cpT_99222, y.cpT_99223, y.cpT_99231, y.cpT_99232, y.cpT_99233, y.cpT_99291, y.cpT_99292], color: reqColor.color };
        seriesArray.push(data);
      });
      let optionsCPTSummary: any =
      {
        chart: {
          type: 'column',
          renderTo: 'container',
          zoomType: 'xy'
        },
        title: {
          text: $('#ddlPhysician option:selected').text()
        },
        subtitle: {

        },
        xAxis: {
          categories: [99221, 99222, 99223, 99231, 99232, 99233, 99291, 99292],
          title: {
            text: null
          }
        },
        yAxis: {
          min: 0,
          title: {
            text: 'CPT',
            align: 'high'
          },
          labels: {
            overflow: 'justify'
          }
        },
        tooltip: {
          valueSuffix: ' CPT'
        },
        plotOptions: {
          bar: {
            dataLabels: {
              enabled: true,
              minPointLength: 20
            }
          }
        },
        legend: {
          layout: 'vertical',
          align: 'right',
          verticalAlign: 'top',
          x: -40,
          y: 80,
          floating: false,
          borderWidth: 1,
          backgroundColor: Highcharts.defaultOptions.legend?.backgroundColor || '#FFFFFF',
          shadow: true,
          showFirstLabel: true,
          showLastLabel: true
        },
        credits: {
          enabled: false
        },
        series: seriesArray
      };
      $('.highcharts-credits').hide();
      Highcharts.chart('conatinerCPTSummary', optionsCPTSummary);
    }, error => { console.error(error.status); });
  }


  getFacilities() {
    this.commonServ.getFacilities().subscribe((p: any) => {
      this.listOfFacilities = p;
    }, error => { console.error(error.status); });
  }

  getPhysiciansByFacility(facillity) {
    this.commonServ.startLoading();
    this.commonServ.getPhysiciansByFacility(facillity).subscribe((p: any) => {
      this.commonServ.stopLoading();
      this.listOfPhysicians = p;
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  exportAsXLSX(): void {
    let fileName = "";
    fileName = 'CPT_Summary_' + this.searchForm.value.ddlFromYear + '_' + this.searchForm.value.ddlPhysician;
    this.excelService.exportAsExcelFile(this.lstOfReportData, fileName);
  }

}
