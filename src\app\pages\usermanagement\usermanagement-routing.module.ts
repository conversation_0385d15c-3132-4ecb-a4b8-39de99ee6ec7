import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { MsalGuard } from '@azure/msal-angular';
import { AddEditGroupComponent } from './add-edit-group.component';
import { AddModifyFacilityComponent } from './add-modify-facility.component';
import { AddModifyGroupsComponent } from './add-modify-groups.component';
import { AddModifyRoleComponent } from './add-modify-role.component';
import { AddModifyUserComponent } from './add-modify-user.component';
import { EditUserDetailsComponent } from './edit-user-details.component';

const routes: Routes = [
  { path: "add-modify-user", component: AddModifyUserComponent, canActivate: [MsalGuard] },
  { path: "add-modify-facility", component: AddModifyFacilityComponent, canActivate: [MsalGuard] },
  { path: "add-modify-groups", component: AddModifyGroupsComponent, canActivate: [MsalGuard] },
  { path: "add-modify-role", component: AddModifyRoleComponent, canActivate: [MsalG<PERSON>] },
  { path: "edit-user-details", component: EditUserDetailsComponent, canActivate: [MsalGuard] },
  { path: "add-edit-group", component: AddEditGroupComponent, canActivate: [MsalGuard] }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class UsermanagementRoutingModule { }
