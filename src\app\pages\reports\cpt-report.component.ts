import { Component, OnInit } from '@angular/core';
import { CommonService } from 'src/app/services/common/common.service';
import { ReportsService } from 'src/app/services/reports/reports.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { AppComponent } from 'src/app/app.component';
import { ExcelServices } from 'src/app/services/common/ExcelService';
declare let $: any;

@Component({
  selector: 'app-cpt-report',
  templateUrl: './cpt-report.component.html',
  styles: []
})
export class CptReportComponent implements OnInit {
  public listOfFacilities: Array<any> = [];
  public listOfPhysicians: Array<any> = [];
  public listOfReports: Array<any> = [];
  public FilterForm: FormGroup;
  public request: any = {};
  public submitted: boolean = false;
  public p: number;
  public searchByName: string;
  public selectedYear: string;
  public lastRefreshDate: string;
  public isFaiclityAccess: boolean = true;
  public listOfYears: Array<any> = [];
  public img1: string = '.././../../assets/img/excel.png';
  constructor(private readonly reportsServ: ReportsService, private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService,
    private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly excelService: ExcelServices) { }

  ngOnInit() {
    this.getYears();
    let currentYear = new Date().getFullYear();
    this.appComp.loadPageName('CPT Report', 'reportsTab');
    this.selectedYear = `${currentYear}`;
    this.getLastCPTDataRefreshDate();
    this.getFacilities();

    this.FilterForm = this.fb.group({
      ddlFacility: ['', Validators.required],
      ddlPhysician: ['', Validators.required],
      ddlYear: [`${currentYear}`]
    });
  }
  getYears() {
    let max = new Date().getFullYear(),
      min = max - 10;
    let years: any[] = [];
    for (let i = min; i <= max; i++) {

      years.push(i);
    }
    this.listOfYears = years;
  }

  get f() { return this.FilterForm.controls; }

  getFacilities() {
    this.commonServ.getFacilities().subscribe((p: any) => {
      this.listOfFacilities = p;
    }, error => { console.error(error.status); });
  }

  getPhysiciansByFacility(facillity) {
    this.commonServ.startLoading();
    this.commonServ.getPhysiciansByFacility(facillity).subscribe((p: any) => {
      this.commonServ.stopLoading();
      this.listOfPhysicians = p;
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  exportAsXLSX(): void {
    let fileName = "";
    fileName = "CPT_REPORT_" + this.FilterForm.value.ddlFacility + '_' + $('#ddlPhysician option:selected').text() + '_' + $('#ddlYear option:selected').text();
    this.excelService.exportAsExcelFile(this.listOfReports, fileName);
  }

  filterReport() {
    this.submitted = true;
    if (this.FilterForm.invalid) {
      return;
    }
    this.commonServ.startLoading();
    this.selectedYear = this.FilterForm.value.ddlYear;
    this.request.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.PhysicanEmail = this.encrDecr.set(this.FilterForm.value.ddlPhysician);
    this.request.sYearvalue = this.encrDecr.set(this.FilterForm.value.ddlYear);
    this.request.DisplayType = this.encrDecr.set('View');
    this.request.CPTValue = this.encrDecr.set('');
    this.reportsServ.getCPTReportData(this.request).subscribe((p: any) => {
      this.listOfReports = p.listofData;
      this.isFaiclityAccess = p.flag;
      this.request = {};
      this.commonServ.stopLoading();
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  getLastCPTDataRefreshDate() {
    this.reportsServ.getLastCPTDataRefreshDate().subscribe((p: any) => {
      this.lastRefreshDate = p;
    }, error => { console.error(error.status); });
  }

}
