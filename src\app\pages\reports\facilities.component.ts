import { Component, OnInit } from '@angular/core';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';

@Component({
  selector: 'app-facilities',
  templateUrl: './facilities.component.html',
  styles: [
  ]
})
export class FacilitiesComponent implements OnInit {
  public listOfFacilities:Array<any>=[];
  public p: number;
  public search = '';

  constructor(private readonly appComp:AppComponent, private readonly commonServ:CommonService) { }

  ngOnInit(): void {
    this.appComp.loadPageName('View Facilities','reportsTab');
    this.fetchFacilities();
  }

  fetchFacilities(){
    this.commonServ.startLoading();   
    this.commonServ.getFacilitiesDetails().subscribe((p: any) => {
      this.listOfFacilities=p;
      this.commonServ.stopLoading();
    },error => {
        console.error(error.status);
        this.commonServ.stopLoading();
    });
  }

}
