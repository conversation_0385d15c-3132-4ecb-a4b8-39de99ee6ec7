import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { MsalGuard } from '@azure/msal-angular';
import { AlertsReportComponent } from './alerts-report.component';
import { BilledEncounterReportComponent } from './billed-encounter-report.component';
import { CptReportComponent } from './cpt-report.component';
import { CptSummaryBetweenYearsComponent } from './cpt-summary-between-years.component';
import { CptSummaryComponent } from './cpt-summary.component';
import { EncounterReportComponent } from './encounter-report.component';
import { FacilitiesComponent } from './facilities.component';
import { MissingEncounterReportComponent } from './missing-encounter-report.component';
import { NoteReportComponent } from './note-report.component';
import { AssignmentAndEncounterMismatchReportComponent } from './assignment-and-encounter-mismatch-report.component';
import { NextGenEncounterReportComponent } from './next-gen-encounter-report.component';

const routes: Routes = [
  { path: 'encounter-report', component: EncounterReportComponent, canActivate: [MsalGuard] },
  { path: 'cpt-report', component: CptReportComponent, canActivate: [MsalGuard] },
  { path: 'missing-encounter-report', component: MissingEncounterReportComponent, canActivate: [MsalGuard] },
  { path: 'alerts-report', component: AlertsReportComponent, canActivate: [MsalGuard] },
  { path: 'cpt-summary-btween-years', component: CptSummaryBetweenYearsComponent, canActivate: [MsalGuard] },
  { path: 'cpt-summary', component: CptSummaryComponent, canActivate: [MsalGuard] },
  { path: 'billed-encounter-report', component: BilledEncounterReportComponent, canActivate: [MsalGuard] },
  { path: 'notes-report', component: NoteReportComponent, canActivate: [MsalGuard] },
  { path: 'view-facilities', component: FacilitiesComponent, canActivate: [MsalGuard] },
  { path: 'deleted-encounter-report', component: EncounterReportComponent, canActivate: [MsalGuard] },
  { path: 'mismatch-encounter-report', component: AssignmentAndEncounterMismatchReportComponent, canActivate: [MsalGuard] },
  { path: 'next-gen-encounter-report', component: NextGenEncounterReportComponent, canActivate: [MsalGuard] },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ReportsRoutingModule { }
