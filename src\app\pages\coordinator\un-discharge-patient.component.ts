import { Component, OnInit } from '@angular/core';
import { AppComponent } from 'src/app/app.component';
import { FacilityModel } from 'src/app/models/facility.model';
import { CommonService } from 'src/app/services/common/common.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { ToastrService } from 'ngx-toastr';
import { CoordinatorService } from 'src/app/services/coordinator/coordinator.service';
declare let $: any;

@Component({
  selector: 'app-un-discharge-patient',
  templateUrl: './un-discharge-patient.component.html',
  styles: []
})
export class UnDischargePatientComponent implements OnInit {
  public listOfFacilities = Array<FacilityModel>();
  public FilterForm: FormGroup;
  public UnDischargeForm: FormGroup;
  public request: any = {};
  public listofDischargePatients: Array<any> = [];
  public patient: any = {};
  public submitted: boolean = false;

  constructor(private readonly commonServ: CommonService, private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly encrDecr: EncrDecrServiceService, private readonly toastr: ToastrService,
    private readonly coordinatorServ: CoordinatorService) { }

  ngOnInit() {
    this.appComp.loadPageName('Undo Discharge Patient', 'coordinatorTab');
    this.getFacilities();

    this.UnDischargeForm = this.fb.group({

      txtAccountNo: ['', Validators.required],
      ddlFacilityName: ['', Validators.required]

    });
  }

  get f() { return this.UnDischargeForm.controls; }
  getFacilities() {
    this.commonServ.getFacilitiesByUserType('COORDINATOR').subscribe((p: any) => {
      this.listOfFacilities = p;
    }, error => { console.error(error.status); });
  }

  GetDischargePatient() {
    this.submitted = true;
    if (this.UnDischargeForm.invalid) {
      this.commonServ.stopLoading();
      return;
    }
    this.commonServ.startLoading();
    this.request.AccountNumber = this.encrDecr.set(this.UnDischargeForm.value.txtAccountNo);
    this.request.FacilityName = this.encrDecr.set(this.UnDischargeForm.value.ddlFacilityName);
    this.coordinatorServ.GetDischargePatients(this.request).subscribe((p: any) => {
      this.listofDischargePatients = p;
      this.request = {};
      this.submitted = false;
      this.commonServ.stopLoading();
    },
      error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
  }

  openUnDischargePatientPop(item) {
    this.commonServ.startLoading();
    this.patient = item;
    $('#UnDischargePopup').modal('show');
    this.commonServ.stopLoading();
  }

}
