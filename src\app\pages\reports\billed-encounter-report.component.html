<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new p-2" style="background: #0169ab;" [formGroup]="FilterForm">
                    <div class="align-items-center row mx-auto">
                        <div class="col-12 px-0">

                            <div class="row mt-1 mx-auto">
                                <div class="col-12 px-1 py-1"
                                    [ngClass]="{'col-md-3': this.listOfReports.length, 'col-md-4': !this.listOfReports.length}">
                                    <select id="ddlFacility" class="form-control" formControlName="ddlFacility"
                                        (change)="getPhysiciansByFacility($any($event.target).value)"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                                        <option value="">---Select Facility---</option>
                                        <option value="All">All Facilities</option>
                                        <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                                            <span>{{s.facilityName}}</span>
                                        </option>
                                    </select>
                                    <div *ngIf="submitted && f['ddlFacility'].errors" class="invalid-feedback">
                                        <div *ngIf="f['ddlFacility'].errors['required']">Facility is required</div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-3 px-1 py-1">
                                    <select id="ddlPhysician" class="form-control custom-control"
                                        formControlName="ddlPhysician"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlPhysician'].errors}">
                                        <option value="">---Select Physician---</option>
                                        <option value="All">All</option>
                                        <option [value]="s.physicianEmaiId" *ngFor="let s of listOfPhysicians">
                                            {{s.physicianName}}
                                        </option>
                                    </select>
                                    <div *ngIf="submitted && f['ddlPhysician'].errors" class="invalid-feedback">
                                        <div *ngIf="f['ddlPhysician'].errors['required']">Physician is required</div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-3 px-1 py-1">
                                    <select id="ddlYear" class="form-control" formControlName="ddlYear">
                                        <option [value]="year" *ngFor="let year of listOfYears">
                                            <span>{{year}}</span>
                                        </option>
                                    </select>
                                    <div *ngIf="submitted && f['ddlYear'].errors" class="invalid-feedback">
                                        <div *ngIf="f['ddlYear'].errors['required']">Year is required</div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-2 px-1 py-1">
                                    <button class="btn btn-outline-info px-2 btn-block" type="submit"
                                        (click)="filterReport()">
                                        <i class="fa-filter fas font-size-13"></i>
                                        Filter
                                    </button>
                                </div>
                                <div *ngIf="this.listOfReports.length>0" class="col-12 col-md-1 px-1 py-1 text-center">
                                    <img alt=' ' (keyup)="exportAsXLSX()" (click)="exportAsXLSX()" class="ml-2 shadow"
                                        [src]="img1" style="width:40px;cursor:pointer;" title="Export Excel">
                                </div>

                            </div>
                            <div class="row my-1 mx-auto align-items-center">
                                <div class="col-12 px-1 py-1"
                                    [ngClass]="{'col-md-11': this.listOfReports.length, 'col-md-12': !this.listOfReports.length}">
                                    <div class="input-group">
                                        <input type="text" class="form-control small" maxlength="1000"
                                            placeholder="Search for..." aria-label="Search"
                                            aria-describedby="basic-addon2" [(ngModel)]="searchByName"
                                            [ngModelOptions]="{standalone: true}" (keyup)="search(searchByName)">
                                        <div class="input-group-append">
                                            <button class="bg-gradient-light btn text-dark" type="button">
                                                <i class="fas fa-search fa-sm"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Card Body -->
                <div class="card-body px-2 py-1">
                    <div class="row mx-0">
                        <div class="col-12 px-0 pull_right note_text">
                            <div class="display_inline approved_encounters">
                                <span><i class="fas fa-fw fa-circle"></i></span><span>Approved Encounters</span>
                            </div>
                            <div class="display_inline billed_encounters">
                                <span><i class="fas fa-fw fa-circle"></i></span><span>Billed Encounters</span>
                            </div>
                        </div>
                    </div>
                    <div class="row mx-0">
                        <div class="col-12 px-0">

                            <table class="table table-striped table-bordered table-hover billed_encounters_grid">
                                <thead>
                                    <tr class="text-center">
                                        <th class="text-center pb-0 cursor-pointer" (keyup)="sortColumn('facilityName')"
                                            (click)="sortColumn('facilityName')">
                                            Facility Name
                                            <span class="float-right" tooltip="Sort By Facility Name"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'facilityName' && orderByFacilityName == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'facilityName' && orderByFacilityName == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'facilityName'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th class="text-center pb-0 cursor-pointer"
                                            (keyup)="sortColumn('physician_name')"
                                            (click)="sortColumn('physician_name')">
                                            Physician Name
                                            <span class="float-right" tooltip="Sort By Physician Name"
                                                triggers="mouseenter mouseleave click">
                                                <i *ngIf="sortColumnBy == 'physician_name' && orderByPhysician_name == 'desc'"
                                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy == 'physician_name' && orderByPhysician_name == 'asc'"
                                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                <i *ngIf="sortColumnBy != 'physician_name'"
                                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                            </span>
                                        </th>
                                        <th>Jan {{selectedYear}}</th>
                                        <th>Feb {{selectedYear}}</th>
                                        <th>Mar {{selectedYear}}</th>
                                        <th>Apr {{selectedYear}}</th>
                                        <th>May {{selectedYear}}</th>
                                        <th>Jun {{selectedYear}}</th>
                                        <th>Jul {{selectedYear}}</th>
                                        <th>Aug {{selectedYear}}</th>
                                        <th>Sep {{selectedYear}}</th>
                                        <th>Oct {{selectedYear}}</th>
                                        <th>Nov {{selectedYear}}</th>
                                        <th>Dec {{selectedYear}}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <ng-container *ngIf="isFaiclityAccess==true;else ElCase">
                                        <tr
                                            *ngFor="let item of listOfReports | paginate: { itemsPerPage: 8, currentPage: p}">
                                            <td>{{item.facilityName}}</td>
                                            <td>{{item.physician_name}}</td>
                                            <td>
                                                <div class='{{item.janColor}}' title="In Kareo">
                                                    <span
                                                        *ngIf="item.janColor=='unapproved_count' || item.janColorLink =='M';else janEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByKareo(item,selectedYear,1)"
                                                            (click)="getMissMatchedEncountersByKareo(item,selectedYear,1)">{{item.ajan}}</span>
                                                    </span>
                                                    <ng-template #janEls>
                                                        {{item.ajan}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">{{item.jan}}</div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Kareo">
                                                    <span *ngIf="item.ecjan !='0';else janEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByGR(item,selectedYear,1)"
                                                            (click)="getMissMatchedEncountersByGR(item,selectedYear,1)">{{item.ecjan}}</span>
                                                    </span>
                                                    <ng-template #janEcEls>
                                                        {{item.ecjan}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="{{item.febColor}}" title="In Kareo">
                                                    <span
                                                        *ngIf="item.febColor=='unapproved_count' || item.febColorLink =='M';else febEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByKareo(item,selectedYear,2)"
                                                            (click)="getMissMatchedEncountersByKareo(item,selectedYear,2)">{{item.afeb}}</span>
                                                    </span>
                                                    <ng-template #febEls>
                                                        {{item.afeb}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">{{item.feb}}</div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Kareo">
                                                    <span *ngIf="item.ecfeb !='0';else febEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByGR(item,selectedYear,2)"
                                                            (click)="getMissMatchedEncountersByGR(item,selectedYear,2)">{{item.ecfeb}}</span>
                                                    </span>
                                                    <ng-template #febEcEls>
                                                        {{item.ecfeb}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="{{item.marchColor}}" title="In Kareo">
                                                    <span
                                                        *ngIf="item.marchColor=='unapproved_count' || item.marchColorLink =='M';else marEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByKareo(item,selectedYear,3)"
                                                            (click)="getMissMatchedEncountersByKareo(item,selectedYear,3)">{{item.amarch}}</span>
                                                    </span>
                                                    <ng-template #marEls>
                                                        {{item.amarch}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">{{item.march}}</div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Kareo">
                                                    <span *ngIf="item.ecmarch !='0';else marchEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByGR(item,selectedYear,3)"
                                                            (click)="getMissMatchedEncountersByGR(item,selectedYear,3)">{{item.ecmarch}}</span>
                                                    </span>
                                                    <ng-template #marchEcEls>
                                                        {{item.ecmarch}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="{{item.aprilColor}}" title="In Kareo">
                                                    <span
                                                        *ngIf="item.aprilColor=='unapproved_count' || item.aprilColorLink =='M';else aprilEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByKareo(item,selectedYear,4)"
                                                            (click)="getMissMatchedEncountersByKareo(item,selectedYear,4)">{{item.aapril}}</span>
                                                    </span>
                                                    <ng-template #aprilEls>
                                                        {{item.aapril}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">{{item.april}}</div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Kareo">
                                                    <span *ngIf="item.ecapril !='0';else aprilEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByGR(item,selectedYear,4)"
                                                            (click)="getMissMatchedEncountersByGR(item,selectedYear,4)">{{item.ecapril}}</span>
                                                    </span>
                                                    <ng-template #aprilEcEls>
                                                        {{item.ecapril}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="{{item.mayColor}}" title="In Kareo">
                                                    <span
                                                        *ngIf="item.mayColor=='unapproved_count' || item.mayColorLink =='M';else mayEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByKareo(item,selectedYear,5)"
                                                            (click)="getMissMatchedEncountersByKareo(item,selectedYear,5)">{{item.amay}}</span>
                                                    </span>
                                                    <ng-template #mayEls>
                                                        {{item.amay}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">{{item.may}}</div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Kareo">
                                                    <span *ngIf="item.ecmay !='0';else mayEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByGR(item,selectedYear,5)"
                                                            (click)="getMissMatchedEncountersByGR(item,selectedYear,5)">{{item.ecmay}}</span>
                                                    </span>
                                                    <ng-template #mayEcEls>
                                                        {{item.ecmay}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="{{item.juneColor}}" title="In Kareo">
                                                    <span
                                                        *ngIf="item.juneColor=='unapproved_count'|| item.juneColorLink =='M';else juneEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByKareo(item,selectedYear,6)"
                                                            (click)="getMissMatchedEncountersByKareo(item,selectedYear,6)">{{item.ajune}}</span>
                                                    </span>
                                                    <ng-template #juneEls>
                                                        {{item.ajune}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">{{item.june}}</div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Kareo">
                                                    <span *ngIf="item.ecjune !='0';else juneEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByGR(item,selectedYear,6)"
                                                            (click)="getMissMatchedEncountersByGR(item,selectedYear,6)">{{item.ecjune}}</span>
                                                    </span>
                                                    <ng-template #juneEcEls>
                                                        {{item.ecjune}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="{{item.julyColor}}" title="In Kareo">
                                                    <span
                                                        *ngIf="item.julyColor=='unapproved_count'|| item.julyColorLink =='M';else julyEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByKareo(item,selectedYear,7)"
                                                            (click)="getMissMatchedEncountersByKareo(item,selectedYear,7)">{{item.ajuly}}</span>
                                                    </span>
                                                    <ng-template #julyEls>
                                                        {{item.ajuly}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">{{item.july}}</div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Kareo">
                                                    <span *ngIf="item.ecjuly !='0';else julyEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByGR(item,selectedYear,7)"
                                                            (click)="getMissMatchedEncountersByGR(item,selectedYear,7)">{{item.ecjuly}}</span>
                                                    </span>
                                                    <ng-template #julyEcEls>
                                                        {{item.ecjuly}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="{{item.augColor}}" title="In Kareo">
                                                    <span
                                                        *ngIf="item.augColor=='unapproved_count'|| item.augColorLink =='M';else augEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByKareo(item,selectedYear,8)"
                                                            (click)="getMissMatchedEncountersByKareo(item,selectedYear,8)">{{item.aaug}}</span>
                                                    </span>
                                                    <ng-template #augEls>
                                                        {{item.aaug}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">{{item.aug}}</div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Kareo">
                                                    <span *ngIf="item.ecaug !='0';else augEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByGR(item,selectedYear,8)"
                                                            (click)="getMissMatchedEncountersByGR(item,selectedYear,8)">{{item.ecaug}}</span>
                                                    </span>
                                                    <ng-template #augEcEls>
                                                        {{item.ecaug}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="{{item.sepColor}}" title="In Kareo">
                                                    <span
                                                        *ngIf="item.sepColor=='unapproved_count' || item.sepColorLink =='M';else sepEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByKareo(item,selectedYear,9)"
                                                            (click)="getMissMatchedEncountersByKareo(item,selectedYear,9)">{{item.asep}}</span>
                                                    </span>
                                                    <ng-template #sepEls>
                                                        {{item.asep}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">{{item.sep}}</div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Kareo">
                                                    <span *ngIf="item.ecsep !='0';else sepEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByGR(item,selectedYear,9)"
                                                            (click)="getMissMatchedEncountersByGR(item,selectedYear,9)">{{item.ecsep}}</span>
                                                    </span>
                                                    <ng-template #sepEcEls>
                                                        {{item.ecsep}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="{{item.octColor}}" title="In Kareo">
                                                    <span
                                                        *ngIf="item.octColor=='unapproved_count' || item.octColorLink =='M';else octEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByKareo(item,selectedYear,10)"
                                                            (click)="getMissMatchedEncountersByKareo(item,selectedYear,10)">{{item.aoct}}</span>
                                                    </span>
                                                    <ng-template #octEls>
                                                        {{item.aoct}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">{{item.oct}}</div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Kareo">
                                                    <span *ngIf="item.ecoct !='0';else octEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByGR(item,selectedYear,10)"
                                                            (click)="getMissMatchedEncountersByGR(item,selectedYear,10)">{{item.ecoct}}</span>
                                                    </span>
                                                    <ng-template #octEcEls>
                                                        {{item.ecoct}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="{{item.novColor}}" title="In Kareo">
                                                    <span
                                                        *ngIf="item.novColor=='unapproved_count'|| item.novColorLink =='M';else novEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByKareo(item,selectedYear,11)"
                                                            (click)="getMissMatchedEncountersByKareo(item,selectedYear,11)">{{item.anov}}</span>
                                                    </span>
                                                    <ng-template #novEls>
                                                        {{item.anov}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">{{item.nov}}</div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Kareo">
                                                    <span *ngIf="item.ecnov !='0';else novEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByGR(item,selectedYear,11)"
                                                            (click)="getMissMatchedEncountersByGR(item,selectedYear,11)">{{item.ecnov}}</span>
                                                    </span>
                                                    <ng-template #novEcEls>
                                                        {{item.ecnov}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="{{item.decColor}}" title="In Kareo">
                                                    <span
                                                        *ngIf="item.decColor=='unapproved_count' || item.decColorLink =='M';else decEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByKareo(item,selectedYear,12)"
                                                            (click)="getMissMatchedEncountersByKareo(item,selectedYear,12)">{{item.adec}}</span>
                                                    </span>
                                                    <ng-template #decEls>
                                                        {{item.adec}}
                                                    </ng-template>
                                                </div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="In Grand Rounds">{{item.dec}}</div>
                                                <hr class="hr_table">
                                                <div class="billed_count" title="Extra count in Kareo">
                                                    <span *ngIf="item.ecdec !='0';else decEcEls">
                                                        <span class='under-line-click'
                                                            (keyup)="getMissMatchedEncountersByGR(item,selectedYear,12)"
                                                            (click)="getMissMatchedEncountersByGR(item,selectedYear,12)">{{item.ecdec}}</span>
                                                    </span>
                                                    <ng-template #decEcEls>
                                                        {{item.ecdec}}
                                                    </ng-template>
                                                </div>
                                            </td>
                                        </tr>
                                    </ng-container>
                                    <ng-template #ElCase>
                                        <tr>
                                            <td class="text-center" colspan="16">Unable to Access this Facility</td>
                                        </tr>
                                    </ng-template>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="16" class="m-0 p-0" style="background: white !important;">
                                            <pagination-controls previousLabel="" nextLabel=""
                                                (pageChange)="p = $event"></pagination-controls>
                                        </td>
                                    </tr>
                            </table>

                        </div>
                    </div>

                </div>

            </div>
        </div>
        <!-- Area Chart Ends -->
    </div>
</div>
<!-- Begin Page Content Ends -->


<!-- Mismatched Kareo Encounters popup starts -->
<div class="modal fade" id="mdlMissKEnvs" tabindex="-1" aria-labelledby="MissKEnvsLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg max-width-80">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="MissKEnvsLabel">Mismatched Kareo Encounters
                    (<b>{{listOfMissMatchedEncountersByKareo.length}}</b>)

                </h5>
                <i class="fa fa-times closePopup" data-dismiss="modal" (keyup)="modelClose()"
                    (click)="modelClose()"></i>
            </div>
            <div class="modal-body" [formGroup]="FilterForm">
                <div class="row mx-auto my-3">
                    <div class="col-12 col-md-12 px-md-1 py-1">
                        <table class="table table-striped table-bordered table-hover billed_encounters_grid">
                            <thead>
                                <tr class="text-center">
                                    <th>Kareo Encounter Id</th>
                                    <th>GR Encounter Id</th>
                                    <th>Account Number</th>
                                    <th>Patient Name</th>
                                    <th>Facility Name</th>
                                    <th>Physician Name</th>
                                    <th>Encounter Seen Date</th>
                                    <th>CPT Code</th>
                                    <th class="width-13per">Reason</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngFor="let item of listOfMissMatchedEncountersByKareo">
                                    <td class="p-2">{{item.kareoEncounterID}}</td>
                                    <td class="p-2">{{item.encounterId}}</td>
                                    <td class="p-2">{{item.accountNumber}}</td>
                                    <td class="p-2">{{item.patientName}}</td>
                                    <td class="p-2">{{item.facilityName}}</td>
                                    <td class="p-2">{{item.physicianName}}</td>
                                    <td class="p-2">{{item.encounterSeenDate | date: 'MM/dd/yyyy' }}</td>
                                    <td class="p-2">
                                        <ul class="ml-3">
                                            <li *ngFor="let cpt of item.listOfCpts">{{cpt}}</li>
                                        </ul>
                                    </td>

                                    <ng-container *ngIf="item.reasonCode =='';else ddlReasonEls">
                                        <td class="p-2 width-13per">
                                            <select id="ddlReason" class="form-control form-control-sm"
                                                (change)="reasonChange($event)"
                                                [attr.disabled]="userAccess.billerModuleAccess !='YES' ? true:null"
                                                [(ngModel)]="selectedResonArray[item.encounterId]"
                                                [ngModelOptions]="{standalone: true}">
                                                <option value='undefined'>---Select Reason---</option>
                                                <option value="Duplicate">Duplicate</option>
                                                <option value="Already Billed">Already Billed</option>
                                            </select>
                                            <div class="mt-2">
                                                <input type="number" min="0" maxlength="15" placeholder="Encounter No"
                                                    class="form-control form-control-sm"
                                                    *ngIf="selectedResonArray[item.encounterId] == 'Already Billed'"
                                                    [(ngModel)]="encounterIDbyUser"
                                                    [ngModelOptions]="{standalone: true}">
                                            </div>
                                        </td>
                                        <td class="p-2">
                                            <button class="btn btn-primary btn-block"
                                                [attr.disabled]="userAccess.billerModuleAccess !='YES' ? true:null"
                                                (click)="updateReasonCodeKareoEncounter(item)">Submit</button>
                                        </td>
                                    </ng-container>
                                    <ng-template #ddlReasonEls>
                                        <td class="p-2 width-13per">
                                            <span>{{item.reasonCode}}</span>
                                            <span *ngIf="item.reasonCode== 'Already Billed'"><br />
                                                <span class="text-primary">{{item.encounterIDEnteredByUser}}</span>
                                            </span>
                                        </td>
                                        <td class="p-2">
                                            <span>Already Submiited</span>
                                        </td>
                                    </ng-template>

                                </tr>
                            </tbody>
                        </table>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Mismatched Kareo Encounters popup ends -->

<!-- Mismatched Kareo Encounters popup starts -->
<div class="modal fade" id="mdlMissKEnvsByGR" tabindex="-1" aria-labelledby="MissKEnvsLabelGr" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg max-width-80">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="MissKEnvsLabelGr">Mismatched Kareo Encounters
                    (<b>{{listOfMissMatchedEncountersByGR.length}}</b>)

                </h5>
                <i class="fa fa-times closePopup" data-dismiss="modal"></i>
            </div>
            <div class="modal-body" [formGroup]="FilterForm">
                <div class="row mx-auto my-3">
                    <div class="col-12 col-md-12 px-md-1 py-1">
                        <table class="table table-striped table-bordered table-hover billed_encounters_grid">
                            <thead>
                                <tr class="text-center">
                                    <th>Kareo Encounter Id</th>
                                    <th>Kareo Patient Id</th>
                                    <th>Physician Name</th>
                                    <th>Patient Name</th>
                                    <th>Facility Name</th>
                                    <th>CPT Code</th>
                                    <th>Created Date</th>
                                    <th>Encounter Seen Date</th>
                                    <th>Patient Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngFor="let item of listOfMissMatchedEncountersByGR">
                                    <td class="p-2">{{item.kareoEncounterID}}</td>
                                    <td class="p-2">{{item.kareoPatientID}}</td>
                                    <td class="p-2">{{item.physicianName}}</td>
                                    <td class="p-2">{{item.patientName}}</td>
                                    <td class="p-2">{{item.facilityName}}</td>
                                    <td class="p-2">{{item.cptCode}}</td>
                                    <td class="p-2">{{item.admissionDate | date: 'MM/dd/yyyy'}}</td>
                                    <td class="p-2">{{item.encounterSeenDate | date: 'MM/dd/yyyy'}}</td>
                                    <td class="p-2">{{item.patientStatus}}</td>

                                </tr>
                            </tbody>
                        </table>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Mismatched Kareo Encounters popup ends -->