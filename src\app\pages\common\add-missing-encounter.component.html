<div class="modal fade" id="missingEn" tabindex="-1" maria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg">
        <div class="modal-content" [formGroup]="missingEncForm">
            <div class="modal-header py-2">
                <h5 class="modal-title" id="exampleModalCenterTitle">Missing Encounters</h5>
                <i class="fa fa-times closePopup" data-dismiss="modal"></i>
            </div>
            <div class="modal-body p-md-2 p-1">
                <div class="row mx-auto py-md-4 py-2">
                    <div class="col border">
                        <div class="table-responsive">
                            <table id="missingTable" class="table table-bordered">
                                <thead id="missingHear">
                                    <tr id="missingHearTr" class="text-center">
                                        <th>Patient Name</th>
                                        <th>Account No</th>
                                        <th>Facility</th>
                                        <th>Admission Date</th>
                                    </tr>
                                </thead>
                                <tbody id="missingBody" class="word-break-all">
                                    <tr id="missingBodyTr" class="text-center">
                                        <td id="missEnPatientName">{{PatientObject?.patient_Name}}</td>
                                        <td id="missEnPatientAccNo">{{PatientObject?.account_Number}}</td>
                                        <td id="missEnFacility">{{PatientObject?.facility_Name}}</td>
                                        <td id="missEnAdmDate">{{PatientObject?.admission_Date| date :'M/d/yyyy'}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="row mx-auto mb-3 border">
                            <div class="col p-3">
                                <div class="form-group my-auto">
                                    <label for="ddlPhysicianMiss" class="font-weight-bold my-auto text-dark float-left">Physicians:</label>
                                    <select id="ddlPhysicianMiss" formControlName="ddlPhysicianMiss"
                                        class="form-control custom-control bg-whitesmoke">
                                        <option value="">---Select Physician---</option>
                                        <option [value]="s.physicianEmaiId" *ngFor="let s of listOfPhysicians">
                                            {{s.physicianName}}
                                        </option>
                                    </select>
                                </div>
                                <div class="text-danger d-block">
                                    <div *ngIf="submitted && f['ddlPhysicianMiss'].errors">Physician is required</div>
                                </div>
                            </div>
                            <div class="col px-1">
                                <div class="form-group my-auto">
                                    <label for="envSeenDate" class="font-weight-bold my-auto text-dark float-left">Encounter Seen
                                        Date:</label>
                                    <mat-form-field class="float-left text-dark">
                                        <mtx-datetimepicker #datetimePicker7 [type]="type" [mode]="mode"
                                            [multiYearSelector]="multiYearSelector" [startView]="startView"
                                            [twelvehour]="twelvehour" [timeInterval]="timeInterval" [touchUi]="touchUi"
                                            [timeInput]="timeInput">
                                        </mtx-datetimepicker>
                                        <input [mtxDatetimepicker]="datetimePicker7" formControlName="envSeenDate"
                                            matInput formControlName="envSeenDate" class="form-control input-border float-left" [max]="maxDateTime"
                                            [ngClass]="{ 'is-invalid': submitted && f['envSeenDate'].errors}">
                                        <mtx-datetimepicker-toggle [for]="datetimePicker7"
                                            matSuffix></mtx-datetimepicker-toggle>
                                    </mat-form-field>
                                </div>
                                <div class="text-danger d-block">
                                    <div *ngIf="submitted && f['envSeenDate'].errors">Encounter Seen Date is required
                                    </div>
                                    <div *ngIf="vali2">Encounter Seen Date Can't Be Less Than Admission Date</div>
                                    <div *ngIf="vali3">Encounter Seen Date Can't Be a Future Date</div>
                                </div>
                            </div>
                        </div>

                        <div *ngIf="lisfOfMissingEncounters.length>0" class="table-responsive">
                            <table id="missingTable" class="table table-bordered">
                                <thead id="missingHear">
                                    <tr id="missingHearTr" class="text-center">
                                        <th>Patient Name</th>
                                        <th>Account No</th>
                                        <th>Facility</th>
                                        <th>Physician</th>
                                        <th>Missed Encounter Seen Date</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="missingBody" class=" word-break-all">
                                    <tr *ngFor="let item of lisfOfMissingEncounters" id="missingBodyTr" class="text-center">
                                        <td>{{item.patientName}}</td>
                                        <td>{{item.accountNumber}}</td>
                                        <td>{{item.facilityName}}</td>
                                        <td>{{item.physicianMailid}}</td>
                                        <td>{{item.encounterSeenDate}}</td>
                                        <td><a class="ml-3" (click)='openDeleteConfirmPopup(item)' title="Delete"><i
                                                    class="fas fa-trash text-danger"></i></a> </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
            <div class="modal-footer py-2">
                <button class="btn btn-outline-info float-right" (click)="insertMissedEvn(PatientObject)">Save</button>
            </div>
        </div>
    </div>
</div>