import { Component, OnInit } from '@angular/core';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { CommonService } from 'src/app/services/common/common.service';
import { AppComponent } from 'src/app/app.component';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { CoordinatorService } from 'src/app/services/coordinator/coordinator.service';
import { forkJoin } from 'rxjs';
import { ExcelServices } from 'src/app/services/common/ExcelService';
import { ToastrService } from 'ngx-toastr';

declare let $: any;

@Component({
  selector: 'app-coordinator',
  templateUrl: './coordinator.component.html',
  styleUrls: ['./coordinator.component.css']
})
export class CoordinatorComponent implements OnInit {
  public listOfFacilities: Array<any> = [];
  public listOfGroups: Array<any> = [];
  public listofDepartments: Array<any> = [];
  public FilterForm: FormGroup;
  public request: any = {};
  public grprequest: any = {};
  public listOfPatients: Array<any> = [];
  public listOfPhysicians: Array<any> = [];
  public totalCount: number;
  public p: number = 1;
  public searchByName: string = '';
  public patient: any = {};
  public submitted: boolean = false;
  public listOfUsersAndGroups: Array<any> = [];
  public lisfOfSentNotes: Array<any> = [];
  public listOfAttendingProviders: Array<any> = [];
  public listOfActionGroups: Array<any> = [];
  public selectedUsersAndGroups: Array<any> = [];
  public mDdlAttendingProviderSettings: any = {};
  public mDdlGroupSettings: any = {};
  public swapForm: FormGroup;
  public timeout: any = null;
  public nonPrimeCount: number = 0;
  public img1: string = '.././../../assets/img/excel.png';

  constructor(private readonly coordinatorServ: CoordinatorService, private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService
    , private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly excelService: ExcelServices, private readonly toastr: ToastrService) { }

  ngOnInit() {
    this.appComp.loadPageName('Coordinator Overview', 'coordinatorTab');

    this.mDdlAttendingProviderSettings = {
      singleSelection: false,
      idField: 'name',
      textField: 'name',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 1,
      allowSearchFilter: true,
      defaultOpen: false,
      closeDropDownOnSelection: true
    };
    this.mDdlGroupSettings = {
      singleSelection: false,
      idField: 'name',
      textField: 'name',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 1,
      allowSearchFilter: true,
      defaultOpen: false,
      closeDropDownOnSelection: true
    };

    this.FilterForm = this.fb.group({
      ddlFacility: ['All', Validators.required],
      ddlDepartment: ['All', Validators.required],
      ddlGroups: ['All', Validators.required],
      ddlPhysician: ['All', Validators.required],

    });
    this.swapForm = this.fb.group({
      ddlSwapPhysician: [null, Validators.required]
    });
    this.getFacilitiesByUserType();
  }

  get f() { return this.FilterForm.controls; }
  get f2() { return this.swapForm.controls; }

  getFacilitiesByUserType() {
    this.commonServ.getFacilitiesByUserType('COORDINATOR').subscribe((p: any) => {
      this.listOfFacilities = p;
      if (this.listOfFacilities.length > 0) {
        this.FilterForm.get('ddlFacility')?.setValue(this.listOfFacilities[0].facilityName);
        this.loadDataInFaciliytChangeEvent(1);
      }
    });
  }

  loadDdls() {
    this.grprequest.FacilityName = this.encrDecr.set('All');

    forkJoin(
      this.commonServ.getFacilitiesByUserType('COORDINATOR'),
      this.coordinatorServ.getDepartmentsGroupsPhysiciansByFacility(this.grprequest)
    ).subscribe((p: any) => {
      this.listOfFacilities = p[0];
      this.listofDepartments = p[1].departmentlist;
      this.listOfGroups = p[1].groupslist;
      this.listOfPhysicians = p[1].physicianlist;
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  loadDataInFaciliytChangeEvent(pageNo) {
    this.commonServ.startLoading();
    this.nonPrimeCount = 0;
    this.p = pageNo;
    this.FilterForm.get('ddlPhysician')?.setValue('All');
    this.FilterForm.get('ddlDepartment')?.setValue('All');
    this.FilterForm.get('ddlGroups')?.setValue('All');
    this.request.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.DepartmentName = this.encrDecr.set(this.FilterForm.value.ddlDepartment);
    this.request.PhysicianName = this.encrDecr.set(this.FilterForm.value.ddlPhysician);
    this.request.GroupName = this.encrDecr.set(this.FilterForm.value.ddlGroups);
    this.request.PageIndex = this.p;
    this.request.PageSize = 10;

    this.grprequest.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);

    forkJoin(
      this.coordinatorServ.getCoordinatorPatients(this.request),
      this.coordinatorServ.getDepartmentsGroupsPhysiciansByFacility(this.grprequest)
    ).subscribe((p: any) => {
      this.listOfPatients = p[0].listofPatients;
      let listpatients = [...p[0].listofPatients].filter(x => x.is_prime_hospital === false);
      this.nonPrimeCount = listpatients.length;
      this.totalCount = p[0].totalReCount;
      this.listofDepartments = p[1].departmentlist;
      this.listOfGroups = p[1].groupslist;
      this.listOfPhysicians = p[1].physicianlist;
      this.swapForm.reset();
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  getCoordinatorPatients(pageNo) {
    this.commonServ.startLoading();
    this.nonPrimeCount = 0;
    this.p = pageNo;
    this.request.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.DepartmentName = this.encrDecr.set(this.FilterForm.value.ddlDepartment);
    this.request.PhysicianName = this.encrDecr.set(this.FilterForm.value.ddlPhysician);
    this.request.GroupName = this.encrDecr.set(this.FilterForm.value.ddlGroups);
    this.request.PatientName = this.encrDecr.set(this.searchByName);
    this.request.PageIndex = this.p;
    this.request.PageSize = 10;
    this.coordinatorServ.getCoordinatorPatients(this.request).subscribe((p: any) => {
      this.listOfPatients = p.listofPatients;
      let listpatients = [...p.listofPatients].filter(x => x.is_prime_hospital === false);
      this.nonPrimeCount = listpatients.length;
      this.totalCount = p.totalReCount;
      this.swapForm.reset();
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  onKeySearchCoordinatorPatients() {
    clearTimeout(this.timeout);
    if (this.FilterForm.invalid) {
      return;
    }

    this.timeout = setTimeout(() => {
      this.getCoordinatorPatients(1);
    }, 2000);
  }

  saveCoordinatorNotes(pObj) {
    if (pObj.coordinator_notes.length < 1000) {
      this.commonServ.startLoading();
      this.request.admission_type = this.encrDecr.set('NOTES');
      this.request.account_number = this.encrDecr.set(pObj.account_number);
      this.request.patient_name = this.encrDecr.set(pObj.patient_name);
      this.request.facility_name = this.encrDecr.set(pObj.facility_name);
      this.request.coordinator_notes = this.encrDecr.set(pObj.coordinator_notes);
      this.coordinatorServ.saveNotesGroupsAttendingPhysician(this.request).subscribe((p: any) => {
        this.request = {};
        this.commonServ.stopLoading();
        this.toastr.success('Coordinator Notes Saved Successfully!', '', { timeOut: 2500 });
        $('#mdlCoordinatorNotes').modal('hide');
      },
        error => {
          this.request = {};
          this.commonServ.stopLoading();
          console.error(error.status);
        });
    }
    else {
      this.toastr.error('Max length is 1000 characters.', '', { timeOut: 2500 });
    }
  }

  saveAttendingProvider(pObj, type) {
    this.commonServ.startLoading();
    this.request.admission_type = this.encrDecr.set('Attending_Physician');
    this.request.account_number = this.encrDecr.set(pObj.account_number);
    this.request.patient_name = this.encrDecr.set(pObj.patient_name);
    this.request.facility_name = this.encrDecr.set(pObj.facility_name);
    this.request.coordinator_notes = this.encrDecr.set(pObj.coordinator_notes);
    if (type == 'onSelectAll') {
      this.request.ListofSelectedAttendingProviders = pObj.listofAttendingProviders;
    }
    else if (type == 'onDeSelectAll') {
      this.request.ListofSelectedAttendingProviders = [];
    }
    else {
      this.request.ListofSelectedAttendingProviders = pObj.listofSelectedAttendingProviders;
    }

    this.coordinatorServ.saveNotesGroupsAttendingPhysician(this.request).subscribe((p: any) => {
      this.request = {};
      this.commonServ.stopLoading();
      pObj.attending_physician_inapp = (pObj.listofSelectedAttendingProviders.length > 0 ? pObj.listofSelectedAttendingProviders : null);
      this.toastr.success('Attending Providers Saved Successfully!', '', { timeOut: 2500 });
      $('#mdlAttendingProvider').modal('hide');
      this.getCoordinatorPatients(this.p);
    },
      error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
  }

  saveGroups(pObj, type) {
    this.commonServ.startLoading();
    this.request.admission_type = this.encrDecr.set('GROUPS');
    this.request.account_number = this.encrDecr.set(pObj.account_number);
    this.request.patient_name = this.encrDecr.set(pObj.patient_name);
    this.request.facility_name = this.encrDecr.set(pObj.facility_name);
    if (type == 'onSelectAll') {
      this.request.ListofSelectedGroups = pObj.listofGroups;
    }
    else if (type == 'onDeSelectAll') {
      this.request.ListofSelectedGroups = [];
    }
    else {
      this.request.ListofSelectedGroups = pObj.listofSelectedGroups;
    }

    this.coordinatorServ.saveNotesGroupsAttendingPhysician(this.request).subscribe((p: any) => {
      this.request = {};
      this.commonServ.stopLoading();
      pObj.selectedgroups = (pObj.listofSelectedGroups.length > 0 ? pObj.listofSelectedGroups : null);
      this.toastr.success('Groups Saved Successfully!', '', { timeOut: 2500 });
      $('#mdlGroups').modal('hide');
      this.getCoordinatorPatients(this.p);
    },
      error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
  }

  openNotes(item) {
    this.patient = {
      patient_Name: item.patient_name??this.patient.account_Number,
      account_Number: item.account_number??this.patient.account_Number,
      facility_Name: item.facility_name??this.patient.facility_Name
    };
    this.selectedUsersAndGroups = [];
    this.commonServ.startLoading();
    this.request.ACCOUNT_NUMBER = this.encrDecr.set(this.patient.account_Number);
    this.request.FACILITYNAME = this.encrDecr.set(this.patient.facility_Name);
    forkJoin(
      this.commonServ.getUserGroups(this.patient.facility_Name),
      this.commonServ.getNotes(this.request)
    ).subscribe((p: any) => {
      this.listOfUsersAndGroups = p[0];
      this.lisfOfSentNotes = p[1];
      $('#mdlNotes').modal('show');
      this.request = {};
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.toastr.error('Something went wrong!!!');
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  exportAsXLSX(): void {
    let fileName = "";
    fileName = (this.FilterForm.value.ddlFacility == 'All' ? 'All Facilities' : this.FilterForm.value.ddlFacility) + '_' + (this.FilterForm.value.ddlDepartment == 'All' ? 'All Departments' : this.FilterForm.value.ddlDepartment)
      + '_' + (this.FilterForm.value.ddlGroups == 'All' ? 'All Groups' : this.FilterForm.value.ddlGroups) + '_' + (this.FilterForm.value.ddlPhysician == 'All' ? 'All Physicians' : this.FilterForm.value.ddlPhysician);

    this.commonServ.startLoading();
    this.request.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.DepartmentName = this.encrDecr.set(this.FilterForm.value.ddlDepartment);
    this.request.PhysicianName = this.encrDecr.set(this.FilterForm.value.ddlPhysician);
    this.request.GroupName = this.encrDecr.set(this.FilterForm.value.ddlGroups);
    this.request.PatientName = this.encrDecr.set(this.searchByName);
    this.coordinatorServ.getPatientForCoordinatorForDownload(this.request).subscribe((p: any) => {
      if (p) {
        this.excelService.exportAsExcelFile(p, fileName);
      }
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  openEditPatient(item) {
    this.commonServ.startLoading();
    this.request.account_number = this.encrDecr.set(item.account_number);
    this.request.facility_name = this.encrDecr.set(item.facility_name);
    this.commonServ.getPatientInfo(this.request).subscribe((p: any) => {
      this.request = {};
      this.patient = p;
      this.patient.admit_datetime = new Date(this.patient.admit_datetime);
      $('#editPatient').modal('show');
      this.commonServ.stopLoading();
    },
      error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
  }

  openSwapPhysician() {
    this.submitted = true;
    if (this.swapForm.invalid) {
      return;
    }
    this.commonServ.startLoading();
    $('#swapConfModel').modal('show');
    this.commonServ.stopLoading();
  }
  saveAssignments() {
    this.request.facility_name = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.commonServ.startLoading();
    this.coordinatorServ.saveCoordinatorAssignments(this.request).subscribe((p: any) => {
      this.request = {};
      this.commonServ.stopLoading();
      this.toastr.success('Assignments Saved Successfully!', '', { timeOut: 2000 });
    },
      error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
  }
  swapPhysician() {
    this.submitted = true;
    if (this.swapForm.invalid) {
      return;
    }
    $('#swapConfModel').modal('hide');
    this.commonServ.startLoading();
    this.request.facility_name = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.old_physician = this.encrDecr.set(this.FilterForm.value.ddlPhysician);
    this.request.new_attending_physicians = this.encrDecr.set(this.swapForm.value.ddlSwapPhysician);
    this.request.department_name = this.encrDecr.set(this.FilterForm.value.ddlDepartment);
    this.request.group_name = this.encrDecr.set(this.FilterForm.value.ddlGroups);
    this.request.search_key = this.encrDecr.set(this.searchByName);
    this.coordinatorServ.swapPhysician(this.request).subscribe((p: any) => {
      this.request = {};
      this.submitted = false;
      this.swapForm.reset();
      this.commonServ.stopLoading();
      this.getCoordinatorPatients(1);
      this.toastr.success('Physician Swapped Successfully!', '', { timeOut: 2500 });
    },
      error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
  }



}
