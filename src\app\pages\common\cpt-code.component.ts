import { Component, OnInit, Input, EventEmitter, Output } from '@angular/core';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
declare let $: any;

@Component({
  selector: 'app-cpt-code',
  templateUrl: './cpt-code.component.html',
  styleUrls: ['./cpt-code.component.css']
})
export class CptCodeComponent implements OnInit {

  @Input() lisfOfCPTData: Array<any> = [];
  @Input() cptType: string = "";
  @Input() encounterObj: any = {};
  public chkValuesArray: Array<any> = [];
  public editedValue: string;
  public searchCPTs: string;
  public request: any = {};
  public lisfOfSearchCPTData: Array<any> = [];
  public filterCPTs: string;
  public isSearch: boolean = false;
  public timeout: any = null;
  public haveDeleteReason: boolean = true;
  public submitted: boolean = false;
  public device: boolean = false;
  @Output() eventListOfRemovedCpts = new EventEmitter<Array<any>>();
  @Output() eventUpdateUpdatedCptDataList = new EventEmitter<Array<any>>();
  constructor(private readonly encrDecr: EncrDecrServiceService, private readonly commonServ: CommonService) { }

  ngOnInit() {
    // Detect mobile devices
    if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
      this.device = true;
    } else {
      this.device = false;
    }
    this.submitted = true;
    this.haveDeleteReason = true;
  }

  search() {
    this.lisfOfSearchCPTData = [];
    this.filterCPTs = "";
    this.isSearch = true;
  }

  fav() {
    this.isSearch = false;
    this.eventUpdateUpdatedCptDataList.emit(this.encounterObj);
  }

  searchCPTData(eObj) {
    clearTimeout(this.timeout);
    let existingArray: Array<any> = [];
    eObj.listOfCpts.forEach(a => {
      existingArray.push(a.split('(--')[0].trim());
    });
    this.timeout = setTimeout(() => {
      this.commonServ.startLoading();
      this.request.CPTNAME = this.encrDecr.set(this.filterCPTs);
      this.request.PHYSICIAN_MAIL_ID = this.encrDecr.set(eObj.physicianmailid);
      this.commonServ.searchCPTData(this.request).subscribe((p: any) => {
        this.lisfOfSearchCPTData = p;
        this.lisfOfSearchCPTData.forEach(x => {
          x.isRemoved = false;
          x.deletedReason = "";
          x.isExist = existingArray.includes(x.cptname);
          x.isOld = existingArray.includes(x.cptname);
          let ittem=this.findGroupedCptItem(eObj, x.cptname);
          x.cpt_count = ittem ? ittem?.cpt_count : 0;
        });
        this.request = {};
        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        console.error(error.status);
      });
    }, 2000);
  }

  findGroupedCptItem(eObj, cptname) {
    return eObj.groupedListOfCpts.find(a => a.cpt_name.split('(--')[0].trim() == cptname);
  }

  chkChangeEventAdd(val, event) {
    this.haveDeleteReason = true;
    for (let i = 0; i < this.lisfOfCPTData.length; i++) {
      if (this.lisfOfCPTData[i].cpT_ID == event.target.id && event.target.checked) {
        this.lisfOfCPTData[i].checked = event.target.checked;
        this.lisfOfCPTData[i].isExist = event.target.checked;
        this.lisfOfCPTData[i].haveDeleteReason = true;
        this.lisfOfCPTData[i].cpt_count = this.lisfOfCPTData[i].cpt_count + 1;
        this.lisfOfCPTData[i].isRemoved = false;
      }
      else if (this.lisfOfCPTData[i].cpT_ID == event.target.id && !event.target.checked && !this.lisfOfCPTData[i].isOld) {
        this.lisfOfCPTData[i].checked = event.target.checked;
        this.lisfOfCPTData[i].isExist = event.target.checked;
        this.lisfOfCPTData[i].haveDeleteReason = true;
        this.lisfOfCPTData[i].cpt_count = 0;
        this.lisfOfCPTData[i].isRemoved = false;
      }
      else if (this.lisfOfCPTData[i].cpT_ID == event.target.id && !event.target.checked && this.lisfOfCPTData[i].isOld) {
        this.lisfOfCPTData[i].checked = event.target.checked;
        this.lisfOfCPTData[i].isExist = event.target.checked;
        this.lisfOfCPTData[i].haveDeleteReason = false;
        this.haveDeleteReason = false;
        for (let j = 0; j < this.lisfOfCPTData[i].cpt_count; j++) {
          if (this.lisfOfCPTData[i].isOld) {
            this.encounterObj.listOfRemovedCpts.push(val);
          }
        }
        this.lisfOfCPTData[i].cpt_count = 0;
        this.lisfOfCPTData[i].isRemoved = true;
      }
    }
  }

  chkChangeEventUpdate(val) {
    this.haveDeleteReason = true;
    this.editedValue = val;
  }

  keyupReason(val) {
    if (val) {
      this.haveDeleteReason = true;
    }
    else {
      this.haveDeleteReason = false;
    }
  }

  chkChangeEventSearchAdd(val, event) {
    this.haveDeleteReason = true;
    for (let item of this.lisfOfSearchCPTData) {
      if ('sch-' + item.cpT_ID == event.target.id && event.target.checked) {
        item.checked = event.target.checked;
        item.isExist = event.target.checked;
        item.haveDeleteReason = true;
        item.isRemoved = false;
        item.cpt_count = item.cpt_count + 1;
      }
      else if ('sch-' + item.cpT_ID == event.target.id && !event.target.checked && !item.isOld) {
        item.checked = event.target.checked;
        item.isExist = event.target.checked;
        item.haveDeleteReason = true;
        item.cpt_count = 0;
      }
      else if ('sch-' + item.cpT_ID == event.target.id && !event.target.checked && item.isOld) {
        item.checked = event.target.checked;
        item.isExist = event.target.checked;
        item.haveDeleteReason = false;
        this.haveDeleteReason = false;
        item.isRemoved = true;
        item.cpt_count = 0;
        this.encounterObj.listOfRemovedCpts.push(val);
      }
    }
  }

  editCPTData(eObj, cptCode) {
    this.commonServ.startLoading();
    if (this.editedValue) {
      let cptEixt: any = eObj.groupedListOfCpts.find(x => x.cpt_name == cptCode);
      cptEixt.cpt_count = cptEixt.cpt_count - 1;
      const index: number = eObj.listOfCpts.indexOf(cptCode);
      if (index !== -1) {
        if (!eObj.listOfCpts.includes(this.editedValue)) {
          eObj.listOfCpts[index] = this.editedValue;
          eObj.listOfRemovedCpts.push(cptCode);
        }
        let cptItem: any = {
          cpt_name: this.editedValue,
          cpt_count: 1
        }
        eObj.groupedListOfCpts.push(cptItem);
      }
      $(".btnSave" + eObj.encounteR_ID).show();
    }
    this.eventListOfRemovedCpts.emit(eObj);
    this.commonServ.stopLoading();
  }

  editCPTDataSearch(eObj, cptCode) {
    this.commonServ.startLoading();
    const index: number = eObj.listOfCpts.indexOf(cptCode);
    if (index !== -1) {
      if (!eObj.listOfCpts.includes(this.editedValue)) {
        eObj.listOfCpts[index] = this.editedValue;
        eObj.listOfRemovedCpts.push(cptCode);
        $(".btnSave" + eObj.encounteR_ID).show();
      }
    }
    this.eventListOfRemovedCpts.emit(eObj);
    this.commonServ.stopLoading();
  }

  addCPTDataFvSr(eObj, list) {
    this.submitted = true;
    this.haveDeleteReason = true;

    // First check if all required delete reasons are provided
    let allReasonsProvided = true;
    list.forEach(x => {
      // If it's an old CPT that's being removed or decreased in count
      if (x.isOld && (x.cpt_count === 0 || (x.isRemoved && !x.deletedReason))) {
        // Check if a delete reason is provided
        if (!x.deletedReason || x.deletedReason.trim() === '') {
          allReasonsProvided = false;
          x.haveDeleteReason = false;
          this.haveDeleteReason = false;
        }
      }
    });

    // If not all reasons are provided, don't proceed
    if (!allReasonsProvided) {
      return;
    }

    
    this.commonServ.startLoading();
    list.forEach(x => {
      if (x.checked || x.isExist) {
        this.updateCptList(x, eObj);
      }
      else if(!x.checked && x.isOld){
        this.updateCptList(x, eObj);
        this.updateDeleteReson(x, eObj);
      }
    });
    this.eventListOfRemovedCpts.emit(eObj);
    this.commonServ.stopLoading();

    
    if (this.haveDeleteReason) {
      $("#CPTData").modal('hide');
    }
  }

  updateCptList(x, eObj) {
    // Check if a delete reason is required but not provided
    const index: number = eObj.listOfRemovedCpts.indexOf(x.cptname);
    if (x.isOld && index !== -1 && x.cptname !== "" && (!x.deletedReason || x.deletedReason.trim() === "")) {
      this.haveDeleteReason = false;
      x.haveDeleteReason = false;
      return; // Don't proceed with the update if a reason is required but not provided
    }

    let listCount: number = eObj.listOfCpts.length;
    for (let j = listCount; j > 0; --j) {
      const cptIndex: number = eObj.listOfCpts.indexOf(x.cptname);
      if (cptIndex !== -1) {
        eObj.listOfCpts.splice(cptIndex, 1);
      }
    }

    
    for (let i = x.cpt_count; i > 0; --i) {
      eObj.listOfCpts.push(x.cptname);
    }

    
    let cptItem: any = {
      cpt_name: x.cptname,
      cpt_count: x.cpt_count
    }
    let groupObj:any=eObj.groupedListOfCpts.find(y => y.cpt_name == x.cptname);
    if (groupObj) {
      groupObj.cpt_count = x.cpt_count;
    }
    else {
      eObj.groupedListOfCpts.push(cptItem);
    }

    // If a valid reason is provided, update the removed CPT list
    if (x.isOld && index !== -1 && x.cptname !== "" && x.deletedReason && x.deletedReason.trim() !== "") {
      x.haveDeleteReason = true;
      this.updateDeleteReson(x, eObj);
    }

    
    $(".btnSave" + eObj.encounteR_ID).show();
  }

  updateDeleteReson(x, eObj) {
    this.encounterObj.listOfRemovedCpts.forEach((r, i) => {
      this.updateRemvoedList(x, r, i, eObj);
    });
  }

  updateRemvoedList(x, r, i, eObj) {
    if (r == x.cptname && x.deletedReason != "") {
      eObj.listOfRemovedCpts[i] = x.cptname + "||" + x.deletedReason;
    }
  }


  favUnfavCPTCodesAdd(peid, status, item) {
    let confirmMessage = 'Do you want to ' + (status == 0 ? 'unfavorite' : 'favorite') + ' this CPT Code?';
    if (confirm(confirmMessage)) {
      this.commonServ.startLoading();
      this.request.PHYSICIANMAILID = this.encrDecr.set(peid);
      this.request.STATUS = status;
      this.request.CPT_ID = item.cpT_ID;
      this.commonServ.insertCPTData(this.request).subscribe((p: any) => {
        this.request = {};
        if (p > 0) {
          item.status = status;
        }
        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
    }
  }

  addCPTCode(eObj, cptCode) {
    this.commonServ.startLoading();
    this.lisfOfCPTData.push(cptCode.cptname);
    cptCode.cpt_count = cptCode.cpt_count + 1;
    cptCode.checked = true;
    this.commonServ.stopLoading();
  }

  deleteCPTCode(eObj, cptCode) {
    this.commonServ.startLoading();

    
    const originalCount = cptCode.cpt_count;

    
    let gItem:any=eObj.groupedListOfCpts.find(x=>x.cpt_name==cptCode.cptname);

    // If it's an old CPT and we're decreasing the count
    if (cptCode.isOld && cptCode.cpt_count <= gItem.cpt_count) {
      // Mark the CPT as needing a delete reason
      eObj.listOfRemovedCpts.push(cptCode.cptname);
      cptCode.isRemoved = true;
      cptCode.haveDeleteReason = false; // Set to false to require a reason
      this.haveDeleteReason = false; // Set to false to prevent saving without a reason
    }

    
    cptCode.cpt_count = cptCode.cpt_count - 1;

    
    if (cptCode.cpt_count == 0) {
      cptCode.isExist = false;
      cptCode.checked = true;
    }
    else {
      cptCode.checked = true;
      cptCode.isExist = true;
    }
    this.commonServ.stopLoading();
  }

}
