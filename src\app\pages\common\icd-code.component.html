<div class="modal fade" id="ICDData" tabindex="-1" aria-labelledby="exampleModalCenterTitle" maria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" [ngClass]="{'mobile-icd-modal': device}">
        <!-- Added mobile-specific class -->
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title" id="exampleModalCenterTitle">{{icdType}} ICD Codes</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- search start -->

                <div class="row">
                    <div class="col-12">
                        <!-- tabs start -->
                        <nav>
                            <div class="nav nav-tabs border-bottom-0" id="nav-tabpop" role="tablist">
                                <a class="nav-item nav-link pop-nav px-5" id="search-tab1" data-toggle="tab"
                                    href="#nav-search1" aria-controls="nav-profile" (click)="search()">Search</a>
                                <a class="nav-item nav-link pop-nav active px-5" id="favorite-tab1" data-toggle="tab"
                                    href="#nav-fav1" role="tab" role="tab" aria-controls="nav-home"
                                    (click)="fav()">Favorites</a>
                            </div>
                        </nav>
                        <div class="card px-3 py-2 shadow tab-content" id="nav-tabContent1">

                            <div class="tab-pane fade show active" id="nav-fav1" role="tabpanel"
                                aria-labelledby="favorite-tab1">
                                <div class="row scrolling" style="height:300px;overflow-x:scroll;">
                                    <div class="col-12">
                                        <div class="input-group">
                                            <input type="text" class="form-control small" maxlength="1000"
                                                placeholder="Search Here.." aria-label="Search" [(ngModel)]="searchICDs"
                                                [ngModelOptions]="{standalone: true}" aria-describedby="basic-addon2">
                                            <div class="input-group-append">
                                                <button class="bg-gradient-info btn text-white" type="button">
                                                    <i class="fas fa-search fa-sm"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12 my-2"
                                        *ngFor="let item of lisfOfICDData |gridFilter:{icdname:searchICDs}:false">
                                        <ng-container *ngIf="item.status">
                                            <div class="custom-control custom-checkbox" *ngIf="icdType=='Add'">
                                                <input *ngIf="item.isExist" checked type="checkbox"
                                                    class="custom-control-input" id="{{item.icD_ID}}"
                                                    value="{{item.icdname}}" (change)="chkChangeEventAdd($event)">
                                                <input *ngIf="!item.isExist" type="checkbox"
                                                    class="custom-control-input" id="{{item.icD_ID}}"
                                                    value="{{item.icdname}}" (change)="chkChangeEventAdd($event)">
                                                <label class="custom-control-label"
                                                    for="{{item.icD_ID}}">{{item.icdname}}</label>
                                                <span class="custom-control-inline"><a
                                                        style="font-size:20px;color:green;"
                                                        (click)="favUnfavICDCodesAdd(encounterObj.physicianmailid,false,item)">★</a></span>
                                            </div>
                                            <div *ngIf="icdType!='Add'" class="radio-color-div">
                                                <input *ngIf="icdType.includes(item.icdname)" checked type="radio"
                                                    name="icdCode" id="{{item.icD_ID}}" value="{{item.icdname}}"
                                                    (change)="chkChangeEventUpdate(item.icdname)">
                                                <input *ngIf="!icdType.includes(item.icdname)" type="radio"
                                                    name="icdCode" id="{{item.icD_ID}}" value="{{item.icdname}}"
                                                    (change)="chkChangeEventUpdate(item.icdname)">
                                                {{item.icdname}}
                                                <a style="font-size:20px;color:green;"
                                                    (click)="favUnfavICDCodesAdd(encounterObj.physicianmailid,false,item)">★</a>
                                            </div>
                                        </ng-container>
                                    </div>

                                </div>
                                <div class="modal-footer py-2">
                                    <button *ngIf="icdType=='Add'" class="btn btn-outline-info float-right"
                                        data-dismiss="modal" (click)="addICDData(encounterObj)">Save</button>
                                    <button *ngIf="icdType!='Add'" class="btn btn-outline-info float-right"
                                        data-dismiss="modal" (click)="editICDData(encounterObj,icdType)">Save</button>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="nav-search1" role="tabpanel" aria-labelledby="search-tab1">
                                <div class="row scrolling" style="height:300px;overflow-x:scroll;">
                                    <div class="col-12">
                                        <div class="input-group">
                                            <input type="text" class="form-control small" maxlength="1000"
                                                placeholder="Search Here.." aria-label="Search" [(ngModel)]="filterICDs"
                                                [ngModelOptions]="{standalone: true}" aria-describedby="basic-addon2"
                                                (keyup)="searchICDData(encounterObj)">
                                            <div class="input-group-append">
                                                <button class="bg-gradient-info btn text-white" type="button">
                                                    <i class="fas fa-search fa-sm"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12 my-2" *ngFor="let item of lisfOfSearchICDsData">
                                        <div class="custom-control custom-checkbox" *ngIf="icdType=='Add'">
                                            <input *ngIf="item.isExist" checked type="checkbox"
                                                class="custom-control-input" id="sch-{{item.icD_ID}}"
                                                value="{{item.icdname}}" (change)="chkChangeEventSearchAdd($event)">
                                            <input *ngIf="!item.isExist" type="checkbox" class="custom-control-input"
                                                id="sch-{{item.icD_ID}}" value="{{item.icdname}}"
                                                (change)="chkChangeEventSearchAdd($event)">
                                            <label class="custom-control-label"
                                                for="sch-{{item.icD_ID}}">{{item.icdname}}</label>
                                            <span class="custom-control-inline">
                                                <a *ngIf="item.status" style="font-size:20px;color:green;"
                                                    (click)="favUnfavICDCodesAdd(encounterObj.physicianmailid,false,item)">★</a>
                                                <a *ngIf="!item.status" style="font-size:20px;color:#777676;"
                                                    (click)="favUnfavICDCodesAdd(encounterObj.physicianmailid,true,item)">★</a>
                                            </span>
                                        </div>
                                        <div *ngIf="icdType!='Add'" class="radio-color-div">
                                            <input *ngIf="icdType.includes(item.icdname)" checked type="radio"
                                                name="icdCode" id="sch-{{item.icD_ID}}" value="{{item.icdname}}"
                                                (change)="chkChangeEventUpdate(item.icdname)">
                                            <input *ngIf="!icdType.includes(item.icdname)" type="radio" name="icdCode"
                                                id="sch-{{item.icD_ID}}" value="{{item.icdname}}"
                                                (change)="chkChangeEventUpdate(item.icdname)">
                                            {{item.icdname}}
                                            <a *ngIf="item.status" style="font-size:20px;color:green;"
                                                (click)="favUnfavICDCodesAdd(encounterObj.physicianmailid,false,item)">★</a>
                                            <a *ngIf="!item.status" style="font-size:20px;color:#777676;"
                                                (click)="favUnfavICDCodesAdd(encounterObj.physicianmailid,true,item)">★</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer py-2">

                                    <button *ngIf="icdType=='Add'" class="btn btn-outline-info float-right"
                                        data-dismiss="modal" (click)="addICDDataSearch(encounterObj)">Save</button>
                                    <button *ngIf="icdType!='Add'" class="btn btn-outline-info float-right"
                                        data-dismiss="modal"
                                        (click)="editICDDataSearch(encounterObj,icdType)">Save</button>
                                </div>
                            </div>

                        </div>
                        <!-- tabs end -->

                    </div>
                </div>

                <!-- search end -->
            </div>

        </div>
    </div>
</div>