<div class="row">

    <!-- Area Chart -->
    <div class="col px-0">
        <div class="card shadow border-blue">
            <!-- Card Header - Dropdown -->
            <!-- Card Body -->
            <div class="card-body p-3">

                <div class="row mx-auto my-3" [formGroup]="FilterForm">
                    <div class="col-12 col-md-5 pr-md-1 py-1">

                        <div class="form-group my-auto">
                            <select id="ddlFacility" class="form-control" formControlName="ddlFacility"
                                [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}"
                                aria-describedby="facilityHelpInline">
                                <option value="">---Select Facility---</option>
                                <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                                    <span>{{s.facilityName}}</span>
                                </option>
                            </select>

                        </div>
                        <small *ngIf="submitted && f['ddlFacility'].errors" id="facilityHelpInline"
                            class="text-danger">
                            <div *ngIf="f['ddlFacility'].errors['required']">Facility is required</div>
                        </small>

                    </div>
                    <div class="col-12 col-md-5 px-md-1 py-1">

                        <div class="form-group my-auto">
                            <input type="text" id="txtAcNo" class="form-control" maxlength="50"
                                formControlName="txtAcNo" aria-describedby="passwordHelpInline"
                                placeholder="Patient Acccount No"
                                [ngClass]="{ 'is-invalid': submitted && f['txtAcNo'].errors}" />

                        </div>
                        <small id="passwordHelpInline" *ngIf="submitted && f['txtAcNo'].errors" class="text-danger">
                            <div *ngIf="f['txtAcNo'].errors['required']">Acccount is required</div>
                            <div *ngIf="f['txtAcNo'].errors['maxlength']">Max length is 50 characters.</div>
                        </small>

                    </div>
                    <div class="col-12 col-md-2 pl-md-1 py-1">
                        <button class="btn btn-outline-info btn-block" type="submit" (click)="getPatientDetails()">
                            Search
                        </button>
                    </div>
                </div>

            </div>

        </div>
    </div>
    <!-- Area Chart Ends -->
</div>