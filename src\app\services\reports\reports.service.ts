import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { apiUrl } from 'src/app/config';
import { EncrDecrServiceService } from '../common/encr-decr-service.service';

@Injectable({
  providedIn: 'root'
})
export class ReportsService {

  constructor(private readonly http: HttpClient, private readonly encrDecr: EncrDecrServiceService) { }

  getEncounterReportData(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/GetEncounterReportData', request, { headers: headers });
  }

  getCPTReportData(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/GetCPTReportData', request, { headers: headers });
  }

  getMissingEncounterReportData(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/GetMissingEncounterReportData', request, { headers: headers });
  }

  getLastCPTDataRefreshDate() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/GetLastCPTDataRefreshDate', null, { headers: headers });
  }

  getMonitorAlerts_Report(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/GetMonitorAlerts_Report?facilityName=' + request, { headers: headers });
  }

  get_CPT_Summary_Between_Year(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/Get_CPT_Summary_Between_Year', request, { headers: headers });
  }

  get_CPT_Summary_Chart(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/Get_CPT_Summary_Chart', request, { headers: headers });
  }

  analtics_GET_ENCOUNTERS_DATA_BY_Facility(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/Analtics_GET_ENCOUNTERS_DATA_BY_Facility', request, { headers: headers });
  }

  getCPTUsedCountByFacility_Chart(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/GetCPTUsedCountByFacility_Chart', request, { headers: headers });
  }

  get_TOP_ENCOUNTERS_CPT_USED(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/GET_TOP_ENCOUNTERS_CPT_USED', request, { headers: headers });
  }

  getKareoEncounterStatusReport(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/GetKareoEncounterStatusReport', request, { headers: headers });
  }

  getNotesReport(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/GetNotesReport', request, { headers: headers });
  }

  getMissMatchedEncountersByKareo(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/GetMissMatchedEncountersByKareo', request, { headers: headers });
  }

  updateReasonCodeKareoEncounter(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/UpdateReasonCodeKareoEncounter', request, { headers: headers });
  }

  getMissMatchedEncountersByGR(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/GetMissMatchedEncountersByGR', request, { headers: headers });
  }

  getEncounterStatusReportDetails(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/GetEncounterStatusReportDetails', request, { headers: headers });
  }

  getGroupWiseFacilities() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/GetGroupWiseFacilities', null, { headers: headers });
  }

  getGroupNameByFacility(facillity) {
    const body = { FacilityName: this.encrDecr.set(facillity) };
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/GetGroupNameByFacility', body, { headers: headers });
  }

  getPhysiciansByGroup(g_name) {
    const body = { group_name: this.encrDecr.set(g_name) };
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/GetPhysiciansByGroup', body, { headers: headers });
  }
  getEncounterAndDeletedEncounterReportData(request, url: string) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    if (url == '/report/encounter-report') {
      return this.http.post(apiUrl + 'api/Reports/GetEncounterReportData_V1', request, { headers: headers });
    } else if (url == '/report/deleted-encounter-report') {
      return this.http.post(apiUrl + 'api/Reports/GetDeletedEncounterReportData', request, { headers: headers });
    } else {
      return this.http.post(apiUrl + 'api/Reports/GetEncounterReportData_V1', request, { headers: headers });
    }
  }
  getMissMatchedEncountersReportData(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/GetAssignmentMismatch', request, { headers: headers });
  }
  getMissMatchedEncountersReportDetails(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/GetAssignmentMismatchDetails', request, { headers: headers });
  }
  getCreatedEncountersReportDetails(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/GetAssignmentEncounterCreatedDetails', request, { headers: headers });
  }
  getNextGenStatusReportData(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/GetNextGenStatusReportData', request, { headers: headers });
  }

  getMissMatchedEncountersByNextgen(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Reports/GetMissMatchedEncountersByNextGen', request, { headers: headers });
  }

}
