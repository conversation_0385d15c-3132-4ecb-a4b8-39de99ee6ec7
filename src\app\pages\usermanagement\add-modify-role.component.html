<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">

            <div class="card shadow border-blue">
                <div class="card-header bg-blue p-2">
                    <div class="row mx-auto align-items-center">
                        <div class="col-12 col-md-4 pl-2 pr-1">
                            <button class="btn btn-secondary btn-block w-auto"
                                [routerLink]="['/usermanagement/add-modify-user']"
                                [state]="{filterObj:filterObj}">Back</button>
                        </div>
                        <div class="col-12 col-md-8 pl-1 pr-2">
                            <div class="form-group my-auto text-white h6 font-weight-bold" *ngIf="userDetails">
                                {{userDetails.display_name}}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body overflow-auto">
                    <div [formGroup]="roleForm" class="border border-blue mb-3 container p-2 rounded-lg">
                        <div class="row mx-auto align-items-center justify-content-center">
                            <div class="form-group col-12">
                                <label class="my-auto" for="ddlRole">Role:</label>
                                <select class="form-control font-weight-bold" id="ddlRole" formControlName="ddlRole"
                                    [(ngModel)]="selectedRole" (change)="roleChange($any($event.target).value)">
                                    <option value="">Select Role</option>
                                    <option *ngIf="userDetails.physicianModuleAccess=='YES'" value="PHYSICIAN">Physician
                                    </option>
                                    <option *ngIf="userDetails.coordinatorModuleAccess=='YES'" value="COORDINATOR">
                                        Coordinator</option>
                                    <option *ngIf="userDetails.auditorModuleAccess=='YES'" value="AUDITOR">Auditor
                                    </option>
                                    <option *ngIf="userDetails.billerModuleAccess=='YES'" value="BILLER">Biller</option>
                                    <option *ngIf="userDetails.userManagementAccess=='YES'" value="USERMANAGEMENT">User
                                        Management</option>
                                    <option *ngIf="userDetails.residentModuleAccess=='YES'" value="RESIDENT">Resident
                                    </option>
                                </select>
                                <div *ngIf="submitted && f['ddlRole'].invalid" class="alert alert-danger">
                                    <div *ngIf="f['ddlRole'].errors&&f['ddlRole'].errors['required']">Role Selection is
                                        required.</div>
                                </div>
                            </div>
                        </div>
                        <div class="row mx-auto align-items-center justify-content-center">
                            <div class="form-group col-12" *ngIf="selectedRole">
                                <label class="my-auto" for="ddlFacility">Facility:</label>
                                <ng-multiselect-dropdown id="ddlFacility" [placeholder]="'--Select Facility--'"
                                    [settings]="mDdlFacilitySettings" [data]="lstOfFacilities"
                                    [(ngModel)]="selectedLstOfFacilities" formControlName="ddlFacility"
                                    (onSelect)="onFacilitySelectOrDeSelect($event,'Select')"
                                    (onDeSelect)="onFacilitySelectOrDeSelect($event,'DeSelect')">
                                </ng-multiselect-dropdown>
                                <div *ngIf="submitted && f['ddlFacility'].invalid" class="alert alert-danger">
                                    <div *ngIf="f['ddlFacility'].errors&&f['ddlFacility'].errors['required']">Facility
                                        Selection is required.</div>
                                </div>
                            </div>
                        </div>
                        <div class="row mx-auto align-items-center justify-content-center">
                            <div class="form-group col-12" *ngIf="selectedRole && selectedRole!='USERMANAGEMENT'">
                                <label class="my-auto" for="ddlGroup">Group:</label>
                                <ng-multiselect-dropdown id="ddlGroup" [placeholder]="'--Select Group--'"
                                    [settings]="mDdlGroupsSettings" [data]="lstOfGroups"
                                    [(ngModel)]="selectedLstOfGroups" formControlName="ddlGroup"
                                    (onSelect)="onGroupSelectOrDeSelect($event,'Select')"
                                    (onDeSelect)="onGroupSelectOrDeSelect($event,'DeSelect')">
                                </ng-multiselect-dropdown>
                                <div *ngIf="submitted && f['ddlGroup'].invalid" class="alert alert-danger">
                                    <div *ngIf="f['ddlGroup'].errors&&f['ddlGroup'].errors['required']">Group Selection
                                        is required.</div>
                                </div>
                                <div *ngIf="groupValidationMessage" class="alert alert-danger">
                                    {{groupValidationMessage}}
                                </div>
                            </div>
                        </div>
                        <div class="row mx-auto align-items-center justify-content-center">
                            <div class="form-group col-12"
                                *ngIf="selectedRole && selectedRole=='PHYSICIAN' && isKareoPhysicianGroup">
                                <label class="my-auto" for="txtKareoPhysicianName">Kareo Physician:</label>
                                <input type="text" class="form-control" id="txtKareoPhysicianName"
                                    formControlName="txtKareoPhysicianName" [(ngModel)]="kareoPhysicianName" />
                            </div>
                        </div>
                        <div class="row mx-auto align-items-center justify-content-end text-right my-2">
                            <div class="col-12">
                                <button class="btn btn-sm btn-outline-secondary mr-2"
                                    (click)="cancelEvent()">Clear</button>
                                <button class="btn btn-sm btn-outline-info" (click)="updateEvent()">Update</button>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <h6 class="h6">Assigned Role(s):</h6>
                        <div class="d-table-cell d-grid border p-1" *ngIf="userDetails.physicianModuleAccess=='YES'">
                            <span class="p-1 my-auto"><b>Physician Facilities</b></span>
                            <div *ngFor="let flt of userDetails.physicianModuleFacilityList"
                                class="alert alert-light text-white bg-secondary alert-dismissible fade alert-content show">
                                <strong>{{flt}}</strong>
                                <button *ngIf="getUserAccesPermission(flt,userDetails.accessFacilityList)"
                                    class="btn close"
                                    (click)="deassignRoleFacilityOrGroup(userDetails.email_id,'PHYSICIAN','FACILITY',flt,'')">
                                    <span aria-hidden="true">×</span>
                                </button>
                            </div>
                        </div>
                        <div class="d-table-cell d-grid border p-1" *ngIf="userDetails.physicianModuleAccess=='YES'">
                            <span class="p-1 my-auto"><b>Physician Groups</b></span>
                            <div *ngFor="let grp of userDetails.physicianModuleFacilityGroupList"
                                class="alert alert-light text-white bg-secondary alert-dismissible fade alert-content show">
                                <strong>{{grp.groupName}}</strong>
                                <button *ngIf="getUserAccesPermission(grp.facilityName,userDetails.accessFacilityList)"
                                    class="btn close"
                                    (click)="deassignRoleFacilityOrGroup(userDetails.email_id,'PHYSICIAN','GROUP','',grp)">
                                    <span aria-hidden="true">×</span>
                                </button>
                            </div>
                        </div>
                        <div class="d-table-cell d-grid border p-1" *ngIf="userDetails.coordinatorModuleAccess=='YES'">
                            <span class="p-1 my-auto"><b>Coordinator Facilities</b></span>
                            <div *ngFor="let flt of userDetails.coordinatorModuleFacilityList"
                                class="alert alert-light text-white bg-secondary alert-dismissible fade alert-content show">
                                <strong>{{flt}}</strong>
                                <button *ngIf="getUserAccesPermission(flt,userDetails.accessFacilityList)"
                                    class="btn close"
                                    (click)="deassignRoleFacilityOrGroup(userDetails.email_id,'COORDINATOR','FACILITY',flt,'')">
                                    <span aria-hidden="true">×</span>
                                </button>
                            </div>
                        </div>
                        <div class="d-table-cell d-grid border p-1" *ngIf="userDetails.coordinatorModuleAccess=='YES'">
                            <span class="p-1 my-auto"><b>Coordinator Groups</b></span>
                            <div *ngFor="let grp of userDetails.coordinatorModuleFacilityGroupList"
                                class="alert alert-light text-white bg-secondary alert-dismissible fade alert-content show">
                                <strong>{{grp.groupName}}</strong>
                                <button *ngIf="getUserAccesPermission(grp.facilityName,userDetails.accessFacilityList)"
                                    class="btn close"
                                    (click)="deassignRoleFacilityOrGroup(userDetails.email_id,'COORDINATOR','GROUP','',grp)">
                                    <span aria-hidden="true">×</span>
                                </button>
                            </div>
                        </div>
                        <div class="d-table-cell d-grid border p-1" *ngIf="userDetails.auditorModuleAccess=='YES'">
                            <span class="p-1 my-auto"><b>Auditor Facilities</b></span>
                            <div *ngFor="let flt of userDetails.auditorModuleFacilityList"
                                class="alert alert-light text-white bg-secondary alert-dismissible fade alert-content show">
                                <strong>{{flt}}</strong>
                                <button *ngIf="getUserAccesPermission(flt,userDetails.accessFacilityList)"
                                    class="btn close"
                                    (click)="deassignRoleFacilityOrGroup(userDetails.email_id,'AUDITOR','FACILITY',flt,'')">
                                    <span aria-hidden="true">×</span>
                                </button>
                            </div>
                        </div>
                        <div class="d-table-cell d-grid border p-1" *ngIf="userDetails.auditorModuleAccess=='YES'">
                            <span class="p-1 my-auto"><b>Auditor Groups</b></span>
                            <div *ngFor="let grp of userDetails.auditorModuleFacilityGroupList"
                                class="alert alert-light text-white bg-secondary alert-dismissible fade alert-content show">
                                <strong>{{grp.groupName}}</strong>
                                <button *ngIf="getUserAccesPermission(grp.facilityName,userDetails.accessFacilityList)"
                                    class="btn close"
                                    (click)="deassignRoleFacilityOrGroup(userDetails.email_id,'AUDITOR','GROUP','',grp)">
                                    <span aria-hidden="true">×</span>
                                </button>
                            </div>
                        </div>
                        <div class="d-table-cell d-grid border p-1" *ngIf="userDetails.billerModuleAccess=='YES'">
                            <span class="p-1 my-auto"><b>Biller Facilities</b></span>
                            <div *ngFor="let flt of userDetails.billerModuleFacilityList"
                                class="alert alert-light text-white bg-secondary alert-dismissible fade alert-content show">
                                <strong>{{flt}}</strong>
                                <button *ngIf="getUserAccesPermission(flt,userDetails.accessFacilityList)"
                                    class="btn close"
                                    (click)="deassignRoleFacilityOrGroup(userDetails.email_id,'BILLER','FACILITY',flt,'')">
                                    <span aria-hidden="true">×</span>
                                </button>
                            </div>
                        </div>
                        <div class="d-table-cell d-grid border p-1" *ngIf="userDetails.billerModuleAccess=='YES'">
                            <span class="p-1 my-auto"><b>Biller Groups</b></span>
                            <div *ngFor="let grp of userDetails.billerModuleFacilityGroupList"
                                class="alert alert-light text-white bg-secondary alert-dismissible fade alert-content show">
                                <strong>{{grp.groupName}}</strong>
                                <button *ngIf="getUserAccesPermission(grp.facilityName,userDetails.accessFacilityList)"
                                    class="btn close"
                                    (click)="deassignRoleFacilityOrGroup(userDetails.email_id,'BILLER','GROUP','',grp)">
                                    <span aria-hidden="true">×</span>
                                </button>
                            </div>
                        </div>
                        <div class="d-table-cell d-grid border p-1" *ngIf="userDetails.userManagementAccess=='YES'">
                            <span class="p-1 my-auto"><b>User Management Facilities</b></span>
                            <div *ngFor="let flt of userDetails.userManagementFacilityList"
                                class="alert alert-light text-white bg-secondary alert-dismissible fade alert-content show">
                                <strong>{{flt}}</strong>
                                <button *ngIf="getUserAccesPermission(flt,userDetails.accessFacilityList)"
                                    class="btn close"
                                    (click)="deassignRoleFacilityOrGroup(userDetails.email_id,'USERMANAGEMENT','FACILITY',flt,'')">
                                    <span aria-hidden="true">×</span>
                                </button>
                            </div>
                        </div>
                        <div class="d-table-cell d-grid border p-1" *ngIf="userDetails.residentModuleAccess=='YES'">
                            <span class="p-1 my-auto"><b>Resident Facilities</b></span>
                            <div *ngFor="let flt of userDetails.residentModuleFacilityList"
                                class="alert alert-light text-white bg-secondary alert-dismissible fade alert-content show">
                                <strong>{{flt}}</strong>
                                <button *ngIf="getUserAccesPermission(flt,userDetails.accessFacilityList)"
                                    class="btn close"
                                    (click)="deassignRoleFacilityOrGroup(userDetails.email_id,'RESIDENT','FACILITY',flt,'')">
                                    <span aria-hidden="true">×</span>
                                </button>
                            </div>
                        </div>
                        <div class="d-table-cell d-grid border p-1" *ngIf="userDetails.residentModuleAccess=='YES'">
                            <span class="p-1 my-auto"><b>Resident Groups</b></span>
                            <div *ngFor="let grp of userDetails.residentModuleFacilityGroupList"
                                class="alert alert-light text-white bg-secondary alert-dismissible fade alert-content show">
                                <strong>{{grp.groupName}}</strong>
                                <button *ngIf="getUserAccesPermission(grp.facilityName,userDetails.accessFacilityList)"
                                    class="btn close"
                                    (click)="deassignRoleFacilityOrGroup(userDetails.email_id,'RESIDENT','GROUP','',grp)">
                                    <span aria-hidden="true">×</span>
                                </button>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </div>
    </div>
</div>