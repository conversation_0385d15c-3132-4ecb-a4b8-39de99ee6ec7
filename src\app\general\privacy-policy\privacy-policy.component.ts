import { Component, OnInit } from '@angular/core';
import { redirectUri } from '../../config';
import { FormGroup, FormBuilder, Validators, AbstractControl } from '@angular/forms';
import { JsonAppConfigService } from 'src/app/config/json-app-config.service';
import { CommonService } from 'src/app/services/common/common.service';
declare let $: any;

@Component({
  selector: 'app-privacy-policy',
  templateUrl: './privacy-policy.component.html',
  styleUrls: ['./privacy-policy.component.css']
})
export class PrivacyPolicyComponent implements OnInit {
  public reqiuestForm: FormGroup;
  public request: any = {};
  public submitted: boolean = false;
  public website_name: string = '';
  public rupparlapalli: string = 'rupparlapalli@@primehealthcare.com';
  constructor(private readonly fb: FormBuilder, private readonly commonServ: CommonService, private readonly jsonAppConfigService: JsonAppConfigService) { }

  ngOnInit(): void {
    this.reqiuestForm = this.fb.group({
      txtWebsite: ['', [Validators.required]],
      txtName: ['', [Validators.required]],
      txtEmail: ['', [Validators.required, Validators.email]],
      rdoRequestType: ['Whose name appears above', Validators.required],
      txtComments: ['', [Validators.pattern('^[0-9a-zA-Z-’\'"“” ()_?!:;.,stn]+$')]],
      chkConfirm1: [false, Validators.requiredTrue],
      chkConfirm2: [false, Validators.requiredTrue],
      chkConfirm3: [false, Validators.requiredTrue]
    });
  }

  get f(): { [key: string]: AbstractControl } {
    return this.reqiuestForm.controls;
  }

  submit() {
    this.submitted = true;
    if (this.reqiuestForm.invalid) {
      return;
    }
    this.commonServ.startLoading();
    this.request.web_site_name = this.reqiuestForm.value.txtWebsite;
    this.request.name = this.reqiuestForm.value.txtName;
    this.request.email = this.reqiuestForm.value.txtEmail;
    this.request.submitting_as = this.reqiuestForm.value.rdoRequestType;
    this.request.comments = this.reqiuestForm.value.txtComments;
    this.jsonAppConfigService.insertDataRequestForm(this.request);
    setTimeout(() => {
      alert('Request submitted successfully!.');
      this.commonServ.stopLoading();
      $('#submitFormModel').modal('hide');
    }, 2000);
  }

  scroll(el: HTMLElement) {
    el.scrollIntoView({ behavior: 'smooth' });
  }

  navigateToFrom() {
    window.location.href = redirectUri + '/#/privacy/data-request-form';
  }

  navigateToFrom2() {
    $('#submitFormModel').modal('show');
  }

}
