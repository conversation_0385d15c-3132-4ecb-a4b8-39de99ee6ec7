import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { apiUrl } from 'src/app/config';

@Injectable({
  providedIn: 'root'
})
export class AuditorService {
  constructor(private readonly http: HttpClient) { }
  GetPatientsChargesDataAuditor(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Auditor/FetchPatientsForAuditor', request, { headers: headers });
  }

  getEncountersByPatient(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Auditor/AsyncGetEncountersByPatientForAuditor', request, { headers: headers });
  }

  markAsCodedOrNorCodeded(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Auditor/ConfirmMarkAsCoded', request, { headers: headers });
  }

}
