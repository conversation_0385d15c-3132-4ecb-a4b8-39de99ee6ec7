import { Component, OnInit } from '@angular/core';
import { BillingService } from 'src/app/services/billing/billing.service';
import { FacilityModel } from 'src/app/models/facility.model';
import { CommonService } from 'src/app/services/common/common.service';
import { PhysicianModel } from 'src/app/models/physician.model';
import { PatientModel } from 'src/app/models/patient.model';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { AppComponent } from 'src/app/app.component';
import { FormGroup, FormBuilder, Validators, FormControl } from '@angular/forms';

import * as moment from 'moment';
import { DatePipe } from '@angular/common';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-billing',
  templateUrl: './billing.component.html',
  styles: []
})
export class BillingComponent implements OnInit {

  public listOfPatients: Array<any> = [];
  public listOfInitialPatients = Array<PatientModel>();
  public listOfFacilities = Array<FacilityModel>();
  public listOfAllPhysicians = Array<PhysicianModel>();
  public lisfOfEncounters: Array<any> = [];
  public request: any = {};
  public userIdleTime: number = 0;
  public ddlFacility: string = "";
  public searchByName: string = "";
  public searchByFacility: string = "";
  public encounterId: number;
  public accountNo: string;
  public FilterForm: FormGroup;
  public submitted: boolean = false;
  public PatientObject: any = {};
  public encounterObj: any = {};
  public lisfOfGroupEncounters: Array<any> = [];
  public listOfHistory: Array<any> = [];
  public groupAlerts: any = {};
  public groupOfIcds: Array<any> = [];
  public envAccountNo: string = "";
  public envMrnNo: string = "";
  public envFacility: string = "";
  public isEnvRemoveFromBilling: number = 1;
  public lisfOfGroupEncountersForKareo: Array<any> = [];
  public confirmationMessage: string = "";
  public kareoPreConfirmationMassage: string = "";
  public ssnConfirmationMessage: string = "";
  public firstEcounterIdToOpen: string = "";
  public p: number = 1;
  config: any;
  public lisfOfGroupEncountersForRXNT: Array<any> = [];
  public rxntPreConfirmationMassage: string = "";

  public lisfOfGroupEncountersForADMD: Array<any> = [];
  public admdPreConfirmationMassage: string = "";
  public totalCount: number;
  public isICDChange: string = "false";
  public daterange: any = {};
  public arrival = '';
  public arrivalLabel: string = "";
  public arrivalDateVal: boolean = false;
  public facilitystr: string = "";
  public isDisabled: boolean = false;

  startDate: any = moment().subtract(6, 'month').startOf('month');
  endDate: any = moment();
  updateDate: any = '';

  public options: any = {
    locale: { format: 'MM/DD/YYYY' },
    alwaysShowCalendars: false,
    showCustomRangeLabel: true,
    ranges: {
      'Last 6 Months': [moment().subtract(6, 'month').startOf('month'), moment()],
      'Last Year': [moment().startOf('year').subtract(1, 'year'), moment().endOf('year').subtract(1, 'year')],
      'This Year': [moment().startOf('year'), moment()]

    },
    linkedCalendars: false,
    autoApply: false,
    autoUpdateInput: true,
    parentEl: 'arrival_date_menu',
    opens: 'center',
    drops: 'auto',
    showDropdowns: true,
    applyButtonClasses: 'btn-outline-info',
    cancelButtonClasses: 'btn-outline-secondary'
  };
  device = false;
  public timeout: any = null;
  public isClearData: boolean = false;
  constructor(private readonly billingServ: BillingService, private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService
    , private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly datePipe: DatePipe, private readonly toastr: ToastrService) {
    this.daterange.start = moment().subtract(6, 'month').startOf('month');
    this.daterange.end = moment();
    this.daterange.label = 'Last 6 Months Admission Data';
  }

  ngOnInit() {
    if (/Android|webOS|iPhone|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) && (window.innerWidth <= 575 || window.innerWidth >= 575)) {
      // some code..
      this.device = true;
    } else {
      this.device = false;
    }

    this.appComp.loadPageName('Billing Overview', 'billingTab');

    this.FilterForm = this.fb.group({
      ddlFacility: [''],
      ddlPhysician: ['All', Validators.required],
      encounterSeenDateFrom: ['', [Validators.nullValidator, endDateValidator]],
      encounterSeenDateTo: ['', [Validators.nullValidator, endDateValidator]],
      ddlBillUnbill: ['']
    });
    this.arrival = this.arrival ? this.arrival : this.datePipe.transform(this.daterange.start, 'MM/dd/yyyy') + ' - ' + this.datePipe.transform(this.daterange.end, 'MM/dd/yyyy');
    this.arrivalLabel = this.arrivalLabel ? this.arrivalLabel : this.daterange.label;
    this.options.startDate = this.updateDate ? this.datePipe.transform(this.updateDate.start, 'MM/dd/yyyy') : this.startDate;
    this.options.endDate = this.updateDate ? this.datePipe.transform(this.updateDate.end, 'MM/dd/yyyy') : this.endDate;
    this.getBillerFacilities();
  }

  // convenience getter for easy access to form fields
  get f() { return this.FilterForm.controls; }

  selectedDate(value: any, datepicker?: any) {
    // any object can be passed to the selected event and it will be passed back here
    datepicker.start = value.start;
    datepicker.end = value.end;
    // use passed valuable to update state
    this.daterange.start = value.start;
    this.daterange.end = value.end;
    this.daterange.label = this.datePipe.transform(value.start, 'MM/dd/yyyy') + ' - ' + this.datePipe.transform(value.end, 'MM/dd/yyyy');
    this.arrival = this.datePipe.transform(this.daterange.start, 'MM/dd/yyyy') + ' - ' + this.datePipe.transform(this.daterange.end, 'MM/dd/yyyy');
    this.arrivalLabel = this.daterange.label;
    this.updateDate = JSON.stringify(value);
    this.arrivalDateVal = false;
  }

  getBillers() {
    this.commonServ.startLoading();
    this.billingServ.getPatientsForBilling().subscribe((p: any) => {
      if (p) {
        this.listOfPatients = p.listofPatients;
        this.listOfPatients.forEach(x => x.patient_Name = this.encrDecr.get(x.patient_Name));
        this.listOfPatients.forEach(x => x.account_Number = this.encrDecr.get(x.account_Number));
        this.listOfPatients.forEach(x => x.dob = this.encrDecr.get(x.dob));
        this.listOfInitialPatients = p.listofPatients;
        this.listOfFacilities = p.groupOfFacilities;
      }
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  FetchFacilitiesAndPatientCount() {
    this.commonServ.startLoading();
    this.billingServ.AsyncFetchFacilitiesAndPatientCount().subscribe((p: any) => {
      if (p) {
        this.listOfFacilities = p;
      }
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  getBillerFacilities() {
    this.commonServ.getFacilitiesByUserType('BILLERWITHGROUPS').subscribe((p: any) => {
      this.listOfFacilities = p;
      this.FilterForm.get('ddlFacility')?.setValue(this.listOfFacilities[0].facilityName);
      this.getPhysiciansByFacility(this.FilterForm.value.ddlFacility);
    }, error => { console.error(error.status); });
  }

  filterBillerPatients() {
    this.submitted = true;
    if (this.FilterForm.invalid) {
      return;
    }
    this.commonServ.startLoading();
    this.request.Facility_Name = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.PhysicianId = this.encrDecr.set(this.FilterForm.value.ddlPhysician);
    this.request.MarkasSeenDateFrom = this.encrDecr.set(this.FilterForm.value.encounterSeenDateFrom);
    this.request.MarkasSeenDateTo = this.encrDecr.set(this.FilterForm.value.encounterSeenDateTo);
    this.billingServ.filterBillerPatients(this.request).subscribe((p: any) => {
      this.listOfPatients = p;
      this.request = {};
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }
  filterBillerPatientsData(pno) {
    this.p = pno;
    this.submitted = true;
    if (this.FilterForm.invalid) {
      return;
    }

    if (!this.FilterForm.value.encounterSeenDateFrom && !this.FilterForm.value.encounterSeenDateTo && this.FilterForm.value.encounterSeenDateFrom > this.FilterForm.value.encounterSeenDateTo) {
      return;
    }

    this.commonServ.startLoading();
    this.request.Facility_Name = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.PhysicianId = this.encrDecr.set(this.FilterForm.value.ddlPhysician);
    this.request.MarkasSeenDateFrom = this.encrDecr.set(this.FilterForm.value.encounterSeenDateFrom);
    this.request.MarkasSeenDateTo = this.encrDecr.set(this.FilterForm.value.encounterSeenDateTo);
    this.request.strPageNumber = this.encrDecr.set(pno);
    this.request.strPagesize = this.encrDecr.set("14");
    this.request.searchText = this.encrDecr.set(this.searchByName);
    this.request.AdmissionDateRange = this.encrDecr.set(this.arrival);
    this.request.BillStatus = this.encrDecr.set(this.FilterForm.value.ddlBillUnbill);
    this.billingServ.filterBillerPatientsData(this.request).subscribe((p: any) => {

      this.totalCount = p.patientCount[0].totalCount;
      this.listOfPatients = p.patient;
      this.arrivalDateVal = false;
      this.submitted = false;
      this.request = {};
      this.PatientObject = {};
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  onKeyPatientSearch() {
    clearTimeout(this.timeout);
    if (this.FilterForm.invalid) {
      return;
    }

    this.timeout = setTimeout(() => {
      this.filterBillerPatientsData(1);
    }, 2000);
  }

  chageEventFilterBillerPatients() {
    this.commonServ.startLoading();
    this.request.Facility_Name = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.PhysicianId = this.encrDecr.set(this.FilterForm.value.ddlPhysician);
    this.request.MarkasSeenDateFrom = this.encrDecr.set('');
    this.request.MarkasSeenDateTo = this.encrDecr.set('');
    this.billingServ.filterBillerPatients(this.request).subscribe((p: any) => {
      this.listOfPatients = p;
      this.request = {};
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  getFacilities() {
    this.commonServ.getFacilities().subscribe((p: any) => {
      this.listOfFacilities = p;
    }, error => { console.error(error.status); });
  }

  getPhysiciansByFacility(facillity) {
    if (facillity == 'All') {
      facillity = '';
    }
    this.commonServ.startLoading();
    this.FilterForm.get('ddlPhysician')?.setValue('All');
    this.searchByFacility = facillity;
    this.filterBillerPatientsData("1");

    this.searchByName = '';
    this.p = 1;
    this.commonServ.getPhysiciansByUserType(facillity, 'BILLER').subscribe((p: any) => {
      this.listOfAllPhysicians = p;

    }, error => {
      this.commonServ.stopLoading();
    });


  }

  getEncountersByPatient(pObj, ui_click: boolean) {
    if (!ui_click || this.PatientObject.account_Number != pObj.account_Number) {
      this.envAccountNo = pObj.account_Number;
      this.envMrnNo = pObj.mrn;
      this.envFacility = pObj.facility_Name;
      this.commonServ.startLoading();
      this.request.account_Number = this.encrDecr.set(pObj.account_Number);
      this.request.facilityName = this.encrDecr.set(pObj.facility_Name);
      this.request.PHYSICIANMAILID = this.encrDecr.set(this.FilterForm.value.ddlPhysician);
      this.request.MRN = this.encrDecr.set(pObj.mrn);
      this.request.BillStatus = this.encrDecr.set(this.FilterForm.value.ddlBillUnbill);
      this.billingServ.getEncountersByPatient(this.request).subscribe((p: any) => {
        this.lisfOfEncounters = p.listofEncounters;
        this.lisfOfEncounters.forEach(y => y.listOfEncounters.forEach(x => x.encounterseendate = this.encrDecr.get(x.encounterseendate)));
        this.isEnvRemoveFromBilling = p.isEnvRemoveFromBilling;
        this.firstEcounterIdToOpen = p.firstEcounterIdToOpen;
        this.request = {};
        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        console.error(error.status);
        this.commonServ.stopLoading();
      });
      this.PatientObject = pObj;
    }
  }

  refreshEncountersByPatient(pObj, encId) {
    this.request.account_Number = this.encrDecr.set(pObj.account_Number);
    this.request.facilityName = this.encrDecr.set(pObj.facility_Name);
    this.request.PHYSICIANMAILID = this.encrDecr.set(this.FilterForm.value.ddlPhysician);
    this.request.MRN = this.encrDecr.set(pObj.mrn);
    this.request.sEncounterId = this.encrDecr.set(encId);
    this.billingServ.getEncountersByPatient(this.request).subscribe((p: any) => {
      this.lisfOfEncounters = p.listofEncounters;
      this.lisfOfEncounters.forEach(y => y.listOfEncounters.forEach(x => x.encounterseendate = this.encrDecr.get(x.encounterseendate)));
      this.isEnvRemoveFromBilling = p.isEnvRemoveFromBilling;
      this.firstEcounterIdToOpen = p.firstEcounterIdToOpen;
      this.request = {};
    }, error => {
      this.request = {};
    });
  }

  removeBillingQueue(pObj) {
    this.PatientObject = pObj;
  }

  clearFilterBillerPatients() {
    this.submitted = false;
    this.FilterForm.get('encounterSeenDateFrom')?.setValue('');
    this.FilterForm.get('encounterSeenDateTo')?.setValue('');
    if (this.isClearData) {
      this.filterBillerPatientsData(1);
    }
    this.isClearData = false;
  }

  clickFilterBillerPatientsData() {
    this.isClearData = true;
    this.filterBillerPatientsData(1);
  }

  
}

export function endDateValidator(control: FormControl) {
  let startDate = control.root.get('encounterSeenDateFrom');
  let endDate = control.root.get('encounterSeenDateTo');
  if (startDate && endDate) {
    let startDateVal = new Date(startDate.value);
    let endDateVal = new Date(endDate.value);
    let errorObj = { 'encounterSeenDateFrom': false, 'encounterSeenDateTo': false };
    let isEndDateValid = true;
    if (startDate.value && endDate.value) {
      isEndDateValid = (startDateVal < endDateVal || startDateVal.getDate() == endDateVal.getDate());
    }
    endDate.setErrors((!isEndDateValid)
      ? errorObj : null);
    startDate.setErrors((!isEndDateValid)
      ? errorObj : null);
    if (control.errors) { return { "endDateError": true } };
  }
  return null;
}
