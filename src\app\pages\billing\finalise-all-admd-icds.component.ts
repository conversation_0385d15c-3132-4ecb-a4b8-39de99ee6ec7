import { Component, Input } from '@angular/core';
import { BillingService } from 'src/app/services/billing/billing.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { CommonService } from 'src/app/services/common/common.service';
import { BillingComponent } from './billing.component';
declare let $: any;

@Component({
  selector: 'app-finalise-all-admd-icds',
  templateUrl: './finalise-all-admd-icds.component.html',
  styles: []
})
export class FinaliseAllADMDIcdsComponent {
  @Input() lisfOfGroupEncountersForADMD: Array<any> = [];
  public groupOfIcds: Array<any> = [];
  @Input() admdPreConfirmationMassage: string = "";
  public request: any = {};
  @Input() confirmationMessage: string = "";
  @Input() PatientObject: any;
  public isValidationMessage: boolean = false;
  public HighlightRow = -1;
  constructor(private readonly billingServ: BillingService, private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService, private readonly billComp: BillingComponent) { }

  ConfirmIcds() {
    this.confirmationMessage = "You are submitting encounter(s) for the below physician(s) <br/>";
    this.confirmationMessage = this.confirmationMessage + this.admdPreConfirmationMassage + "Please click submit to send the selected encounter(s) to .<br/>Note: Once you submit the encounter(s) to ADMD, You can't make changes to the encounter(s) in Grand Rounds.";
  }

  back1() {
    this.admdPreConfirmationMassage = "";
    this.groupOfIcds = [];
  }

  back2() {
    this.confirmationMessage = "";
  }

  chkChangeEvent(event) {
    for (let item of this.lisfOfGroupEncountersForADMD) {
      if (item.encounteR_ID == event.target.id) {
        item.checked = event.target.checked;
        this.isValidationMessage = false;
      }
    }
  }

  finalizeADMDICdsAll(lstOfGEnvs) {
    let message: string = "";
    lstOfGEnvs.forEach(x => {
      if (x.checked) {
        x.listOfIcds.forEach(y => {
          if (!this.groupOfIcds.includes(y)) {
            this.groupOfIcds.push(y);
          }
        });
        message = message + x.posT_TO_BILLED_BY + " for date of service " + x.encounterseendate + "<br/>";
      }
    });
    if (message != "")
      this.admdPreConfirmationMassage = message;
    else
      this.isValidationMessage = true;
  }

  submitEncounterToADMDAll(lstEnvs, pObj) {
    this.commonServ.startLoading();
    this.groupOfIcds = [];
    let listOfFinalizedIcds: Array<any> = [];
    $('select[id*="ddlIcdAll"] option').each(function (index, value) {
      listOfFinalizedIcds.push(value.innerHTML);
    });
    this.request.listOfEncounters = lstEnvs.filter(x => x.checked);
    this.request.IcdsArray = listOfFinalizedIcds;
    listOfFinalizedIcds = [];
    this.billingServ.sendAllEncounterToADMD(this.request).subscribe((p: any) => {
      this.request = {};
      $("#finalizeADMDICdsAll").modal('hide');
      this.admdPreConfirmationMassage = "";
      this.confirmationMessage = "";
      if (p > 0) {
        this.billComp.refreshEncountersByPatient(pObj, '');
      }
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
      console.error(error.status);
    });

  }

  clickedRow(rowIndex) {
    this.HighlightRow = rowIndex;
  }
  moveSelectedUp() {
    let newIndex = this.HighlightRow - 1;
    if (this.HighlightRow>-1 && newIndex >= 0 && newIndex <= this.groupOfIcds.length - 1) {
      let currentICDs = this.groupOfIcds[this.HighlightRow];
      this.groupOfIcds[this.HighlightRow] = this.groupOfIcds[newIndex];
      this.groupOfIcds[newIndex] = currentICDs;
      this.HighlightRow = newIndex;
    }
  }
  moveSelectedDown() {
    let newIndex = this.HighlightRow + 1;
    if (this.HighlightRow>-1 && newIndex >= 0 && newIndex <= this.groupOfIcds.length - 1) {
      let currentICDs = this.groupOfIcds[this.HighlightRow];
      this.groupOfIcds[this.HighlightRow] = this.groupOfIcds[newIndex];
      this.groupOfIcds[newIndex] = currentICDs;
      this.HighlightRow = newIndex;
    }
  }

}
