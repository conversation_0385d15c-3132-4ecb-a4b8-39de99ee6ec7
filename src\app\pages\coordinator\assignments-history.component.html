<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new p-2" style="background: #0169ab;" [formGroup]="FilterForm">
                    <div class="align-items-center row mx-auto">
                        <div class="col-12 px-0">

                            <div class="row mt-1 mx-auto">
                                <div class="col-12 col-md-3 px-1 py-1">
                                    <select id="ddlFacility" class="form-control" formControlName="ddlFacility"
                                        (change)="getCoordinatorPatients(1)"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                                        <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                                            <span>{{s.facilityName}}</span>
                                        </option>
                                    </select>
                                </div>
                                <div class="col-12 col-md-2 px-1 py-1">
                                    <input id="assignmentDate" type="date" [min]="startDate" [max]="endDate"
                                        class="form-control" formControlName="assignmentdate" [(ngModel)]="currentDate"
                                        title="Assignment Date"
                                        [ngClass]="{ 'is-invalid': submitted && f['assignmentdate'].errors}">
                                </div>

                                <div class="col-12 col-md-2 text-right text-md-left px-1 py-1 align-self-center">
                                    <button title="Save Assignments" class="btn btn-outline-info px-2 btn-block"
                                        type="submit" (click)="getCoordinatorPatients(1)">Get Assignments</button>
                                </div>
                                <div class="col-12 col-md-1 text-right text-md-left px-1 py-1">
                                    <ng-container *ngIf="listOfPatients.length>0">
                                        <img alt=' ' src=".././../../assets/img/excel.png" class="downloadIcon shadow"
                                            (keyup)="exportAsXLSX()" (click)="exportAsXLSX()" title="Export Excel">
                                    </ng-container>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row my-1 mx-auto">
                        <div class="col-12 col-md-5 px-1 py-1">
                            <div class="input-group">
                                <input type="text" class="form-control small" maxlength="1000"
                                    placeholder="Search for Account No#, Patient Name or Attending Provider"
                                    aria-label="Search" aria-describedby="basic-addon2" [(ngModel)]="searchByName"
                                    [ngModelOptions]="{standalone: true}" (keyup)="onKeySearchCoordinatorPatients()">
                                <div class="input-group-append">
                                    <button class="bg-white btn text-dark"><i class="fas fa-search fa-sm"></i></button>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-5 pr-md-1 py-1">

                            <div class="input-group">
                                <div class="custom-file">
                                    <input #file type="file" id="attchFileId" (change)="uploadChange(file.files)"
                                        class="custom-file-input" aria-describedby="attchFileId">
                                    <label class="custom-file-label" for="inputGroupFile04">{{ fileName ? fileName :
                                        'Upload an Assignment Data Excel sheet'}}</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-1 text-right text-md-left px-1 py-1 align-self-center">
                            <button title="Save Assignments" class="btn btn-outline-info px-2 btn-block" type="submit"
                                (click)="saveAssignments(0)">Upload</button>
                        </div>
                    </div>
                    <div class="row mx-auto" *ngIf="submitted && submit">
                        <div class="col-12 col-md-6 pr-md-1 py-1"></div>
                        <div class="col-12 col-md-6 pr-md-1 py-1">
                            <div class="col">
                                <div class="text-danger" *ngIf="isFileRequired && submit">
                                    Please select file.
                                </div>
                                <div class="text-danger" *ngIf="isFileValidName && submit">
                                    Please upload a file with valid name.
                                </div>
                                <div class="text-danger" *ngIf="isFile && submit">
                                    Please select a valid file to upload.
                                </div>
                                <div class="text-danger" *ngIf="isFileSize && submit">
                                    Uploaded file size should be less than 20 MB.
                                </div>
                            </div>
                        </div>

                    </div>
                </div>

                <!-- Card Body -->
                <div class="card-body px-2 py-1 scroll-x">
                    <div class="row mx-0">
                        <div class="col-12 px-0">
                            <!-- table start -->
                            <div>
                                <table class="table mb-0 text-small width-100per" border="1">
                                    <thead>
                                        <tr>
                                            <th class="width-4per text-center align-middle pb-0 p0erm">Account No#</th>
                                            <th class="width-3per text-center align-middle pb-0 p0erm">Patient Name</th>
                                            <th class="width-4per text-center align-middle pb-0 p0erm">Facility</th>
                                            <th class="min-width-11 text-center align-middle pb-0 p0erm">Notes</th>
                                            <th class="min-width-11 text-center align-middle pb-0 p0erm">Attending
                                                Provider</th>
                                            <th class="min-width-11 text-center align-middle pb-0 p0erm">Assignment Date
                                            </th>
                                            <th class="min-width-11 text-center align-middle pb-0 p0erm">Created Date
                                            </th>
                                            <th class="min-width-11 text-center align-middle pb-0 p0erm">Created By</th>
                                            <th class="min-width-11 text-center align-middle pb-0 p0erm">Last Modified
                                                Date</th>
                                            <th class="min-width-11 text-center align-middle pb-0 p0erm">Last Modified
                                                By</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr
                                            *ngFor="let item of listOfPatients | paginate: { itemsPerPage: 10, currentPage: p,totalItems:totalCount}; let i= index;">
                                            <td class="width-4per text-center p0erm">{{item.account_Number}}</td>
                                            <td class="width-3per text-center p0erm">{{item.patient_Name}}</td>
                                            <td class="width-4per text-center p0erm">{{item.facility_Name}}</td>
                                            <td class="width-4per text-center p0erm">{{item.coordinator_Notes}}</td>
                                            <td class="width-4per text-center p0erm">{{item.attending_Physician_InApp}}
                                            </td>
                                            <td class="width-4per text-center p0erm">
                                                {{item.attending_Provider_Assignment_Date}}</td>
                                            <td class="width-4per text-center p0erm"> {{item.created_Date}}</td>
                                            <td class="width-4per text-center p0erm"> {{item.created_By}}</td>
                                            <td class="width-4per text-center p0erm"> {{item.last_Modified_Date}}</td>
                                            <td class="width-4per text-center p0erm"> {{item.last_Modified_By}}</td>

                                        </tr>
                                        <tr *ngIf="totalCount==0">
                                            <td colSpan="14" class="text-center">
                                                <span>No Results Found</span>

                                            </td>
                                        </tr>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <td colspan="14" class="m-0 p-0" style="background: white !important;">
                                                <pagination-controls previousLabel="" nextLabel=""
                                                    (pageChange)="getCoordinatorPatients($event)"></pagination-controls>
                                            </td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            <!-- table end -->
                        </div>
                    </div>

                </div>

            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="assignmentConfModel" tabindex="-1" aria-labelledby="assignmentConfModelLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title h5" id="assignmentConfModelLabel">Upload Assignments</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <div class="form-group align-items-center">
                    <span class="text-center">Upload file contains duplicate entries. Are you sure want to
                        overridden?</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary btn-sm" (click)="saveAssignments(2)"
                    data-dismiss="modal">No</button>
                <button type="button" class="btn btn-outline-info btn-sm" (click)="saveAssignments(1)">Yes</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="assignmentModel" tabindex="-1" aria-labelledby="assignmentModelModelLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title h5" id="assignmentModelModelLabel">Upload Assignments</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <div class="form-group align-items-center">
                    <span class="text-center">Are you sure you want to update the assignments for past date?</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary btn-sm" (click)="saveAssignments(2)"
                    data-dismiss="modal">No</button>
                <button type="button" class="btn btn-outline-info btn-sm" (click)="saveAssignments(1)">Yes</button>
            </div>
        </div>
    </div>
</div>
<!-- Page Content Ends -->