import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';
import { Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { EncrDecrServiceService } from './services/common/encr-decr-service.service';
import { CommonService } from './services/common/common.service';
declare let $: any;
@Injectable({
  providedIn: 'root'
})
export class AuthInterceptor implements HttpInterceptor {

  constructor(
    private readonly router: Router,
    private readonly encrDecr: EncrDecrServiceService,
    private readonly commonServ: CommonService
  ) {
  }
  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {

    const token: string = this.commonServ.extractMSALToken()!;
    if (token) {
      let encryptedToken = this.encrDecr.set(token);
      if (encryptedToken) {
        let encryptedlatestToken = this.encrDecr.set(encryptedToken);
        request = request.clone({ headers: request.headers.set('Authorization', 'Bearer ' + encryptedlatestToken) });

        // FOR SECURITY
        request = request.clone({ headers: request.headers.set('withCredentials', 'true') });
        request = request.clone({ headers: request.headers.set('Access-Control-Allow-Credentials', 'true') });
        // TO CLEAR CACHE
        request = request.clone({ headers: request.headers.set('Cache-Control', ['no-cache', 'no-store', 'must-revalidate', 'pre-check=0', 'post-check=0']) });
        request = request.clone({ headers: request.headers.set('Pragma', 'no-cache') });
      }

    }
    else {
      window.location.href = '/';
    }

    /**
     * continues request execution
     */
    return next.handle(request).pipe(catchError((error, caught) => {
      // intercept the respons error and displace it to the console
      this.handleAuthError(error);
      return of(error);
    }) as any);
  }

  /**
     * manage errors
     * @param err
     * @returns {any}
     */
  private handleAuthError(err: HttpErrorResponse): Observable<any> {

    // handle your auth error or rethrow
    if (err.status > 399 && err.status < 600 && err.error != 'Unauthorized Token') {
      $('#loading').hide();
      console.log('Error: ', err.error);
      console.log('Error Message: ', err.message);
      this.router.navigate(['/error'], { state: { errorMessage: err.error } });
      return of(err.message);
    }
    else if (err.error == 'Unauthorized Token') {
      window.location.href = '/';
    }

    throw err;
  }

}
