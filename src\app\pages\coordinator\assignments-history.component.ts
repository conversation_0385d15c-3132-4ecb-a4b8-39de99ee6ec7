import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { AppComponent } from 'src/app/app.component';
import { ExcelServices } from 'src/app/services/common/ExcelService';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { CoordinatorService } from 'src/app/services/coordinator/coordinator.service';
import { DatePipe } from '@angular/common';
import * as moment from 'moment';
declare let $: any;
@Component({
  selector: 'app-assignments-history',
  templateUrl: './assignments-history.component.html',
  styles: []
})
export class AssignmentsHistoryComponent implements OnInit {
  public listOfFacilities: Array<any> = [];
  public listOfGroups: Array<any> = [];
  public listofDepartments: Array<any> = [];
  public FilterForm: FormGroup;
  public request: any = {};
  public grprequest: any = {};
  public listOfPatients: Array<any> = [];
  public listOfPhysicians: Array<any> = [];
  public totalCount: number;
  public p: number = 1;
  public searchByName: string = '';
  public patient: any = {};
  public submitted: boolean = false;
  public submit: boolean = false;
  public nonPrimeCount: number = 0;
  public daterange: any = {};
  public isFileRequired: boolean = true;
  public isCommentLength: boolean = false;
  public isFile: boolean = false;
  public isFileValidName: boolean = false;
  public isFileSize: boolean = false;
  public fileName: string = "";
  public comment: string = "";
  public progress: number;
  public message: string;
  public currentDate: string;
  public formData = new FormData();
  public timeout: any = null;
  public fileMimeArray: Array<string> = ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"];

  public startDate: any;
  public endDate: any;


  constructor(private readonly coordinatorServ: CoordinatorService, private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService
    , private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly excelService: ExcelServices, private readonly toastr: ToastrService, private readonly datePipe: DatePipe) {
    const date = new Date();
    this.startDate = this.datePipe.transform(date.setMonth(-6), 'yyyy-MM-dd');
    this.endDate = this.datePipe.transform(new Date(), 'yyyy-MM-dd');
  }

  ngOnInit(): void {
    this.appComp.loadPageName('Assignments History', 'coordinatorTab');
    this.FilterForm = this.fb.group({
      ddlFacility: ['All', Validators.required],
      assignmentdate: ['', Validators.required]
    });
    this.getFacilitiesByUserType();
  }

  get f() { return this.FilterForm.controls; }

  getFacilitiesByUserType() {
    this.commonServ.getFacilitiesByUserType('COORDINATOR').subscribe((p: any) => {
      this.listOfFacilities = p;
      if (this.listOfFacilities.length > 0) {
        this.FilterForm.get('ddlFacility')?.setValue(this.listOfFacilities[0].facilityName);
        this.currentDate = this.datePipe.transform(new Date(), 'yyyy-MM-dd')!;
        this.getCoordinatorPatients(1);
        this.commonServ.stopLoading();
      }
    });
  }
  getCoordinatorPatients(pageNo) {
    this.submitted = true;
    if (this.FilterForm.invalid) {
      return;
    }
    this.commonServ.startLoading();
    this.nonPrimeCount = 0;
    this.p = pageNo;
    this.request.Facility_Name = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.Attending_Provider_Assignment_Date = this.encrDecr.set(this.datePipe.transform(this.FilterForm.value.assignmentdate ? this.FilterForm.value.assignmentdate : moment(), 'MM/dd/yyyy hh:mm:ss a'));
    this.request.PatientName = this.encrDecr.set(this.searchByName);
    this.coordinatorServ.getCoordinatorAssignmentHistory(this.request).subscribe((p: any) => {
      this.submitted = false;
      this.commonServ.stopLoading();
      this.totalCount = p ? p.length : 0;
      this.listOfPatients = p;

    }, error => {
      console.error(error.status);
      this.submitted = false;
      this.commonServ.stopLoading();
    });

  }
  onKeySearchCoordinatorPatients() {
    clearTimeout(this.timeout);
    if (this.FilterForm.invalid) {
      return;
    }

    this.timeout = setTimeout(() => {
      this.getCoordinatorPatients(1);
    }, 2000);
  }
  saveAssignments(status) {
    this.submitted = true;
    this.submit = true;
    let assignmentdate: any = this.datePipe.transform(this.FilterForm.value.assignmentdate, 'yyyy-MM-dd');
    let todayDate: any = this.datePipe.transform(new Date(), 'yyyy-MM-dd');
    if (this.FilterForm.invalid) {
      return;
    }
    else if (this.isFileRequired || this.isFileSize || this.isFileValidName || this.isFile) {
      return;
    }
    else if (status == 0 && (new Date(assignmentdate) < new Date(todayDate))) {
      this.isCommentLength = false;
      this.isFileSize = false;
      this.isFileValidName = false;
      this.isFile = false;
      this.formData.append('Facility_Name', this.encrDecr.set(this.FilterForm.value.ddlFacility));
      this.formData.append('Attending_Provider_Assignment_Date', this.encrDecr.set(this.datePipe.transform(this.FilterForm.value.assignmentdate ? this.FilterForm.value.assignmentdate : moment(), 'MM/dd/yyyy hh:mm:ss a')));
      $('#assignmentModel').modal('show');
    }
    else if (status == 0 && (new Date(assignmentdate) >= new Date(todayDate))) {
      this.isCommentLength = false;
      this.isFileSize = false;
      this.isFileValidName = false;
      this.isFile = false;
      this.formData.append('Facility_Name', this.encrDecr.set(this.FilterForm.value.ddlFacility));
      this.formData.append('Attending_Provider_Assignment_Date', this.encrDecr.set(this.datePipe.transform(this.FilterForm.value.assignmentdate ? this.FilterForm.value.assignmentdate : moment(), 'MM/dd/yyyy hh:mm:ss a')));
      this.commonServ.startLoading();
      this.assignmentUploadFiles(this.formData);
    }
    else if (status == 2) {
      this.isFileRequired = true;
      this.submitted = false;
      this.submit = false;
      $("#attchFileId").val('');
      this.fileName = "";
      this.commonServ.stopLoading();
      return;
    }
    else {
      this.commonServ.startLoading();
      $('#assignmentConfModel').modal('hide');
      $('#assignmentModel').modal('hide');
      this.formData.append('Status', status);
      this.assignmentUploadFiles(this.formData);
    }

  }
  assignmentUploadFiles(formData: any) {
    this.commonServ.AssignmentUploadFiles(formData).subscribe((p: any) => {
      this.submitted = false;
      this.submit = false;
      if (p == -1) {
        this.commonServ.startLoading();
        $('#assignmentConfModel').modal('show');
      }
      else {
        this.isFileRequired = true;
        this.submitted = false;
        this.submit = false;
        $("#attchFileId").val('');
        this.formData.delete('Status');
        this.formData.delete("File");
        this.formData.delete('Facility_Name');
        this.formData.delete('Attending_Provider_Assignment_Date');
        this.comment = "";

        this.fileName = "";
        if (p > 0) {
          $('#assignmentConfModel').modal('hide');
          this.toastr.success("uploaded successfully");
          this.getCoordinatorPatients(1);
        }
        else
          if (p == -99) {
            this.toastr.error("File should contain following columns : 'account','patient_Name','attending_Physician','facility_Name' ");
          } else {
            this.toastr.error("The Data in the file is not matching with the facility you are choosing");
          }

      }
      this.commonServ.stopLoading();
    }, error => {
      this.formData.delete('Status');
      this.formData.delete('Facility_Name');
      this.formData.delete('Attending_Provider_Assignment_Date');
      this.commonServ.stopLoading();
      this.toastr.error("The Data in the file is not matching with the facility you are choosing");
    });

  }
  exportAsXLSX(): void {
    let fileName = "";
    fileName = (this.FilterForm.value.ddlFacility == 'All' ? 'All Facilities' : this.FilterForm.value.ddlFacility);

    this.commonServ.startLoading();
    this.request.Facility_Name = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.Attending_Provider_Assignment_Date = this.encrDecr.set(this.datePipe.transform(this.FilterForm.value.assignmentdate, 'MM/dd/yyyy hh:mm:ss a'));
    this.request.PatientName = this.encrDecr.set(this.searchByName);
    this.coordinatorServ.getCoordinatorAssignmentHistory(this.request).subscribe((p: any) => {
      if (p) {
        this.excelService.exportAsExcelFile(p, fileName);
      }
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }
  uploadChange(files) {
    this.formData = new FormData();
    const file = files[0];
    if (file.name.split('.').length > 2)
      this.isFileValidName = true;
    else
      this.isFileValidName = false;
    if (!this.fileMimeArray.includes(file.type))
      this.isFile = true;
    else
      this.isFile = false;
    if (file.size > 20000000)
      this.isFileSize = true;
    else
      this.isFileSize = false;
    if (files.length > 0) {
      this.formData.append("File", files[0]);
      this.fileName = files[0].name;
      this.isFileRequired = false;
    }
    else {
      this.isFileRequired = true;
    }
  }
  selectedDate(value: any, datepicker?: any) {
    // this is the date  selected
    console.log(value);


    // use passed valuable to update state
    this.daterange.start = value.start;
    this.daterange.end = value.end;
    if (value.label != 'Custom Range') {
      this.daterange.label = value.label;
    }
    else {
      this.daterange.label = this.datePipe.transform(value.start, 'MM/dd/yyyy') + ' - ' + this.datePipe.transform(value.end, 'MM/dd/yyyy');
    }

  }
}
