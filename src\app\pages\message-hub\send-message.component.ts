import { Component, OnInit, Input, Renderer2 } from '@angular/core';
import { CommonService } from 'src/app/services/common/common.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { MessageHubService } from 'src/app/services/message-hub/message-hub.service';
import { MessageHubComponent } from './message-hub.component';
import { ToastrService } from 'ngx-toastr';
declare let $: any;

@Component({
  selector: 'app-send-message',
  templateUrl: './send-message.component.html',
  styles: []
})
export class SendMessageComponent implements OnInit {
  public noteForm: FormGroup;
  public submitted: boolean = false;
  public request: any = {};
  public ddlVali: boolean = false;
  public listOfUsersAndGroups: Array<any> = [];
  @Input() listOfFacilities: Array<any> = [];

  constructor(private readonly commonServ: CommonService, private readonly fb: FormBuilder, private readonly encrDecr: EncrDecrServiceService, private readonly renderer: Renderer2
    , private readonly mgsServ: MessageHubService, private readonly msgComp: MessageHubComponent, private readonly toastr: ToastrService) { }

  ngOnInit() {
    this.noteForm = this.fb.group({
      ddlFacility: ['', Validators.required],
      ddlUserGroup: [''],
      txtMessage: ['', Validators.required]
    });

  }

  // convenience getter for easy access to form fields
  get f() { return this.noteForm.controls; }



  facilityChange(factility) {
    this.commonServ.startLoading();
    this.commonServ.getUserGroups(factility).subscribe((p: any) => {
      this.listOfUsersAndGroups = p;
      let ddlUserGroup = this.renderer.createElement('select');
      this.renderer.addClass(ddlUserGroup, "chosen-select");
      this.renderer.setAttribute(ddlUserGroup, 'id', 'ddlUserGroup');
      this.renderer.setAttribute(ddlUserGroup, 'multiple', '-1');
      $('#ddlUserGroup').html('');
      this.listOfUsersAndGroups.forEach(el => {

        let option = this.renderer.createElement('option');
        if (el.type == "GROUP") {
          this.renderer.setAttribute(option, 'value', "G-" + el.itemId);
        }
        else {
          this.renderer.setAttribute(option, 'value', "U-" + el.itemId);
        }
        const optionText = this.renderer.createText(el.name);
        this.renderer.appendChild(option, optionText);
        this.renderer.appendChild(ddlUserGroup, option);

      });
      $('#ddlUserGroupTD').html('');
      let tdd = $('#ddlUserGroupTD');
      this.renderer.appendChild(tdd[0], ddlUserGroup);

      $("#ddlUserGroup").chosen();
      $('.chosen-drop').hide();
      $("#ddlUserGroup_chosen").find(".search-field > input[type='text']").keyup(function (e) {
        let key = $(e.target).val();

        if ((key.length >= 1) && !(e.keyCode === 38 || e.keyCode === 40)) {
          $('.chosen-drop').show();
        }
        else {
          $('.chosen-drop').hide();
        }
      });

      $("#ddlUserGroup").trigger("chosen:updated");
      $("#ddlUserGroup").chosen();
      $("#ddlUserGroup").find(".search-field > input[type='text']").val('');
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.toastr.error('Something went wrong!!!');
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  sendMessage() {
    this.submitted = true;
    if (this.noteForm.invalid) {
      return;
    }
    let vParUsers = "";
    let vGrpsIds = "";
    if ($("#ddlUserGroup").val() != null && $("#ddlUserGroup").val() != "") {
      this.ddlVali = false;
      let vTotalIds = $("#ddlUserGroup").val().toString();
      let vSplitUsrGrps = vTotalIds.split(",");
      if (vSplitUsrGrps.length > 0) {
        for (let item of vSplitUsrGrps) {
          if (item.includes('U-')) {
            vParUsers = vParUsers + "," + item.replace("U-", "");
          }
          else if (item.includes('G-')) {
            vGrpsIds = vGrpsIds + "," + item.replace("G-", "");
          }
        }
      }
      this.commonServ.startLoading();
      this.request.ParticipantIds = this.encrDecr.set(vParUsers);
      this.request.ParticipantGroupIds = this.encrDecr.set(vGrpsIds);
      this.request.MessageText = this.encrDecr.set(this.noteForm.value.txtMessage);
      this.request.sMessageType = this.encrDecr.set('1');
      this.request.MediaType = this.encrDecr.set('TEXT');
      this.mgsServ.sendMessage(this.request).subscribe((p: any) => {
        this.request = {};
        this.noteForm.reset();
        this.submitted = false;
        $("#sendMessagePop").modal('hide');
        if (p.statusCode != 200) {
          this.toastr.error(p.message);
        }
        this.msgComp.getMessages();
        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        this.commonServ.stopLoading();
      });

    }
    else
      this.ddlVali = true;

    if (this.noteForm.invalid) {
      return;
    }

  }

  ddlChageEvent() {
    this.ddlVali = false;
  }

}