import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MessageHubRoutingModule } from './message-hub-routing.module';
import { SendMessageComponent } from './send-message.component';
import { MessageHubComponent } from './message-hub.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';
import { SharedModule } from 'src/app/shared.module';


@NgModule({
  imports: [
    CommonModule,
    MessageHubRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    NgxPaginationModule,
    SharedModule
  ],
  exports: [
    MessageHubComponent,
    SendMessageComponent,
  ],
  declarations: [
    MessageHubComponent,
    SendMessageComponent,
  ],
  providers: [],
  bootstrap: [MessageHubComponent]
})
export class MessageHubModule { }
