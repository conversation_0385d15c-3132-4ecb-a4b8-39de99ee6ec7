import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { apiUrl } from 'src/app/config';

@Injectable({
  providedIn: 'root'
})
export class AttachmentService {

  constructor(private readonly http: HttpClient) { }

  getAllAttachments(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/FileUpload/GetAttachmentsByUser', request, { headers: headers });
  }

}