.custom-control-label {
    color: #000000 !important;
    font-size: large;
}

.radio-color-div {
    color: #000000 !important;
    font-size: large;
}

/* Mobile-specific styles for CPT code popup */
@media (max-width: 767.98px) {
    .mobile-cpt-modal {
        max-width: 95% !important;
        margin: 0 auto;
    }

    .mobile-cpt-modal .modal-content {
        border-radius: 8px;
        overflow: hidden;
    }

    .mobile-cpt-modal .modal-header {
        padding: 0.5rem 1rem;
        background-color: #336699;
        color: white;
    }

    .mobile-cpt-modal .modal-title {
        font-size: 1rem;
        font-weight: 600;
    }

    .mobile-cpt-modal .modal-body {
        padding: 0.75rem;
        max-height: 70vh;
        overflow-y: auto;
    }

    .mobile-cpt-modal .quantity {
        display: inline-flex;
        margin-bottom: 0.5rem;
    }

    .mobile-cpt-modal .quantity__input {
        width: 30px;
        text-align: center;
    }

    .mobile-cpt-modal .form-control {
        font-size: 14px;
    }

    .mobile-cpt-modal .text-danger {
        text-align: center;
        margin-top: 0.5rem;
        font-size: 14px;
    }

    .mobile-cpt-modal .modal-footer {
        padding: 0.5rem;
        justify-content: center;
    }

    .mobile-cpt-modal .btn {
        min-width: 100px;
    }
}