<!-- Assign to Others Start -->
<div class="modal fade" id="mdlPhysician" tabindex="-1" aria-labelledby="PhysicianLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content" [formGroup]="FilterPhyForm">
            <div class="modal-header">
                <h5 class="modal-title h5" id="PhysicianLabel">Physicians</h5>
                <i class="fa fa-times closePopup" data-dismiss="modal"></i>
            </div>
            <div class="modal-body min50v">
                <div class="form-group">
                    <select id="ddlPhysician" class="form-control" formControlName="ddlPhysician"
                        [ngClass]="{ 'is-invalid': submitted && f1['ddlPhysician'].errors}">
                        <option value="">---Select Physician---</option>
                        <option [value]="s.physicianEmaiId" *ngFor="let s of listOfPhysicians">
                            <span>{{s.physicianName}}</span>
                        </option>
                    </select>
                </div>

            </div>
            <div class="modal-footer py-2">

                <button class="btn btn-outline-info float-right" type="submit"
                    (click)='assignToOthersSave()'>Save</button>
            </div>
        </div>
    </div>
</div>
<!-- Assign to Others End -->

<!-- Assign to Resident Start -->
<div class="modal fade" id="mdlResiPhysician" tabindex="-1" aria-labelledby="ResiPhysicianLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content" [formGroup]="FilterResiForm">
            <div class="modal-header">
                <h5 class="modal-title h5" id="ResiPhysicianLabel">Physicians</h5>
                <i class="fa fa-times closePopup" data-dismiss="modal"></i>
            </div>
            <div class="modal-body min50v">
                <div class="form-group">
                    <select id="ddlResiPhysician" class="form-control" formControlName="ddlResiPhysician"
                        [ngClass]="{ 'is-invalid': submitted1 && f2['ddlResiPhysician'].errors}">
                        <option value="">---Select Physician---</option>
                        <option [value]="s.residentEmail" *ngFor="let s of listOfProvider">
                            <span>{{s.residentName}}</span>
                        </option>
                    </select>
                </div>

            </div>
            <div class="modal-footer py-2">
                <button class="btn btn-outline-info float-right" type="submit"
                    (click)='assignToResidentsSave()'>Save</button>
            </div>
        </div>
    </div>
</div>
<!-- Assign to Resident End -->