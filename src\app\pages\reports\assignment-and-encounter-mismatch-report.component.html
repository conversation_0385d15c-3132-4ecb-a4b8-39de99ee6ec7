<!-- Begin Page Content Starts -->
<div class="container-fluid">
  <!-- Content Row -->
  <div class="row">
    <!-- Area Chart -->
    <div class="col px-0">
      <div class="card shadow border-blue">
        <!-- Card Header - Dropdown -->
        <div class="card-header-new p-2" style="background: #0169ab;" [formGroup]="FilterForm">
          <div class="align-items-center row mx-auto">
            <div class="col-12 px-0">

              <div class="row mt-1 mx-auto">
                <div class="col-12 px-1 py-1"
                  [ngClass]="{'col-md-3': this.listOfReports.length, 'col-md-4': !this.listOfReports.length}">
                  <select id="ddlFacility" class="form-control" formControlName="ddlFacility"
                    (change)="getPhysiciansByFacility($any($event.target).value)"
                    [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                    <option value="">---Select Facility---</option>
                    <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                      <span>{{s.facilityName}}</span>
                    </option>
                  </select>
                  <div *ngIf="submitted && f['ddlFacility'].errors" class="invalid-feedback">
                    <div *ngIf="f['ddlFacility'].errors['required']">Facility is required</div>
                  </div>
                </div>
                <div class="col-12 col-md-3 px-1 py-1">
                  <select id="ddlPhysician" class="form-control custom-control" formControlName="ddlPhysician"
                    [ngClass]="{ 'is-invalid': submitted && f['ddlPhysician'].errors}">
                    <option value="">---Select Physician---</option>
                    <option value="All">All</option>
                    <option [value]="s.physicianEmaiId" *ngFor="let s of listOfPhysicians">
                      {{s.physicianName}}
                    </option>
                  </select>
                  <div *ngIf="submitted && f['ddlPhysician'].errors" class="invalid-feedback">
                    <div *ngIf="f['ddlPhysician'].errors['required']">Physician is required</div>
                  </div>
                </div>
                <div class="col-12 col-md-3 px-1 py-1">
                  <select id="ddlYear" class="form-control" formControlName="ddlYear">
                    <option [value]="year" *ngFor="let year of listOfYears">
                      <span>{{year}}</span>
                    </option>
                  </select>
                  <div *ngIf="submitted && f['ddlYear'].errors" class="invalid-feedback">
                    <div *ngIf="f['ddlYear'].errors['required']">Year is required</div>
                  </div>
                </div>
                <div class="col-12 col-md-2 px-1 py-1">
                  <button class="btn btn-outline-info px-2 btn-block" type="submit" (click)="filterReport()">
                    <i class="fa-filter fas font-size-13"></i>
                    Filter
                  </button>
                </div>
                <div *ngIf="this.listOfReports.length>0" class="col-12 col-md-1 px-1 py-1 text-center">
                  <img alt=' ' (keyup)="exportAsXLSX()" (click)="exportAsXLSX()" class="ml-2 shadow" [src]="img1"
                    style="width:40px;cursor:pointer;" title="Export Excel">
                </div>

              </div>
              <div class="row my-1 mx-auto align-items-center">
                <div class="col-12 px-1 py-1"
                  [ngClass]="{'col-md-11': this.listOfReports.length, 'col-md-12': !this.listOfReports.length}">
                  <div class="input-group">
                    <input type="text" class="form-control small" maxlength="1000" placeholder="Search for..."
                      aria-label="Search" aria-describedby="basic-addon2" [(ngModel)]="searchByName"
                      [ngModelOptions]="{standalone: true}" (keyup)="search(searchByName)">
                    <div class="input-group-append">
                      <button class="bg-gradient-light btn text-dark" type="button">
                        <i class="fas fa-search fa-sm"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Card Body -->
        <div class="card-body px-2 py-1">
          <div class="row mx-0">
            <div class="col-12 px-0 pull_right note_text">
              <div class="display_inline unapproved_encounters">
                <span><i class="fas fa-fw fa-circle"></i></span><span>Missing Encounter </span>
              </div>
              <div class="display_inline created_encounters">
                <span><i class="fas fa-fw fa-circle"></i></span><span>Created Encounters</span>
              </div>
              <div class="display_inline billed_encounters">
                <span><i class="fas fa-fw fa-circle"></i></span><span>Total Assignments</span>
              </div>
            </div>
          </div>
          <div class="row mx-0">
            <div class="col-12 px-0">

              <table class="table table-striped table-bordered table-hover billed_encounters_grid">
                <thead>
                  <tr class="text-center">
                    <th class="text-center pb-0 cursor-pointer" (keyup)="sortColumn('facilitY_Name')"
                      (click)="sortColumn('facilitY_Name')">
                      Facility Name
                      <span class="float-right" tooltip="Sort By Facility Name" triggers="mouseenter mouseleave click">
                        <i *ngIf="sortColumnBy == 'facilitY_Name' && orderByFacilityName == 'desc'"
                          class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                        <i *ngIf="sortColumnBy == 'facilitY_Name' && orderByFacilityName == 'asc'"
                          class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                        <i *ngIf="sortColumnBy != 'facilitY_Name'"
                          class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                      </span>
                    </th>
                    <th class="text-center pb-0 cursor-pointer" (keyup)="sortColumn('physiciaN_NAME')"
                      (click)="sortColumn('physiciaN_NAME')">
                      Physician Name
                      <span class="float-right" tooltip="Sort By Physician Name" triggers="mouseenter mouseleave click">
                        <i *ngIf="sortColumnBy == 'physiciaN_NAME' && orderByPhysician_name == 'desc'"
                          class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                        <i *ngIf="sortColumnBy == 'physiciaN_NAME' && orderByPhysician_name == 'asc'"
                          class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                        <i *ngIf="sortColumnBy != 'physiciaN_NAME'"
                          class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                      </span>
                    </th>
                    <th>Jan {{selectedYear}}</th>
                    <th>Feb {{selectedYear}}</th>
                    <th>Mar {{selectedYear}}</th>
                    <th>Apr {{selectedYear}}</th>
                    <th>May {{selectedYear}}</th>
                    <th>Jun {{selectedYear}}</th>
                    <th>Jul {{selectedYear}}</th>
                    <th>Aug {{selectedYear}}</th>
                    <th>Sep {{selectedYear}}</th>
                    <th>Oct {{selectedYear}}</th>
                    <th>Nov {{selectedYear}}</th>
                    <th>Dec {{selectedYear}}</th>
                  </tr>
                </thead>
                <tbody>
                  <ng-container *ngIf="isFaiclityAccess==true;else ElCase">
                    <tr *ngFor="let item of listOfReports | paginate: { itemsPerPage: 8, currentPage: p}">
                      <td>{{item.facilitY_Name}}</td>
                      <td>{{item.physiciaN_NAME}}</td>
                      <td>
                        <div class='{{item.janColor}}' title="Missing Encounter ">
                          <span *ngIf="item.janColor=='unapproved_count' || item.janColorLink =='M';else janEls">
                            <span class='under-line-click'
                              (keyup)="getMissMatchedEncountersDetails(item,selectedYear,1)"
                              (click)="getMissMatchedEncountersDetails(item,selectedYear,1)">{{item.ajan}}</span>
                          </span>
                          <ng-template #janEls>
                            {{item.ajan}}
                          </ng-template>
                        </div>
                        <div class="created_encounters_count" title="Created Encounters">
                          <span *ngIf="item.cjan !='0';else janEcEls">
                            <span class='under-line-click' (keyup)="getCreatedEncountersDetails(item,selectedYear,1)"
                              (click)="getCreatedEncountersDetails(item,selectedYear,1)">{{item.cjan}}</span>
                          </span>
                          <ng-template #janEcEls>
                            {{item.cjan}}
                          </ng-template>
                        </div>
                        <hr class="hr_table">
                        <div class="billed_count" title="Total Assignments">{{item.jan}}</div>
                        <hr class="hr_table">
                      </td>
                      <td>
                        <div class="{{item.febColor}}" title="Missing Encounter ">
                          <span *ngIf="item.febColor=='unapproved_count' || item.febColorLink =='M';else febEls">
                            <span class='under-line-click'
                              (keyup)="getMissMatchedEncountersDetails(item,selectedYear,2)"
                              (click)="getMissMatchedEncountersDetails(item,selectedYear,2)">{{item.afeb}}</span>
                          </span>
                          <ng-template #febEls>
                            {{item.afeb}}
                          </ng-template>
                        </div>
                        <div class="created_encounters_count" title="Created Encounters">
                          <span *ngIf="item.cfeb !='0';else febEcEls">
                            <span class='under-line-click' (keyup)="getCreatedEncountersDetails(item,selectedYear,2)"
                              (click)="getCreatedEncountersDetails(item,selectedYear,2)">{{item.cfeb}}</span>
                          </span>
                          <ng-template #febEcEls>
                            {{item.cfeb}}
                          </ng-template>
                        </div>
                        <hr class="hr_table">
                        <div class="billed_count" title="Total Assignments">{{item.feb}}</div>
                        <hr class="hr_table">

                      </td>
                      <td>
                        <div class="{{item.marchColor}}" title="Missing Encounter ">
                          <span *ngIf="item.marchColor=='unapproved_count' || item.marchColorLink =='M';else marEls">
                            <span class='under-line-click'
                              (keyup)="getMissMatchedEncountersDetails(item,selectedYear,3)"
                              (click)="getMissMatchedEncountersDetails(item,selectedYear,3)">{{item.amarch}}</span>
                          </span>
                          <ng-template #marEls>
                            {{item.amarch}}
                          </ng-template>
                        </div>
                        <div class="created_encounters_count" title="Created Encounters">
                          <span *ngIf="item.cmarch !='0';else marchEcEls">
                            <span class='under-line-click' (keyup)="getCreatedEncountersDetails(item,selectedYear,3)"
                              (click)="getCreatedEncountersDetails(item,selectedYear,3)">{{item.cmarch}}</span>
                          </span>
                          <ng-template #marchEcEls>
                            {{item.cmarch}}
                          </ng-template>
                        </div>
                        <hr class="hr_table">
                        <div class="billed_count" title="Total Assignments">{{item.march}}</div>
                        <hr class="hr_table">

                      </td>
                      <td>
                        <div class="{{item.aprilColor}}" title="Missing Encounter ">
                          <span *ngIf="item.aprilColor=='unapproved_count' || item.aprilColorLink =='M';else aprilEls">
                            <span class='under-line-click'
                              (keyup)="getMissMatchedEncountersDetails(item,selectedYear,4)"
                              (click)="getMissMatchedEncountersDetails(item,selectedYear,4)">{{item.aapril}}</span>
                          </span>
                          <ng-template #aprilEls>
                            {{item.aapril}}
                          </ng-template>
                        </div>
                        <div class="created_encounters_count" title="Created Encounters">
                          <span *ngIf="item.capril !='0';else aprilEcEls">
                            <span class='under-line-click' (keyup)="getCreatedEncountersDetails(item,selectedYear,4)"
                              (click)="getCreatedEncountersDetails(item,selectedYear,4)">{{item.capril}}</span>
                          </span>
                          <ng-template #aprilEcEls>
                            {{item.capril}}
                          </ng-template>
                        </div>
                        <hr class="hr_table">
                        <div class="billed_count" title="Total Assignments">{{item.april}}</div>
                        <hr class="hr_table">

                      </td>
                      <td>
                        <div class="{{item.mayColor}}" title="Missing Encounter ">
                          <span *ngIf="item.mayColor=='unapproved_count' || item.mayColorLink =='M';else mayEls">
                            <span class='under-line-click'
                              (keyup)="getMissMatchedEncountersDetails(item,selectedYear,5)"
                              (click)="getMissMatchedEncountersDetails(item,selectedYear,5)">{{item.amay}}</span>
                          </span>
                          <ng-template #mayEls>
                            {{item.amay}}
                          </ng-template>
                        </div>
                        <div class="created_encounters_count" title="Created Encounters">
                          <span *ngIf="item.cmay !='0';else mayEcEls">
                            <span class='under-line-click' (keyup)="getCreatedEncountersDetails(item,selectedYear,5)"
                              (click)="getCreatedEncountersDetails(item,selectedYear,5)">{{item.cmay}}</span>
                          </span>
                          <ng-template #mayEcEls>
                            {{item.cmay}}
                          </ng-template>
                        </div>
                        <hr class="hr_table">
                        <div class="billed_count" title="Total Assignments">{{item.may}}</div>
                        <hr class="hr_table">

                      </td>
                      <td>
                        <div class="{{item.juneColor}}" title="Missing Encounter ">
                          <span *ngIf="item.juneColor=='unapproved_count'|| item.juneColorLink =='M';else juneEls">
                            <span class='under-line-click'
                              (keyup)="getMissMatchedEncountersDetails(item,selectedYear,6)"
                              (click)="getMissMatchedEncountersDetails(item,selectedYear,6)">{{item.ajune}}</span>
                          </span>
                          <ng-template #juneEls>
                            {{item.ajune}}
                          </ng-template>
                        </div>
                        <div class="created_encounters_count" title="Created Encounters">
                          <span *ngIf="item.cjune !='0';else juneEcEls">
                            <span class='under-line-click' (keyup)="getCreatedEncountersDetails(item,selectedYear,6)"
                              (click)="getCreatedEncountersDetails(item,selectedYear,6)">{{item.cjune}}</span>
                          </span>
                          <ng-template #juneEcEls>
                            {{item.cjune}}
                          </ng-template>
                        </div>
                        <hr class="hr_table">
                        <div class="billed_count" title="Total Assignments">{{item.june}}</div>
                        <hr class="hr_table">

                      </td>
                      <td>
                        <div class="{{item.julyColor}}" title="Missing Encounter ">
                          <span *ngIf="item.julyColor=='unapproved_count'|| item.julyColorLink =='M';else julyEls">
                            <span class='under-line-click'
                              (keyup)="getMissMatchedEncountersDetails(item,selectedYear,7)"
                              (click)="getMissMatchedEncountersDetails(item,selectedYear,7)">{{item.ajuly}}</span>
                          </span>
                          <ng-template #julyEls>
                            {{item.ajuly}}
                          </ng-template>
                        </div>
                        <div class="created_encounters_count" title="Created Encounters">
                          <span *ngIf="item.cjuly !='0';else julyEcEls">
                            <span class='under-line-click' (keyup)="getCreatedEncountersDetails(item,selectedYear,7)"
                              (click)="getCreatedEncountersDetails(item,selectedYear,7)">{{item.cjuly}}</span>
                          </span>
                          <ng-template #julyEcEls>
                            {{item.cjuly}}
                          </ng-template>
                        </div>
                        <hr class="hr_table">
                        <div class="billed_count" title="Total Assignments">{{item.july}}</div>
                        <hr class="hr_table">

                      </td>
                      <td>
                        <div class="{{item.augColor}}" title="Missing Encounter ">
                          <span *ngIf="item.augColor=='unapproved_count'|| item.augColorLink =='M';else augEls">
                            <span class='under-line-click'
                              (keyup)="getMissMatchedEncountersDetails(item,selectedYear,8)"
                              (click)="getMissMatchedEncountersDetails(item,selectedYear,8)">{{item.aaug}}</span>
                          </span>
                          <ng-template #augEls>
                            {{item.aaug}}
                          </ng-template>
                        </div>
                        <div class="created_encounters_count" title="Created Encounters">
                          <span *ngIf="item.caug !='0';else augEcEls">
                            <span class='under-line-click' (keyup)="getCreatedEncountersDetails(item,selectedYear,8)"
                              (click)="getCreatedEncountersDetails(item,selectedYear,8)">{{item.caug}}</span>
                          </span>
                          <ng-template #augEcEls>
                            {{item.caug}}
                          </ng-template>
                        </div>
                        <hr class="hr_table">
                        <div class="billed_count" title="Total Assignments">{{item.aug}}</div>
                        <hr class="hr_table">

                      </td>
                      <td>
                        <div class="{{item.sepColor}}" title="Missing Encounter ">
                          <span *ngIf="item.sepColor=='unapproved_count' || item.sepColorLink =='M';else sepEls">
                            <span class='under-line-click'
                              (keyup)="getMissMatchedEncountersDetails(item,selectedYear,9)"
                              (click)="getMissMatchedEncountersDetails(item,selectedYear,9)">{{item.asep}}</span>
                          </span>
                          <ng-template #sepEls>
                            {{item.asep}}
                          </ng-template>
                        </div>
                        <div class="created_encounters_count" title="Created Encounters">
                          <span *ngIf="item.csep !='0';else sepEcEls">
                            <span class='under-line-click' (keyup)="getCreatedEncountersDetails(item,selectedYear,9)"
                              (click)="getCreatedEncountersDetails(item,selectedYear,9)">{{item.csep}}</span>
                          </span>
                          <ng-template #sepEcEls>
                            {{item.csep}}
                          </ng-template>
                        </div>
                        <hr class="hr_table">
                        <div class="billed_count" title="Total Assignments">{{item.sep}}</div>
                        <hr class="hr_table">

                      </td>
                      <td>
                        <div class="{{item.octColor}}" title="Missing Encounter ">
                          <span *ngIf="item.octColor=='unapproved_count' || item.octColorLink =='M';else octEls">
                            <span class='under-line-click'
                              (keyup)="getMissMatchedEncountersDetails(item,selectedYear,10)"
                              (click)="getMissMatchedEncountersDetails(item,selectedYear,10)">{{item.aoct}}</span>
                          </span>
                          <ng-template #octEls>
                            {{item.aoct}}
                          </ng-template>
                        </div>
                        <div class="created_encounters_count" title="Created Encounters">
                          <span *ngIf="item.coct !='0';else octEcEls">
                            <span class='under-line-click' (keyup)="getCreatedEncountersDetails(item,selectedYear,10)"
                              (click)="getCreatedEncountersDetails(item,selectedYear,10)">{{item.coct}}</span>
                          </span>
                          <ng-template #octEcEls>
                            {{item.coct}}
                          </ng-template>
                        </div>
                        <hr class="hr_table">
                        <div class="billed_count" title="Total Assignments">{{item.oct}}</div>
                        <hr class="hr_table">

                      </td>
                      <td>
                        <div class="{{item.novColor}}" title="Missing Encounter ">
                          <span *ngIf="item.novColor=='unapproved_count'|| item.novColorLink =='M';else novEls">
                            <span class='under-line-click'
                              (keyup)="getMissMatchedEncountersDetails(item,selectedYear,11)"
                              (click)="getMissMatchedEncountersDetails(item,selectedYear,11)">{{item.anov}}</span>
                          </span>
                          <ng-template #novEls>
                            {{item.anov}}
                          </ng-template>
                        </div>
                        <div class="created_encounters_count" title="Created Encounters">
                          <span *ngIf="item.cnov !='0';else novEcEls">
                            <span class='under-line-click' (keyup)="getCreatedEncountersDetails(item,selectedYear,11)"
                              (click)="getCreatedEncountersDetails(item,selectedYear,11)">{{item.cnov}}</span>
                          </span>
                          <ng-template #novEcEls>
                            {{item.cnov}}
                          </ng-template>
                        </div>
                        <hr class="hr_table">
                        <div class="billed_count" title="Total Assignments">{{item.nov}}</div>
                        <hr class="hr_table">

                      </td>
                      <td>
                        <div class="{{item.decColor}}" title="Missing Encounter ">
                          <span *ngIf="item.decColor=='unapproved_count' || item.decColorLink =='M';else decEls">
                            <span class='under-line-click'
                              (keyup)="getMissMatchedEncountersDetails(item,selectedYear,12)"
                              (click)="getMissMatchedEncountersDetails(item,selectedYear,12)">{{item.adec}}</span>
                          </span>
                          <ng-template #decEls>
                            {{item.adec}}
                          </ng-template>
                        </div>
                        <div class="created_encounters_count" title="Created Encounters">
                          <span *ngIf="item.cdec !='0';else decEcEls">
                            <span class='under-line-click' (keyup)="getCreatedEncountersDetails(item,selectedYear,12)"
                              (click)="getCreatedEncountersDetails(item,selectedYear,12)">{{item.cdec}}</span>
                          </span>
                          <ng-template #decEcEls>
                            {{item.cdec}}
                          </ng-template>
                        </div>
                        <hr class="hr_table">
                        <div class="billed_count" title="Total Assignments">{{item.dec}}</div>
                        <hr class="hr_table">

                      </td>
                    </tr>
                    <tr *ngIf="!listOfReports.length">
                      <td colSpan="14" class="text-center">
                        <span>No Results Found</span>

                      </td>
                    </tr>
                  </ng-container>
                  <ng-template #ElCase>
                    <tr>
                      <td class="text-center" colspan="16">Unable to Access this Facility</td>
                    </tr>
                  </ng-template>
                </tbody>
                <tfoot>
                  <tr>
                    <td colspan="16" class="m-0 p-0" style="background: white !important;">
                      <pagination-controls previousLabel="" nextLabel=""
                        (pageChange)="p = $event"></pagination-controls>
                    </td>
                  </tr>
              </table>

            </div>
          </div>

        </div>

      </div>
    </div>
    <!-- Area Chart Ends -->
  </div>
</div>
<!-- Begin Page Content Ends -->


<!-- Mismatched Kareo Encounters popup starts -->
<div class="modal fade" id="mdlMissEnvs" tabindex="-1" aria-labelledby="MissKEnvsLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg max-width-80">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="MissKEnvsLabel">{{popupTitle}} (<b>{{listOfMissMatchedEncounters.length}}</b>)

        </h5>
        <ng-container (keyup)="modelClose()" (click)="modelClose()">
          <i class="fa fa-times closePopup" data-dismiss="modal"></i>
        </ng-container>
      </div>

      <div class="modal-body" [formGroup]="DetailFilterForm">
        <div class="row my-1 mx-auto align-items-center">
          <div class="col-12 col-md-3 px-1 py-1">
            <select id="ddlAccountNumber" class="form-control custom-control" formControlName="ddlAccountNumber">
              <option value="">---Select Account Number---</option>
              <option value="All">All</option>
              <option [value]="s" *ngFor="let s of listOfAccountNumbers">
                {{s}}
              </option>
            </select>

          </div>
          <div class="col-12 col-md-3 px-1 py-1">
            <select id="ddlAssignmentBy" class="form-control custom-control" formControlName="ddlAssignmentBy">
              <option value="">---Select Assignment By---</option>
              <option value="All">All</option>
              <option [value]="s" *ngFor="let s of listOfAssignmentBy">
                {{s}}
              </option>
            </select>

          </div>
          <div class="col-12 col-md-2 px-1 py-1">
            <input id="AssignmentDate" type="date" [min]="startDate" [max]="endDate" class="form-control"
              formControlName="AssignmentDate" title="Assignment Date">

          </div>
          <div class="col-12 col-md-2 px-1 py-1">
            <button class="btn btn-outline-info px-2 btn-block" type="submit" (click)="filterReportDetails()">
              <i class="fa-filter fas font-size-13"></i>
              Filter
            </button>
          </div>

        </div>
        <div class="row my-1 mx-auto align-items-center">
          <div class="col-12 px-1 py-1 col-md-12">
            <div class="input-group">
              <input type="text" class="form-control small" maxlength="1000"
                placeholder="Search for Account Number, Assignment Date And Assignment By etc." aria-label="Search"
                aria-describedby="basic-addon2" [(ngModel)]="searchMissMatchedByName"
                [ngModelOptions]="{standalone: true}" (keyup)="searchMissMatched(searchMissMatchedByName)">
              <div class="input-group-append">
                <button class="bg-gradient-light btn text-dark" type="button"> <i class="fas fa-search fa-sm"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="row mx-auto my-3">
          <div class="col-12 col-md-12 px-md-1 py-1">
            <table class="table table-striped table-bordered table-hover billed_encounters_grid">
              <thead>
                <tr class="text-center">
                  <th class="text-center pb-0 cursor-pointer" (keyup)="sortColumnMisMatchList('account_Number')"
                    (click)="sortColumnMisMatchList('account_Number')">
                    Account Number
                    <span class="float-right" tooltip="Sort By Physician Name" triggers="mouseenter mouseleave click">
                      <i *ngIf="sortMMColumnBy == 'account_Number' && orderByAccountNumber == 'desc'"
                        class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                      <i *ngIf="sortMMColumnBy == 'account_Number' && orderByAccountNumber == 'asc'"
                        class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                      <i *ngIf="sortMMColumnBy != 'account_Number'"
                        class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                    </span>
                  </th>
                  <th class="text-center pb-0 cursor-pointer" (keyup)="sortColumnMisMatchList('patient_Name')"
                    (click)="sortColumnMisMatchList('patient_Name')">
                    Patient Name
                    <span class="float-right" tooltip="Sort By Physician Name" triggers="mouseenter mouseleave click">
                      <i *ngIf="sortMMColumnBy == 'patient_Name' && orderByFacilityName == 'desc'"
                        class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                      <i *ngIf="sortMMColumnBy == 'patient_Name' && orderByFacilityName == 'asc'"
                        class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                      <i *ngIf="sortMMColumnBy != 'patient_Name'"
                        class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                    </span>
                  </th>
                  <th>Facility Name</th>
                  <th>Physician Name</th>
                  <th class="text-center pb-0 cursor-pointer" (keyup)="sortColumnMisMatchList('assignmentDate')"
                    (click)="sortColumnMisMatchList('assignmentDate')">
                    Assignment Date
                    <span class="float-right" tooltip="Sort By Physician Name" triggers="mouseenter mouseleave click">
                      <i *ngIf="sortMMColumnBy == 'assignmentDate' && orderByAssignmentDate == 'desc'"
                        class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                      <i *ngIf="sortMMColumnBy == 'assignmentDate' && orderByAssignmentDate == 'asc'"
                        class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                      <i *ngIf="sortMMColumnBy != 'assignmentDate'"
                        class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                    </span>
                  </th>
                  <th class="text-center pb-0 cursor-pointer" (keyup)="sortColumnMisMatchList('assignmentBy')"
                    (click)="sortColumnMisMatchList('assignmentBy')">
                    Assignment By
                    <span class="float-right" tooltip="Sort By Physician Name" triggers="mouseenter mouseleave click">
                      <i *ngIf="sortMMColumnBy == 'assignmentBy' && orderByAssignmentBy == 'desc'"
                        class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                      <i *ngIf="sortMMColumnBy == 'assignmentBy' && orderByAssignmentBy == 'asc'"
                        class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                      <i *ngIf="sortMMColumnBy != 'assignmentBy'"
                        class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                    </span>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let item of listOfMissMatchedEncounters">

                  <td class="p-2">{{item.account_Number}}</td>
                  <td class="p-2">{{item.patient_Name}}</td>
                  <td class="p-2">{{item.facility_Name}}</td>
                  <td class="p-2">{{item.physicianName}}</td>
                  <td class="p-2">{{item.assignmentDate | date: 'MM/dd/yyyy' }}</td>
                  <td class="p-2">{{item.assignmentBy}}</td>


                  <ng-template #ddlReasonEls>
                    <td class="p-2 width-13per">
                      <span>{{item.reasonCode}}</span>
                      <span *ngIf="item.reasonCode== 'Already Billed'"><br />
                        <span class="text-primary">{{item.encounterIDEnteredByUser}}</span>
                      </span>
                    </td>
                    <td class="p-2">
                      <span>Already Submiited</span>
                    </td>
                  </ng-template>

                </tr>
              </tbody>
            </table>

          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Mismatched Encounters popup ends -->