import { Component, OnInit, Input } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { ToastrService } from 'ngx-toastr';
import { DatePipe } from '@angular/common';
import { MtxDatetimepickerType, MtxDatetimepickerMode, MtxCalendarView } from '@ng-matero/extensions/datetimepicker';
declare let $:any;

@Component({
  selector: 'app-add-missing-encounter',
  templateUrl: './add-missing-encounter.component.html',
  styles: [
  ]
})
export class AddMissingEncounterComponent implements OnInit {
  type: MtxDatetimepickerType = 'datetime';
  mode: MtxDatetimepickerMode = 'portrait';
  startView: MtxCalendarView = 'month';
  multiYearSelector = false;
  touchUi = false;
  twelvehour = false;
  timeInterval = 1;
  timeInput = true;
  @Input() lisfOfMissingEncounters:Array<any>=[];
  @Input() PatientObject:any={};
  @Input() listOfPhysicians:Array<any>=[];
  public loading: boolean = false;
  public submitted:boolean=false;
  public request:any={};
  public vali2:boolean=false;
  public vali3:boolean=false;
  public admissionDate:Date;
  public missingEncForm:FormGroup;
  public currentID:string ="";
  public maxDateTime:Date=new Date();
  constructor(private readonly commonServ:CommonService,private readonly fb: FormBuilder,private readonly encrDecr: EncrDecrServiceService,private readonly toastr: ToastrService,public datepipe: DatePipe) { }

  ngOnInit() {
    this.missingEncForm = this.fb.group({
      ddlPhysicianMiss: ['', Validators.required],
      envSeenDate: ['', Validators.required]
    });
    this.missingEncForm.get('envSeenDate')?.setValue('');
    this.missingEncForm.get('ddlPhysicianMiss')?.setValue('');
  }
  get f() { return this.missingEncForm.controls; };

  insertMissedEvn(pObj) {
    this.submitted=true;
    let today = new Date();
    this.vali2 = false;
    this.vali3 = false;
    this.admissionDate = new Date(pObj.admission_Date);
    this.request.EncounterSeenDate = this.encrDecr.set(this.datepipe.transform(this.missingEncForm.value.envSeenDate, 'MM/dd/yyyy hh:mm:ss a'));
    if (this.missingEncForm.invalid) {
      return;
    }
    else if (new Date(this.missingEncForm.value.envSeenDate) < (new Date(this.admissionDate.setHours(-72)))) {
      this.vali2 = true;
      return;
    }
    else if (new Date(this.missingEncForm.value.envSeenDate.setHours(0,0,0,0)) > new Date(today.setHours(0,0,0,0))) {
      this.vali3 = true;
      return;
    }
    
      this.commonServ.startLoading();
      this.request.FACTIMEZONE = this.encrDecr.set(pObj.factimezone);
      this.request.AccountNumber = this.encrDecr.set(pObj.account_Number);
      this.request.PatientName = this.encrDecr.set(pObj.patient_Name);
      this.request.sAdmissionDate = this.encrDecr.set(pObj.admission_Date);
      this.request.FacilityName = this.encrDecr.set(pObj.facility_Name);
      this.request.PhysicianMailid = this.encrDecr.set(this.missingEncForm.value.ddlPhysicianMiss);
      this.request.Patient_MRN = pObj.mrn != null ? this.encrDecr.set(pObj.mrn):this.encrDecr.set("");
      this.request.sDOB = pObj.dob != null ? this.encrDecr.set(pObj.dob):this.encrDecr.set("");
      this.request.Sex = pObj.sex != null ? this.encrDecr.set(pObj.sex):this.encrDecr.set("");
      this.request.Room_Number = pObj.room_Number != null?this.encrDecr.set(pObj.room_Number):this.encrDecr.set("");
      this.request.Patient_Class = pObj.patient_Class != null ? this.encrDecr.set(pObj.patient_Class):this.encrDecr.set("");
      this.request.BED_NUMBER = pObj.bed_Number != null ? this.encrDecr.set(pObj.bed_Number):this.encrDecr.set("");
      this.commonServ.insertMissingEncounter(this.request).subscribe((p: any) => {
        this.request={};
        this.submitted=false;
        if (p == 1) {
          this.bindPatientEncounterDetails(pObj);         
          this.toastr.success("Missing Encounter sumbitted successfully",'',{timeOut: 9000});
        }
        else if (p == 0) {
          this.toastr.error("This Missing Encounter already submitted, Please see below table",'',{timeOut: 9000});
        }
        else if (p == -2) {
          this.toastr.error("Encounter Seen Date  Can't Be a Future Date",'',{timeOut: 9000});
        }
        else if (p == -3) {
          this.toastr.error("Encounter Seen Date  Can't Be Less Than  Admission Date",'',{timeOut: 9000});
        }
        else {
          this.toastr.error('This Encounter Already Created ' + this.missingEncForm.value.ddlPhysicianMiss + " on " + this.missingEncForm.value.envSeenDate,'',{timeOut: 9000});
        }
        this.missingEncForm.get('envSeenDate')?.setValue('');
        this.missingEncForm.get('ddlPhysicianMiss')?.setValue('');
        this.commonServ.stopLoading();
      }, error => { 
        this.request={};
        this.commonServ.stopLoading();
        console.error(error.status); 
      });
  }

  openDeleteConfirmPopup(item)
  {
    if (confirm('Do you want to contine to Delete this Missing Encounter?')) 
    {
     
      this.commonServ.startLoading();
    this.request.sID = this.encrDecr.set(item.id);
    this.commonServ.DeleteMissingEncounter(this.request).subscribe((p: any) => {
      this.request={};
     this.GetPatientEncounterDetails(item);
      this.commonServ.stopLoading();
    },error => {
      this.request={};
      this.commonServ.stopLoading();
      console.error(error.status);
    });
    }
  }  

  GetPatientEncounterDetails(item)
  {
   
    this.commonServ.startLoading();
    this.request.AccountNumber=this.encrDecr.set(item.accountNumber);
    this.request.FacilityName=this.encrDecr.set(item.facilityName);
    this.commonServ.getMissingEncounters(this.request).subscribe((p: any) => {
      this.lisfOfMissingEncounters = p;
    },error => {
      this.request={};
      this.commonServ.stopLoading();
      console.error(error.status);
    });
  }
  
  bindPatientEncounterDetails(item)
  {
    this.commonServ.startLoading();
    this.request.AccountNumber=this.encrDecr.set(item.account_Number);
    this.request.FacilityName=this.encrDecr.set(item.facility_Name);
    this.commonServ.getMissingEncounters(this.request).subscribe((p: any) => {
      this.lisfOfMissingEncounters = p;
    },error => {
      this.request={};
      this.commonServ.stopLoading();
      console.error(error.status);
    });
  }

}
