<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new p-2" style="background: #0169ab;" [formGroup]="FilterForm">
                    <div class="align-items-center row mx-auto">
                        <div class="col-12 px-0">

                            <div class="row mt-1 mx-auto">
                                <div class="col-12 px-1 py-1"
                                    [ngClass]="{'col-md-3': this.listOfReports.length, 'col-md-4': !this.listOfReports.length}">
                                    <select id="ddlFacility" class="form-control" formControlName="ddlFacility"
                                        (change)="getPhysiciansByFacility($any($event.target).value)"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                                        <option value="">---Select Facility---</option>
                                        <option value="All">All Facilities</option>
                                        <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                                            <span>{{s.facilityName}}</span>
                                        </option>
                                    </select>
                                    <div *ngIf="submitted && f['ddlFacility'].errors" class="invalid-feedback">
                                        <div *ngIf="f['ddlFacility'].errors['required']">Facility is required</div>
                                    </div>
                                </div>
                                <div class="col-12 px-1 py-1"
                                    [ngClass]="{'col-md-3': this.listOfReports.length, 'col-md-4': !this.listOfReports.length}">
                                    <select id="ddlPhysician" class="form-control custom-control"
                                        formControlName="ddlPhysician"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlPhysician'].errors}">
                                        <option value="">---Select Physician---</option>
                                        <option value="All">All Physicians</option>
                                        <option [value]="s.physicianEmaiId" *ngFor="let s of listOfPhysicians">
                                            {{s.physicianName}}
                                        </option>
                                    </select>
                                    <div *ngIf="submitted && f['ddlPhysician'].errors" class="invalid-feedback">
                                        <div *ngIf="f['ddlPhysician'].errors['required']">Physician is required</div>
                                    </div>
                                </div>
                                <div class="col-12 px-1 py-1"
                                    [ngClass]="{'col-md-3': this.listOfReports.length, 'col-md-2': !this.listOfReports.length}">
                                    <select id="ddlYear" class="form-control" formControlName="ddlYear">
                                        <option [value]="year" *ngFor="let year of listOfYears">
                                            <span>{{year}}</span>
                                        </option>
                                    </select>
                                    <div *ngIf="submitted && f['ddlYear'].errors" class="invalid-feedback">
                                        <div *ngIf="f['ddlYear'].errors['required']">Year is required</div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-2 px-1 py-1">
                                    <button class="btn btn-outline-info px-2 btn-block" type="submit"
                                        (click)="filterReport()">
                                        <i class="fa-filter fas font-size-13"></i>
                                        Filter
                                    </button>
                                </div>
                                <div *ngIf="this.listOfReports.length>0" class="col-12 col-md-1 px-1 py-1 text-center">
                                    <img alt=' ' (keyup)="exportAsXLSX()" (click)="exportAsXLSX()" class="ml-2 shadow"
                                        [src]="img1" style="width:40px;cursor:pointer;" title="Export Excel">
                                </div>
                            </div>
                            <div class="row my-1 mx-auto">
                                <div class="col-12 px-1 py-1"
                                    [ngClass]="{'col-md-11': this.listOfReports.length, 'col-md-12': !this.listOfReports.length}">
                                    <div class="input-group">
                                        <input type="text" class="form-control small" maxlength="1000"
                                            placeholder="Search for..." aria-label="Search"
                                            aria-describedby="basic-addon2" [(ngModel)]="searchByName"
                                            [ngModelOptions]="{standalone: true}">
                                        <div class="input-group-append">
                                            <button class="bg-gradient-light btn text-dark" type="button">
                                                <i class="fas fa-search fa-sm"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Card Body -->
                <div class="card-body px-2 py-1">
                    <div class="row mx-0">
                        <div class="col-12 px-0">
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered table-hover my-auto">
                                    <tr class="text-center">
                                        <th>CPT Code</th>
                                        <th>Physician Name</th>
                                        <th>Facility Name</th>
                                        <th class="width-6per">Code Type</th>
                                        <th>Jan {{selectedYear}}</th>
                                        <th>Feb {{selectedYear}}</th>
                                        <th>Mar {{selectedYear}}</th>
                                        <th>Apr {{selectedYear}}</th>
                                        <th>May {{selectedYear}}</th>
                                        <th>Jun {{selectedYear}}</th>
                                        <th>Jul {{selectedYear}}</th>
                                        <th>Aug {{selectedYear}}</th>
                                        <th>Sep {{selectedYear}}</th>
                                        <th>Oct {{selectedYear}}</th>
                                        <th>Nov {{selectedYear}}</th>
                                        <th>Dec {{selectedYear}}</th>
                                    </tr>
                                    <ng-container *ngIf="isFaiclityAccess==true;else ElCase">
                                        <tr
                                            *ngFor="let item of  listOfReports |gridFilter:{cpt_code:searchByName,facilityName:searchByName,physician_name:searchByName,code_type:searchByName,jan:searchByName,feb:searchByName,march:searchByName,april:searchByName,may:searchByName,june:searchByName,july:searchByName,aug:searchByName,sep:searchByName,oct:searchByName,nov:searchByName,dec:searchByName}:false| paginate: { itemsPerPage: 17, currentPage: p}">
                                            <td>{{item.cpt_code}}</td>
                                            <td>{{item.physician_name}}</td>
                                            <td>{{item.facilityName}}</td>
                                            <td>{{item.code_type}}</td>
                                            <td>{{item.jan}}</td>
                                            <td>{{item.feb}}</td>
                                            <td>{{item.march}}</td>
                                            <td>{{item.april}}</td>
                                            <td>{{item.may}}</td>
                                            <td>{{item.june}}</td>
                                            <td>{{item.july}}</td>
                                            <td>{{item.aug}}</td>
                                            <td>{{item.sep}}</td>
                                            <td>{{item.oct}}</td>
                                            <td>{{item.nov}}</td>
                                            <td>{{item.dec}}</td>
                                        </tr>
                                    </ng-container>
                                    <ng-template #ElCase>
                                        <tr>
                                            <td class="text-center" colspan="16">Unable to Access this Facility</td>
                                        </tr>
                                    </ng-template>
                                    <tr>
                                        <td colspan="16" class="m-0 p-0" style="background: white !important;">
                                            <pagination-controls previousLabel="" nextLabel=""
                                                (pageChange)="p = $event"></pagination-controls>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                </div>

            </div>
        </div>
        <!-- Area Chart Ends -->
    </div>
</div>
<!-- Begin Page Content Ends -->