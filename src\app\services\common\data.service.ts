import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable()
export class DataService {

    private readonly messageSource = new BehaviorSubject('');
    userDetails = this.messageSource.asObservable();

    private readonly secretKey = new BehaviorSubject('');
    accessKey = this.secretKey.asObservable();

    private readonly loggedIn = new BehaviorSubject(false);
    isLoggedIn = this.loggedIn.asObservable();

    private readonly updateURL = new BehaviorSubject('');
    currentUpateURL = this.updateURL.asObservable();

    private readonly updateUserAccess = new BehaviorSubject({});
    isUserAccess = this.updateUserAccess.asObservable();

    constructor() { }

    updateUser(user: string) {
        this.messageSource.next(user);
    }
  
    updateAcessKey(key) {
        this.secretKey.next(key);
    }
    updateLogginStatus(status) {
        this.loggedIn.next(status);
    }

    updatenewURL(url) {
        this.updateURL.next(url);
    }

    updateAccesssForUser(access) {
        this.updateUserAccess.next(access);
    }
}