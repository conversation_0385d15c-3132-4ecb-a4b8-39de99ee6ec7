import { Injectable } from '@angular/core';
import { AppConfiguration } from './app-configuration';
import { HttpClient, HttpHeaders, HttpBackend } from '@angular/common/http';
import { apiUrl } from '../config';
import { mobiscroll } from '@mobiscroll/angular-lite';

@Injectable({
  providedIn: 'root'
})
export class JsonAppConfigService extends AppConfiguration {
  public http: HttpClient;
  public arrayOfCalls: Array<string> = ['getCPT', 'getSummary', 'getAlerts', 'getCounters', 'getDistance', 'getMeters'];
  public authService: any;
  loggedIn = false;
  loaded = false;
  errorCode = '';
  currentUrl = '';
  asyncLocalStorage = {
    setItem(key, value) {
      return Promise.resolve().then(() => {
        sessionStorage.setItem(key, value);
      });
    },
    getItem(key) {
      return Promise.resolve().then(() => {
        return sessionStorage.getItem(key);
      });
    }
  };
  constructor(handler: HttpBackend) {
    super();
    this.http = new HttpClient(handler);
    this.currentUrl = window.location.href;
  }

  // This function needs to return a promise
  load() {
    const token: string = sessionStorage.getItem('adal.idtoken')!;
    let headers = new HttpHeaders({ 'Authorization': 'Bearer QYdopFTOtmie/owWTVGCaY4UBegYLUEgGCvBbTrsFlubW7LO/0X+64NNCbQcSBEPP3HcQLXeJnBc37Uq5mmv/GQo6vAnUvqSBNZpkK5LUU9cnwGUxWz5QGo6Dhe' + token, 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.get<any>(apiUrl + 'api/Common/' + this.arrayOfCalls[this.generateRandamNo()], { headers: headers })
      .toPromise()
      .then(data => {
        this.data1 = data.a.substring(1, 6) + data.b.substring(2, 7) + data.c.substring(3, 8) + data.d.substring(4, 9) + data.e.substring(5, 10) + data.f.substring(6, 13);
        this.data2 = data.g.substring(7, 12) + data.h.substring(8, 13) + data.i.substring(9, 15);
      })
      .catch(() => {
        console.error('Could not load configuration');
      });
  }

  checkCookie() {
    let cookieEnabled = navigator.cookieEnabled;
    if (!cookieEnabled) {
      document.cookie = 'testcookie';
      cookieEnabled = document.cookie.indexOf('testcookie') != -1;
    }
    return cookieEnabled || this.showCookieFail();
  }
  showCookieFail() {
    // do something here
    mobiscroll.alert({
      title: 'Cookies Disabled',
      message: 'This can happen if the cookies have been disabled in your browser. Please Re-enable the cookies in your browser and try to login again.',
    });
  }
  async getKeys(token) {
    this.loaded = true;
    const headers = new HttpHeaders({ Authorization: 'Bearer QYdopFTOtmie/owWTVGCaY4UBegYLUEgGCvBbTrsFlubW7LO/0X+64NNCbQcSBEPP3HcQLXeJnBc37Uq5mmv/GQo6vAnUvqSBNZpkK5LUU9cnwGUxWz5QGo6Dhe' + token, 'Content-Type': 'application/json', Accept: 'application/json' });
    return new Promise<void>((resolve, reject) => {
      this.http.get<any>(apiUrl + 'api/Common/' + this.arrayOfCalls[this.generateRandamNo()], { headers })
        .toPromise().then((data: any) => {
          this.data1 = data.a.substring(1, 6) + data.b.substring(2, 7) + data.c.substring(3, 8) + data.d.substring(4, 9) + data.e.substring(5, 10) + data.f.substring(6, 13);
          this.data2 = data.g.substring(7, 12) + data.h.substring(8, 13) + data.i.substring(9, 15);
          console.log('getKeys Resolve');
          resolve();
        }).catch((response: any) => {
          console.log('getKeys Reject', response);
        });
    });
  }
  getUser(token) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json', 'Authorization': 'Bearer ' + token });
    const GRAPH_ENDPOINT = 'https://graph.microsoft.com/v1.0/me'; // without header
    return this.http.get(GRAPH_ENDPOINT, { headers: headers });
  }
  insertDataRequestForm(request) {
    this.loaded = true;
    return new Promise<void>((resolve, reject) => {
      this.http.post<any>(apiUrl + 'api/Common/InsertDataRequestForm', request)
        .toPromise().then((data: any) => {
          this.loaded = false;
        }).catch((response: any) => {
          console.log('Exception while saving data request', response);
        });
    });
  }
  generateRandamNo(): number {
    const array = new Uint32Array(10);
    let randamValue: any = crypto.getRandomValues(array);
    randamValue = Math.floor(((randamValue[0] / 4294967295) * 6) + 0);
    return randamValue;
  }
}

