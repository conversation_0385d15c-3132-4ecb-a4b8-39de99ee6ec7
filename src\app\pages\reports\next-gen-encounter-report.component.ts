import { Component, OnInit } from '@angular/core';
import { CommonService } from 'src/app/services/common/common.service';
import { ReportsService } from 'src/app/services/reports/reports.service';
import { FormGroup, FormBuilder, Validators, AbstractControl } from '@angular/forms';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { AppComponent } from 'src/app/app.component';
import { ExcelServices } from 'src/app/services/common/ExcelService';
import { ToastrService } from 'ngx-toastr';
import { mobiscroll } from '@mobiscroll/angular-lite';
declare let $: any;

@Component({
  selector: 'app-next-gen-encounter-report',
  templateUrl: './next-gen-encounter-report.component.html',
  styles: []
})
export class NextGenEncounterReportComponent implements OnInit {
  public listOfFacilities: Array<any> = [];
  public listOfPhysicians: Array<any> = [];
  public listOfReports: Array<any> = [];
  public listOfReportsInitial: Array<any> = [];
  public FilterForm: FormGroup;
  public request: any = {};
  public submitted: boolean = false;
  public p: number;
  public searchByName: string;
  public selectedYear: string;
  public lastRefreshDate: string;
  public isFaiclityAccess: boolean = true;
  public listOfYears: Array<any> = [];
  public selectedResonArray: Array<string> = [];
  public selectedRequest: any = {};
  public userAccess: any = {};
  public listOfMissMatchedEncounters: Array<any> = [];
  public listOfEcountinKareo: Array<any> = [];
  orderBy = 'desc';
  orderByFacilityName = 'desc';
  orderByPhysician_name = 'desc';
  sortColumnBy = 'physician_name';
  public timeout: any = null;
  public encounterIDbyUser: any;
  public detailExportFileName: string = "";
  public exportFileName: string = "";
  public missmatchedItem: any = {};
  public month: number;
  public type: string;
  public monthsList: Array<any> = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dev'];
  public mDdlGroupsSettings: any = {};
  public listOfGroups: Array<any> = [];
  public selectedListOfGroups: Array<any> = [];
  public mDdlPhysicianSettings: any = {};
  public selectedListOfPhysicians: Array<any> = [];
  public ddlPatientType: string = '';
  public img1: string = '.././../../assets/img/excel.png';

  constructor(private readonly reportsServ: ReportsService, private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService,
    private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly excelService: ExcelServices, private readonly toastr: ToastrService) { }

  ngOnInit() {
    this.getYears();
    let currentYear = new Date().getFullYear();
    this.appComp.loadPageName('Nextgen Encounters Report', 'reportsTab');
    this.selectedYear = `${currentYear}`;
    this.getFacilities();
    this.FilterForm = this.fb.group({
      ddlFacility: ['All', Validators.required],
      ddlPatientType: ['All', Validators.required],
      ddlGroups: ['', Validators.required],
      ddlPhysician: ['', Validators.required],
      ddlYear: [`${currentYear}`]
    });
    this.userAccess = this.appComp.userAccess;
    this.mDdlGroupsSettings = {
      singleSelection: false,
      idField: 'group_id',
      textField: 'group_name',
      enableCheckAll: true,
      itemsShowLimit: 1,
      allowSearchFilter: true,
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
    };
    this.mDdlPhysicianSettings = {
      singleSelection: false,
      idField: 'physicianName',
      textField: 'physicianName',
      enableCheckAll: true,
      itemsShowLimit: 1,
      allowSearchFilter: true,
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
    };
  }
  getYears() {
    let max = new Date().getFullYear(),
      min = max - 10;
    let years: any[] = [];
    for (let i = min; i <= max; i++) {

      years.push(i);
    }
    this.listOfYears = years;
  }
  get f(): { [key: string]: AbstractControl } {
    return this.FilterForm.controls;
  }

  getFacilities() {
    this.commonServ.getFacilitiesForNG().subscribe((p: any) => {
      this.listOfFacilities = p;
      if (this.listOfFacilities) {
        this.FilterForm.get('ddlFacility')?.setValue(this.listOfFacilities[0].facilityName);
        this.request.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
        this.request.PhysicanName = this.encrDecr.set('All');
        this.request.sYearvalue = this.encrDecr.set(this.FilterForm.value.ddlYear);
        this.request.DisplayType = this.encrDecr.set('View');
        this.request.PatientType = this.encrDecr.set(this.FilterForm.value.ddlPatientType);
        this.request.GroupIds = this.encrDecr.set('All');
        this.getReportBySelection(this.request);
        this.getGroupsByFacility(this.listOfFacilities[0].facilityName, false);
      }
    }, error => { console.error(error.status); });
  }

  exportAsXLSX(): void {
    mobiscroll.confirm({
      title: 'Confirm',
      message: `Do you want download?`,
      okText: 'Yes',
      cancelText: 'Cancel',
      callback: (res) => {
        if (res) {
          let fileName = "";
          fileName = "NEXTGEN_REPORT_" + this.exportFileName;
          this.excelService.exportAsExcelFile(this.listOfReports, fileName);
        }
      }
    });
  }

  filterReport() {
    this.submitted = true;
    if (this.FilterForm.invalid) {
      return;
    }
    this.p = 1;
    this.request.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    let physyn_names: string = '';
    if (this.selectedListOfPhysicians.length == this.listOfPhysicians.length) {
      physyn_names = 'All';
    }
    else {
      this.selectedListOfPhysicians.forEach((phy: any) => {
        physyn_names = physyn_names + phy.physicianName + ',';
      });
    }
    this.request.PhysicanName = this.encrDecr.set(physyn_names);
    this.request.sYearvalue = this.encrDecr.set(this.FilterForm.value.ddlYear);
    this.request.DisplayType = this.encrDecr.set('View');
    this.request.PatientType = this.encrDecr.set(this.FilterForm.value.ddlPatientType);
    let group_ids: string = '';
    if (this.selectedListOfGroups.length == this.listOfGroups.length) {
      group_ids = 'All';
    }
    else {
      this.selectedListOfGroups.forEach((grp: any) => {
        group_ids = group_ids + grp.group_id + ',';
      });
    }
    this.request.GroupIds = this.encrDecr.set(group_ids);
    this.getReportBySelection(this.request);
  }

  getReportBySelection(request) {
    this.commonServ.startLoading();
    this.selectedYear = this.FilterForm.value.ddlYear;
    this.reportsServ.getNextGenStatusReportData(request).subscribe((p: any) => {
      if (p.listofData.length > 0) {
        this.listOfReports = p.listofData;
        this.listOfReportsInitial = p.listofData;
      }
      else {
        this.listOfReports = [];
      }
      this.ddlPatientType = request.PatientType;
      this.isFaiclityAccess = p.flag;
      this.request = {};
      this.exportFileName = this.FilterForm.value.ddlFacility + '_' + $('#ddlPhysician option:selected').text() + '_' + $('#ddlYear option:selected').text();
      this.commonServ.stopLoading();
    }, error => {
      this.commonServ.stopLoading();

    });
  }

  updateReasonCodeKareoEncounter(item) {
    if (this.selectedResonArray[item.encounterId] && this.encounterIDbyUser) {
      this.commonServ.startLoading();
      let request = {
        AccountNumber: this.encrDecr.set(item.accountNumber),
        PatientName: this.encrDecr.set(item.patientName),
        FacilityName: this.encrDecr.set(item.facilityName),
        PhysicianName: this.encrDecr.set(item.physicianName),
        strKareoPatientID: this.encrDecr.set(item.kareoPatientID),
        strEncounterId: this.encrDecr.set(item.encounterId),
        strEncounterCreatedDate: this.encrDecr.set(item.encounterCreatedDate),
        ReasonCode: this.encrDecr.set(this.selectedResonArray[item.encounterId]),
        CPTCode: this.encrDecr.set(item.cptCode),
        strKareoEncounterID: this.encrDecr.set(item.kareoEncounterID),
        strEncounterSeenDate: this.encrDecr.set(item.encounterSeenDate),
        strAdmissionDate: this.encrDecr.set(item.admissionDate),
        EncounterIDEnteredByUser: this.encrDecr.set(this.encounterIDbyUser)
      }
      this.reportsServ.updateReasonCodeKareoEncounter(request).subscribe((p: any) => {
        this.request.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
        this.request.PhysicanName = this.encrDecr.set($('#ddlPhysician option:selected').text().trim());
        this.request.sYearvalue = this.encrDecr.set(this.FilterForm.value.ddlYear);
        this.request.DisplayType = this.encrDecr.set('View');
        this.request.CPTValue = this.encrDecr.set('');
        this.encounterIDbyUser = 0;
        this.getReportBySelection(this.request);
        this.reportsServ.getMissMatchedEncountersByKareo(this.selectedRequest).subscribe((p: any) => {
          this.listOfMissMatchedEncounters = p;
          this.commonServ.stopLoading();
        }, error => {
          this.commonServ.stopLoading();
        });
      }, error => {
        this.commonServ.stopLoading();
      });
    }
    else if (!this.selectedResonArray[item.encounterId]) {
      this.toastr.error('Please select Reson to submit.', '', { timeOut: 1900 });
    }
    else if (!this.encounterIDbyUser) {
      this.toastr.error('Please Enter the Encounter ID.', '', { timeOut: 1900 });
    }
    else {
      this.toastr.error('Something went wrong!', '', { timeOut: 1900 });
    }
  }

  getMissMatchedEncounters(item, year, month, type) {
    this.commonServ.startLoading();
    let request = {
      strYear: this.encrDecr.set(year),
      strMonth: this.encrDecr.set(month),
      Facility: this.encrDecr.set(item.facility),
      Physician: this.encrDecr.set(item.physician),
      Type: this.encrDecr.set(type),
      PatientType: this.ddlPatientType
    }
    this.missmatchedItem = item;
    this.month = month;
    this.type = type;
    this.reportsServ.getMissMatchedEncountersByNextgen(request).subscribe((p: any) => {
      this.commonServ.stopLoading();
      $("#mdlMissEnv_" + type).modal('show');
      if (type == 'GR' || type == 'GRAll' && p) {
        this.listOfMissMatchedEncounters = p.map(item => {
          let newItem = {
            patientName: item.patientName,
            accountNumber: item.accountNumber,
            mrn: item.mrn,
            dob: item.dob,
            encounterSeenDate: item.encounterSeenDate,
            physicianName: item.physicianName,
            facilityName: item.facilityName,
          };
          return newItem;
        })
      }
      if (type == 'DELETE' && p) {
        this.listOfMissMatchedEncounters = p.map(item => {
          let newItem = {
            patientName: item.patientName,
            accountNumber: item.accountNumber,
            mrn: item.mrn,
            dob: item.dob,
            encounterSeenDate: item.encounterSeenDate,
            physicianName: item.physicianName,
            facilityName: item.facilityName,
            deleteBy: item.deleteBy,
            deletedOn: item.deletedOn,
            deletedReason: item.deletedReason
          };
          return newItem;
        })
      }
      if ((type == 'NG' || type == 'NGAll') && p) {
        this.listOfMissMatchedEncounters = p.map(item => {
          let newItem = {
            nextgenEncounterID: item.nextgenEncounterID,
            patientName: item.patientName,
            mrn: item.mrn,
            dob: item.dob,
            encounterSeenDate: item.encounterSeenDate,
            cptCode: item.cptCode,
            physicianName: item.physicianName,
            facilityName: item.facilityName,
            encounterStaus: item.encounterStaus,
            agent_name: item.agent_name,
            locationName: item.locationName
          };
          return newItem;
        })
      }
      this.detailExportFileName = type + '_' + this.monthsList[month - 1] + '_' + this.selectedYear + '_' + item.physician;
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  modelClose() {
    this.selectedRequest = null;
  }

  sortColumn(columnBy) {
    this.sortColumnBy = columnBy;

    if (columnBy == 'facility') {
      this.orderByFacilityName = this.orderByFacilityName == 'asc' ? 'desc' : 'asc';
      this.orderBy = this.orderByFacilityName;
    }
    else if (columnBy == 'physician') {
      this.orderByPhysician_name = this.orderByPhysician_name == 'asc' ? 'desc' : 'asc';
      this.orderBy = this.orderByPhysician_name;
    }

    this.listOfReports = this.sortOrderBy(this.listOfReports, columnBy, this.orderBy)
  }

  sortOrderBy(list, sortBy, sortOrder) {
    let newArray = [];
    this.p = 1;
    if (sortOrder == 'asc') {
      newArray = list.sort(
        function (a, b) {
          let sortNest: number = (b[sortBy] > a[sortBy]) ? -1 : 0;
          let sort: number = (a[sortBy] > b[sortBy]) ? 1 : sortNest;
          return sort;
        }
      );
    }
    else if (sortOrder == 'desc') {
      newArray = list.sort(
        function (a, b) {
          let sortNest: number = (b[sortBy] > a[sortBy]) ? 1 : 0;
          let sort: number = (a[sortBy] > b[sortBy]) ? -1 : sortNest;
          return sort;
        });
    }
    return newArray;
  }

  search(searchKey) {
    clearTimeout(this.timeout);

    this.timeout = setTimeout(() => {
      if (this.listOfReports.length)
        this.listOfReports = this.filterByValue(this.listOfReportsInitial, searchKey)
    }, 2000);

  }

  filterByValue(array, string) {
    return array.filter(o => {
      return Object.keys(o).some(k => {
        if (typeof o[k] === 'string')
          return o[k].toLowerCase().includes(string.toLowerCase());
      });
    });
  }
  reasonChange(event) {
    if (event.target.value == "Duplicate") {
      this.encounterIDbyUser = -1;
    }
    else {
      this.encounterIDbyUser = null;
    }
  }

  exportAsXLSXForDetails(): void {
    mobiscroll.confirm({
      title: 'Confirm',
      message: `Do you want download?`,
      okText: 'Yes',
      cancelText: 'Cancel',
      callback: (res) => {
        if (res) {
          let fileName = "";
          fileName = "DETAIL_REPORT_" + this.detailExportFileName;
          this.excelService.exportAsExcelFile(this.listOfMissMatchedEncounters, fileName);
        }
      }
    });
  }

  getGroupsByFacility(facillity, type: boolean) {
    if (type) {
      this.commonServ.startLoading();
    }
    this.reportsServ.getGroupNameByFacility(facillity).subscribe((p: any) => {
      this.selectedListOfGroups = [];
      this.listOfGroups = [];
      this.selectedListOfPhysicians = [];
      this.listOfPhysicians = [];
      this.listOfGroups = p;
      if (type) {
        this.commonServ.stopLoading();
      }
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  onGroupSelect(item: any) {
    this.commonServ.startLoading();
    let group_names: string = '';
    this.selectedListOfGroups.forEach((grp: any) => {
      group_names = group_names + grp.group_name + ',';
    });
    this.reportsServ.getPhysiciansByGroup(group_names).subscribe((p: any) => {
      this.commonServ.stopLoading();
      this.selectedListOfPhysicians = [];
      this.listOfPhysicians = [];
      this.listOfPhysicians = p;
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  onGroupSelectAll(item: any, type: string) {
    this.commonServ.startLoading();
    let group_names: string = '';
    if (type == 'S') {
      this.listOfGroups.forEach((grp: any) => {
        group_names = group_names + grp.group_name + ',';
      });
      this.reportsServ.getPhysiciansByGroup(group_names).subscribe((p: any) => {
        this.commonServ.stopLoading();
        this.selectedListOfPhysicians = [];
        this.listOfPhysicians = [];
        this.listOfPhysicians = p;
      }, error => {
        this.commonServ.stopLoading();
      });
    }
    else {
      this.commonServ.stopLoading();
      this.selectedListOfPhysicians = [];
      this.listOfPhysicians = [];
    }
  }


}

