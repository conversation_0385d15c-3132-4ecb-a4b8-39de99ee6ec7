import { Component, OnInit } from '@angular/core';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { CommonService } from 'src/app/services/common/common.service';
import { AppComponent } from 'src/app/app.component';
import { FormGroup, FormBuilder, Validators, FormControl } from '@angular/forms';
import { CoordinatorService } from 'src/app/services/coordinator/coordinator.service';
import { FacilityModel } from 'src/app/models/facility.model';
import { PhysicianModel } from 'src/app/models/physician.model';
import { DatePipe } from '@angular/common';
import * as moment from 'moment';
import { forkJoin } from 'rxjs';
import { ToastrService } from 'ngx-toastr';

declare let $:any;

@Component({
  selector: 'app-coordinatorview-edit',
  templateUrl: './coordinatorview-edit.component.html',
  styleUrls: ['./coordinatorview-edit.component.css']
})
export class CoordinatorviewEditComponent implements OnInit {
  public FilterForm: FormGroup;
  public listOfFacilities=Array<FacilityModel>();
  public listOfFacilitiesAndStatus=Array<FacilityModel>();
  public listOfPatients:Array<any>=[];
  public request:any={};
  public p:number=1;
  public searchByName:string="";
  public totalCount :number;
  public searchByFacility:string="";
  public listOfPhysicians=Array<PhysicianModel>();
  public listOfAllPhysicians=Array<PhysicianModel>();
  public submitted:boolean=false;
  public firstEcounterIdToOpen:string="";
  public envAccountNo:string="";
  public envMrnNo:string="";
  public envFacility:string="";
  public lisfOfEncounters:Array<any>=[];
  public isEnvRemoveFromBilling:number=1;
  public PatientObject:any={};
  public encounterObj:any={};
  public listOfHistory:Array<any>=[];
  public lisfOfAttachments:Array<any>=[];
  public groupAlerts:any={};
  public patient:any={};
  public listOfUsersAndGroups: Array<any> = [];
  public lisfOfSentNotes: Array<any> = [];
  public selectedUsersAndGroups: Array<any> = [];
  public savedCoordinatorNotes:string='';
  public lisfOfMissingEncounters:Array<any>=[];
  public timeout: any = null;
  public encounterIds:string="";
  public daterange: any = {}; 
  public arrival = '';
  public arrivalLabel:string="";
  public arrivalDateVal:boolean=false;
  public approvedRequired:string='';
  public facilitystr:string=''; 
  public lisfOfGroupEncounters: Array<any> = [];
  public isDisabled : boolean=false;
  startDate: any =moment().subtract(6, 'month').startOf('month');
  endDate: any = moment();
  updateDate: any = '';
  public ranges: any = {
    'Last 6 Months': [moment().subtract(6, 'month').startOf('month'), moment()],
    'Last Year': [moment().startOf('year').subtract(1, 'year'), moment().endOf('year').subtract(1, 'year')],
    'This Year': [moment().startOf('year'), moment()]

  };
  public dateRangeLable: any = ''; 
  device = false;
  constructor(private readonly coordinatorServ:CoordinatorService, private readonly commonServ:CommonService,private readonly encrDecr: EncrDecrServiceService
    ,private readonly appComp:AppComponent,private readonly fb: FormBuilder,private readonly datePipe: DatePipe,private readonly toastr: ToastrService) {
    this.loadInitialDateRange();
  }

  ngOnInit() {
    if (/Android|webOS|iPhone|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) && (window.innerWidth <= 575 || window.innerWidth >= 575)) {
      // some code..
      this.device = true;
    } else {
      this.device = false;
    }

    this.appComp.loadPageName('Coordinator View/Edit','coordinatorTab');
    this.FilterForm = this.fb.group({
      ddlFacility: [''],
      ddlPhysician: ['All', Validators.required],
      encounterSeenDateFrom: ['', [Validators.nullValidator,endDateValidator]],
      encounterSeenDateTo: ['', [Validators.nullValidator,endDateValidator]],
      discharged:[false],
      ddlMarkAsApp:['', [Validators.nullValidator]],
      dateRangeLable:[this.dateRangeLable]            
    }    
    );
    this.getFacilities();
    
  }
  get f() { return this.FilterForm.controls; }

  loadInitialDateRange() {
    let from_date: any = this.datePipe.transform(moment().subtract(6, 'month').startOf('month').toDate(), 'yyyy-MM-dd');
    let to_date: any = this.datePipe.transform(moment().toDate(), 'yyyy-MM-dd');
    this.dateRangeLable = { "startDate": new Date(from_date), "endDate": new Date(to_date) };
    this.daterange.start = new Date(moment().subtract(6, 'month').startOf('month').toDate());
    this.daterange.end = new Date(moment().toDate());
    this.daterange.label = 'Last 6 Months Admission Data';
    this.arrival = this.datePipe.transform(this.daterange.start, 'MM/dd/yyyy') + ' - ' + this.datePipe.transform(this.daterange.end, 'MM/dd/yyyy');
    this.arrivalLabel = this.daterange.label;
  }
  selectedDate(value: any) {
    if (value.startDate && value.endDate) {
      this.daterange.start = new Date(value.startDate.$d);
      this.daterange.end = new Date(value.endDate.$d)
      this.daterange.label = this.datePipe.transform(new Date(value.startDate.$d), 'MM/dd/yyyy') + ' - ' + this.datePipe.transform(new Date(value.endDate.$d), 'MM/dd/yyyy');;
      this.arrival = this.datePipe.transform(this.daterange.start, 'MM/dd/yyyy') + ' - ' + this.datePipe.transform(this.daterange.end, 'MM/dd/yyyy');
      this.arrivalLabel = this.daterange.label;
      this.updateDate = JSON.stringify(value);
      this.arrivalDateVal = false;
    }
  }
  getFacilities(){
    forkJoin(
      this.commonServ.GetFacilitiesByCoorORAudr('COORDINATOR'),
      this.commonServ.getFacilitiesByUserType('COORDINATOR')
    ).subscribe((p: any) => {
      this.listOfFacilitiesAndStatus =p[0];
      this.listOfFacilities = p[1];
      this.FilterForm.get('ddlFacility')?.setValue(this.listOfFacilities[0].facilityName);
      this.getPhysiciansByFacility(this.FilterForm.value.ddlFacility);
      if(this.listOfFacilitiesAndStatus.length >0)
      {
        for (let item of this.listOfFacilitiesAndStatus) {      
         if(item.approvedRequired == 'True')
         {
         this.approvedRequired = item.approvedRequired;
         break;
         } 
        }
      }
      if(this.approvedRequired =='True')
      {
        $("#approvedRequired").show();
      }
      else{
        $("#approvedRequired").hide();
        this.FilterForm.get('ddlMarkAsApp')?.setValue('All');
      }
      this.approvedRequired="";
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  } 

  getPatientsViewCharges(pno) {
    this.p = pno;
    this.submitted = true;
    if (this.FilterForm.invalid) {
      return;
    }
    if (!this.FilterForm.value.encounterSeenDateFrom && !this.FilterForm.value.encounterSeenDateTo && this.FilterForm.value.encounterSeenDateFrom > this.FilterForm.value.encounterSeenDateTo) {
      return;
    }
    let dchged: string = '0';
    if (this.FilterForm.value.discharged)
      dchged = '1';
    this.commonServ.startLoading();
    this.request.Facility_Name = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.PhysicianId = this.encrDecr.set(this.FilterForm.value.ddlPhysician);
    this.request.MarkasSeenDateFrom = this.encrDecr.set(this.FilterForm.value.encounterSeenDateFrom);
    this.request.MarkasSeenDateTo = this.encrDecr.set(this.FilterForm.value.encounterSeenDateTo);
    this.request.AdmissionDateRange = this.encrDecr.set(this.arrival);
    this.request.strPageNumber = this.encrDecr.set(pno);
    this.request.strPagesize = this.encrDecr.set("14");
    this.request.searchText = this.encrDecr.set(this.searchByName);
    this.request.sIsDischargeDate = this.encrDecr.set(dchged);
    this.request.MarkasApprove = this.encrDecr.set(this.FilterForm.value.ddlMarkAsApp);
    this.coordinatorServ.GetPatientsChargesDataCoordinator(this.request).subscribe((p: any) => {
      this.submitted = false;
      this.arrivalDateVal = false;
      this.totalCount = p.patientCount[0].totalCount;
      this.listOfPatients = p.patient;
      this.request = {};
      this.PatientObject={};
      this.commonServ.stopLoading();
    }, error => { console.error(error.status); }
    )
  }

  getPhysiciansByFacility(facillity) {
    if (facillity == 'All') {
      facillity = '';
    }
    let approvedCount = this.listOfFacilitiesAndStatus.filter(x => x.approvedRequired == "True");
    if (this.listOfFacilitiesAndStatus.length > 0 && facillity == '' && approvedCount.length > 0) {
      this.approvedRequired = "True";
      $("#approvedRequired").show();
    }
    else if (this.listOfFacilitiesAndStatus.length > 0 && facillity != '') {
      for (let item of this.listOfFacilitiesAndStatus) {
        if (item.facilityName == facillity && item.approvedRequired == "True") {
          this.approvedRequired = item.approvedRequired;
          $("#approvedRequired").show();
          break;
        }
      }
    }
    else {
      $("#approvedRequired").hide();
      this.FilterForm.get('ddlMarkAsApp')?.setValue('All');
    }
    this.approvedRequired = "";
    this.FilterForm.get('ddlPhysician')?.setValue('All');
    this.searchByFacility = facillity;
    this.commonServ.startLoading();
    this.getPatientsViewCharges("1");
    this.p = 1;
    this.commonServ.getPhysiciansByUserType(facillity, 'COORDINATOR').subscribe((p: any) => {
      this.listOfAllPhysicians = p;
    }, error => {
      this.commonServ.stopLoading();
    });

    this.FilterForm.value.ddlPhysician = 'All';
  }

  SearchPatientData() {
    clearTimeout(this.timeout);
    
    this.timeout = setTimeout(() => {
      this.getPatientsViewCharges(1);
    }, 2000);
  }

  getEncountersByPatient(pObj,ui_click:boolean) {
    if (!ui_click || this.PatientObject.account_Number != pObj.account_Number) {
      this.envAccountNo = pObj.account_Number;
      this.envMrnNo = pObj.mrn;
      this.envFacility = pObj.facility_Name;
      this.commonServ.startLoading();
      this.request.account_Number = this.encrDecr.set(pObj.account_Number);
      this.request.facilityName = this.encrDecr.set(pObj.facility_Name);
      this.request.PHYSICIANMAILID = this.encrDecr.set(this.FilterForm.value.ddlPhysician);
      this.request.MRN = this.encrDecr.set(pObj.mrn);
      this.request.MarkasApprove = this.encrDecr.set(this.FilterForm.value.ddlMarkAsApp);
      this.coordinatorServ.getEncountersByPatient(this.request).subscribe((p: any) => {
        this.lisfOfEncounters = p.listofEncounters;
        this.lisfOfEncounters.forEach(y => y.listOfEncounters.forEach(x => x.encounterseendate = this.encrDecr.get(x.encounterseendate)));
        this.isEnvRemoveFromBilling = p.isEnvRemoveFromBilling;
        this.firstEcounterIdToOpen = p.firstEcounterIdToOpen;
        this.request = {};
        this.lisfOfEncounters.forEach((b, index) => {
          if (b.groupName) {
            this.getAlerts(pObj, b.groupName, b.listOfEncounters);
          }
        });

        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        console.error(error.status);
        this.commonServ.stopLoading();
      });
      this.PatientObject = pObj;
    }
  }
  getAlerts(pObj,gName,lstEnvs){
    this.commonServ.startLoading();
    this.PatientObject=pObj;
    this.request.Patient_Account_Number=this.encrDecr.set(pObj.account_Number);
    this.request.Facility_Name=this.encrDecr.set(pObj.facility_Name);
    this.request.GroupName=this.encrDecr.set(gName);  
    lstEnvs.forEach((x,index)=> {
      this.encounterIds = this.encounterIds + "," + x.encounteR_ID;
  });
  this.request.Encounter_Ids = this.encounterIds;
  this.encounterIds = "";
    this.coordinatorServ.getAlerts(this.request).subscribe((p: any) => {
      this.request={};     
      this.groupAlerts=p;
      this.commonServ.stopLoading();
    },error => {
      this.request={};
      this.commonServ.stopLoading();
      console.error(error.status);
    });
  }
  clearFilterBillerPatients(){
    this.submitted = false;
    this.FilterForm.get('encounterSeenDateFrom')?.setValue('');
    this.FilterForm.get('encounterSeenDateTo')?.setValue('');
  }

}

export function endDateValidator(control: FormControl) {
  let startDate = control.root.get('encounterSeenDateFrom');
  let endDate = control.root.get('encounterSeenDateTo');
  if (startDate && endDate) {
    let startDateVal = new Date(startDate.value);
    let endDateVal = new Date(endDate.value);
    let errorObj = { 'encounterSeenDateFrom': false, 'encounterSeenDateTo': false };
    let isEndDateValid = true;
    if (startDate.value && endDate.value) {
      isEndDateValid = (startDateVal < endDateVal || startDateVal.getDate() == endDateVal.getDate());
    }
    endDate.setErrors((!isEndDateValid)
      ? errorObj : null);
    startDate.setErrors((!isEndDateValid)
      ? errorObj : null);
    if (control.errors) { return { "endDateError": true } };
  }
  return null;
}

