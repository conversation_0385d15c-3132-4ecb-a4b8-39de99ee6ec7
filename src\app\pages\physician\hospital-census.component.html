<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new p-2" style="background: #0169ab;" [formGroup]="FilterForm">
                    <div class="align-items-center row mx-auto">
                        <div class="col-12 px-0">

                            <div class="row mb-2 mt-1 mx-auto">
                                <div class="col-12 col-md-5 px-1 py-1">
                                    <select id="ddlFacility" class="form-control" formControlName="ddlFacility"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}"
                                        (change)="facilityChangeEvent()">
                                        <option value="All">All Facilities</option>
                                        <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                                            <span>{{s.facilityName}}</span>
                                        </option>
                                    </select>
                                </div>

                                <div class="col-12 col-md-5 px-1 py-1">
                                    <select id="ddlDepartment" class="form-control custom-control"
                                        formControlName="ddlDepartment" (change)="getHospitalCensusData(1)"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlDepartment'].errors}">
                                        <option value="All">All Departments</option>
                                        <option [value]="s.patienT_LOCATION" *ngFor="let s of listofDepartments">
                                            {{s.patienT_LOCATION}}
                                        </option>
                                    </select>
                                </div>

                            </div>
                            <div class="row mt-2 mb-1 mx-auto">
                                <div class="col-10 col-md-10 px-1 py-1">
                                    <input type="text" class="form-control small" maxlength="1000"
                                        placeholder="Search for Patient Name, Account No, MRN or Room"
                                        aria-label="Search" aria-describedby="basic-addon2"
                                        formControlName="txtSearchKey" (keyup)="onKeyPatientSearch()">
                                    <img alt=' ' class="search position-absolute mr-3"
                                        src="../../../assets/img/search.png">
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <!-- table Card starts -->
                <div class="card-body px-md-2 py-md-1 px-1 py-0">
                    <app-patients [listOfPatients]="listOfPatients" [p]="p" [pageName]="'/physician/hospital-census'" [FilterForm]="FilterForm" [totalCount]="totalCount" [searchByName]="''" [device]="device" (enventUpdateSortObj)="updateSortObj($event)"  [orderBy]="orderBy" [sortColumnBy]="sortColumnBy" (eventListOfPatients)="getHospitalCensusData($event)"></app-patients>
                </div>
                <!-- table card ends-->

            </div>
        </div>

    </div>
</div>