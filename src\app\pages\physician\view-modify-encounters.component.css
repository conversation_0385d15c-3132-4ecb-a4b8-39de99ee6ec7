/* Import mobile responsive styles */
/* Mobile Responsive Styles for GrandRounds UI */

/* Base mobile styles */
@media (max-width: 767px) {
    .container-fluid {
        padding: 0.5rem;
    }

    .card-header-new {
        padding: 0.5rem !important;
    }

    .form-control {
        font-size: 14px;
    }

    .btn {
        font-size: 14px;
        padding: 0.25rem 0.5rem;
    }
}

/* Mobile view container */
.mobile-view-container {
    padding: 0;
}

/* Mobile card styles */
.mobile-card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e3e6f0;
}

.mobile-card .card-header {
    background-color: #336699 !important;
    color: white !important;
    cursor: pointer;
    border-bottom: 1px solid #e3e6f0;
}

.mobile-card .card-body {
    padding: 0.75rem;
}

/* Mobile modal styles */
.mobile-modal .modal-dialog {
    margin: 0;
    max-width: 100%;
    height: 100vh;
}

.mobile-modal .modal-content {
    height: 100vh;
    border-radius: 0;
    border: none;
}

.mobile-modal .modal-header {
    padding: 10px 15px;
    background-color: #0169ab !important;
    color: white !important;
}

.mobile-modal .modal-body {
    -webkit-overflow-scrolling: touch;
    padding: 15px;
}

/* iOS specific fixes */
.modal-open {
    position: fixed;
    width: 100%;
}

/* Fix for iOS modal scrolling */
.modal {
    -webkit-overflow-scrolling: touch !important;
}

/* Ensure popups are properly positioned on mobile */
.popover {
    max-width: 90%;
    width: 90%;
}

/* Mobile CPT and ICD modal styles */
.mobile-cpt-modal, .mobile-icd-modal {
    margin: 0.5rem auto;
    max-width: 95%;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    top: auto;
    transform: none !important;
}

.mobile-cpt-modal .modal-content, .mobile-icd-modal .modal-content {
    border-radius: 0.5rem 0.5rem 0 0;
    max-height: 90vh;
    border-bottom: none;
}

.mobile-cpt-modal .modal-header, .mobile-icd-modal .modal-header {
    background-color: #0169ab !important;
    color: white !important;
}

.mobile-cpt-modal .modal-body, .mobile-icd-modal .modal-body {
    padding: 0.75rem;
    max-height: 70vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

/* Fix for iOS modal scrolling issues */
.mobile-cpt-modal .scrolling, .mobile-icd-modal .scrolling {
    -webkit-overflow-scrolling: touch !important;
    overflow-y: scroll !important;
    height: 300px !important;
}

/* Fix for iOS modal backdrop */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
}

/* Fix for iOS modal positioning */
@media (max-width: 767px) {
    .modal.fade .modal-dialog {
        transform: none !important;
    }

    .modal.show .modal-dialog {
        transform: none !important;
    }
}

/* iOS date input placeholder fix */
input[type="date"]::-webkit-datetime-edit-text,
input[type="date"]::-webkit-datetime-edit-month-field,
input[type="date"]::-webkit-datetime-edit-day-field,
input[type="date"]::-webkit-datetime-edit-year-field {
  color: #495057;
}

/* Empty date inputs should show placeholder color */
input[type="date"]:not(.has-value)::-webkit-datetime-edit {
  color: #6c757d;
}

/* Fix for iOS date inputs */
@supports (-webkit-touch-callout: none) {
  input[type="date"] {
    display: inline-block;
    position: relative;
  }

  /* Show placeholder text for empty date inputs */
  input[type="date"]:not(.has-value)::before {
    content: attr(placeholder);
    position: absolute;
    color: #6c757d;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
  }

  /* Hide placeholder when input is focused or has a value */
  input[type="date"].has-value::before,
  input[type="date"]:focus::before {
    display: none;
  }

  /* Fix for iOS date picker appearance */
  input[type="date"]::-webkit-date-and-time-value {
    text-align: left;
  }

  /* Make sure the placeholder doesn't overlap with the calendar icon */
  input[type="date"]::-webkit-calendar-picker-indicator {
    background: transparent;
    color: transparent;
    position: absolute;
    right: 0;
    top: 0;
    width: 2.5rem;
    height: 100%;
    cursor: pointer;
  }
}
