<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new p-2" style="background: #0169ab;" [formGroup]="UnDischargeForm">
                    <div class="align-items-center row mx-auto">
                        <div class="col-12 px-0">

                            <div class="row mx-auto">
                                <div class="col-12 col-md-5 px-1 py-1">
                                    <select formControlName="role" class="form-control"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlFacilityName'].errors}"
                                        formControlName="ddlFacilityName">
                                        <option value="">Choose Facility Name</option>
                                        <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                                            <span>{{s.facilityName}}</span>
                                        </option>
                                    </select>

                                </div>
                                <div class="col-12 col-md-5 px-1 py-1">
                                    <input class="form-control input-border" type="text" formControlName="txtAccountNo"
                                        [ngClass]="{ 'is-invalid': submitted && f['txtAccountNo'].errors}" type="text"
                                        placeholder="Account Number">

                                </div>

                                <div class="col-12 col-md-2 text-right text-md-left px-1 py-1">
                                    <button class="btn btn-outline-info px-2 btn-block" (click)="GetDischargePatient()">
                                        Search
                                    </button>

                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <!-- table Card starts -->
                <div class="card-body px-md-2 py-md-1 px-1 py-0">
                    <div class="row mx-0">
                        <div class="col-12 px-0">
                            <div>
                                <table class="table table-bordered my-auto">
                                    <thead>
                                        <tr>
                                            <th>Patient Name</th>
                                            <th>AccountNo</th>
                                            <th>DOB</th>
                                            <th>Discharge Date</th>
                                            <th>Facility</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let udp of listofDischargePatients">
                                            <td id="dPatientName">{{udp?.patient_Name}}</td>
                                            <td id="dPatientAccNo">{{udp?.account_Number}}</td>
                                            <td id="dOB">{{udp?.dob}}</td>
                                            <td id="dDischargeDate">{{udp?.discharge_Date}}</td>
                                            <td id="dFacilityName">{{udp?.facility_Name}}</td>
                                            <td> <button class="btn btn-outline-info px-2 float-left btn-style"
                                                    (click)="openUnDischargePatientPop(udp)">
                                                    Un-Discharge
                                                </button></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- table card ends-->

            </div>
        </div>

    </div>
</div>


<!-- Undo Dischare Confiramation Popup Model Starts -->
<app-undo-discharge-patient [patient]="patient"
    (eventListOfPatients)="GetDischargePatient()"></app-undo-discharge-patient>
<!-- Undo Dischare Confiramation Popup Model Ends -->