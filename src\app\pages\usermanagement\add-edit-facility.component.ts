import { Component, OnInit, Input } from '@angular/core';
import { Form<PERSON><PERSON>er, Validators, FormGroup } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { UserManagementService } from 'src/app/services/user-management/userManagement.service';
import { AddModifyFacilityComponent } from './add-modify-facility.component';
declare let $;
@Component({
  selector: 'app-add-edit-facility',
  templateUrl: './add-edit-facility.component.html',
  styles: []
})
export class AddEditFacilityComponent implements OnInit {
  public listOfStates: Array<any> = [];
  public listOfTimeZone: Array<any> = [];
  public request: any = {};
  @Input() facilityObj: any = {};
  public AddorEditFacilityForm: FormGroup;
  public submitted: boolean = false;

  constructor(private readonly userSer: UserManagementService, private readonly commonServ: CommonService, private readonly fb: FormBuilder, private readonly encrDecr: EncrDecrServiceService,
    private readonly toastr: ToastrService, private readonly addmodifyfacility: AddModifyFacilityComponent) { }

  ngOnInit(): void {
    this.fetchStates();
    this.AddorEditFacilityForm = this.fb.group({
      txtFacilityName: ['', Validators.required],
      txtFacilityFullName: [''],
      txtAddress: [''],
      txtCity: ['', Validators.required],
      ddlStates: ['', Validators.required],
      txtZipCode: [''],
      ddlTimeZone: [''],
      chkIsPrimeHospital: [false],
      chkCoordinatorAppRqrd: [false],
      chkIsAuditorApp: [false],
      chkAdmissionDateEdit: [false]
    });
  }
  get f() { return this.AddorEditFacilityForm.controls; }
  fetchStates() {

    this.commonServ.startLoading();
    this.userSer.getStatesandTimeZones().subscribe((p: any) => {
      this.listOfStates = p.stateInfos;
      this.listOfTimeZone = p.timeZonelist;
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }
  GetTimeZones(item) {

    this.commonServ.startLoading();
    this.userSer.getTimeZonebyState(item).subscribe((p: any) => {
      this.listOfTimeZone = p.timeZonelist;
      if (this.facilityObj.facilityID == 0) {
        this.facilityObj.timezoneID = p.timeZonelist[0].timeZoneID;
      }
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  submittedFacility(facilityobj, flag) {
    this.submitted = true;
    if (this.AddorEditFacilityForm.invalid) {
      return;
    }

    this.commonServ.startLoading();
    this.request.strFacilityID = this.encrDecr.set(facilityobj.facilityID);
    this.request.FacilityName = this.encrDecr.set(this.AddorEditFacilityForm.value.txtFacilityName);
    if (this.AddorEditFacilityForm.value.txtFacilityFullName) {
      this.request.Facility_Full_Name = this.encrDecr.set(this.AddorEditFacilityForm.value.txtFacilityFullName);
    }
    if (this.AddorEditFacilityForm.value.txtAddress) {
      this.request.Facility_Address = this.encrDecr.set(this.AddorEditFacilityForm.value.txtAddress);
    }

    if (this.AddorEditFacilityForm.value.txtZipCode) {
      this.request.Zip_Code = this.encrDecr.set(this.AddorEditFacilityForm.value.txtZipCode);
    }
    this.request.CITY = this.encrDecr.set(this.AddorEditFacilityForm.value.txtCity);
    this.request.strCoordinatorAppRequired = this.encrDecr.set(this.AddorEditFacilityForm.value.chkCoordinatorAppRqrd);
    this.request.strIsPrimeHospital = this.encrDecr.set(this.AddorEditFacilityForm.value.chkIsPrimeHospital);
    this.request.strIsAdissionDateEdit = this.encrDecr.set(this.AddorEditFacilityForm.value.chkAdmissionDateEdit);
    this.request.strIsAuditRequired = this.encrDecr.set(this.AddorEditFacilityForm.value.chkIsAuditorApp);
    this.request.STATE_NAME = this.encrDecr.set(this.AddorEditFacilityForm.value.ddlStates);
    this.request.strTimezoneID = this.encrDecr.set(this.AddorEditFacilityForm.value.ddlTimeZone);
    this.request.Flag = this.encrDecr.set(flag);
    this.userSer.InsertOrUpdateFacilitiesDetails(this.request).subscribe((p: any) => {
      this.request = {};
      this.submitted = false;
      this.commonServ.stopLoading();
      if (p == 'SUCCESS') {
        if (flag == 'UPDATE') {
          this.toastr.success('Facility Updated Successfully.', '', { timeOut: 2500 });
        }
        else {
          this.toastr.success('Facility Added Successfully.', '', { timeOut: 2500 });
        }

        $('#AddorEditFacility').modal('hide');
        this.addmodifyfacility.fetchFacilities();
      }
      else {
        this.toastr.error('Facility already exist');
      }

    },
      error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
  }

  ReloadFacilities() {
    this.addmodifyfacility.fetchFacilities();
  }

}
