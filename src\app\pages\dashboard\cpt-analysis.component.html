<!-- Begin Page Content Starts -->
<div class="container-fluid px-3" *ngIf="userAccess.dashboardModuleAccess=='YES'">
  <!-- Content Row -->

  <div class="card shadow mb-4 border-blue">
    <div class="card-header py-2" style=" background:#0169ab;">
      <div class="align-items-center row mx-auto" [formGroup]="searchForm">
        <div class="col-12 px-0">

          <div class="row mb-2 mt-1 mx-auto">
            <div class="col-12 col-md-3 px-1 py-1">
              <select id="ddlFacility" placement="left" class="form-control w-100" name="ddlFacility"
                (change)="getGroupsByFacility($any($event.target).value,true)"
                [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}" formControlName="ddlFacility">
                <option value="">---Select Facility---</option>
                <option value="All">All Facilities</option>
                <option value="{{s.facilityName}}" *ngFor="let s of listOfFacilities">
                  {{s.facilityName}}
                </option>
              </select>
            </div>
            <div class="col-12 col-md-3 px-1 py-1 multiselect-absolute">
              <ng-multiselect-dropdown class="report-multiselect form-control" [placeholder]="'---Select Groups---'"
                [data]="listOfGroups" [(ngModel)]="selectedListOfGroups" [settings]="mDdlGroupsSettings"
                formControlName="ddlGroups" [ngClass]="{ 'is-invalid': submitted && f['ddlGroups'].errors}"
                (onSelect)="onGroupSelect($event)" (onDeSelect)="onGroupSelect($event)"
                (onSelectAll)="onGroupSelectAll($event,'S')" (onDeSelectAll)="onGroupSelectAll($event,'D')">
              </ng-multiselect-dropdown>
              <div *ngIf="submitted && f['ddlGroups'].errors" class="invalid-feedback">
                <div *ngIf="f['ddlGroups'].errors['required']">Group is required</div>
              </div>
            </div>
            <div class="col-12 col-md-3 px-1 py-1">
              <ng-multiselect-dropdown class="report-multiselect form-control" [placeholder]="'---Select Physician---'"
                [data]="listOfPhysicians" [(ngModel)]="selectedListOfPhysicians" [settings]="mDdlPhysicianSettings"
                formControlName="ddlPhysican" [ngClass]="{ 'is-invalid': submitted && f['ddlPhysician'].errors}">
              </ng-multiselect-dropdown>
            </div>
            <div class="col-12 col-md-2 px-1 py-1">
              <select class="form-control w-100" [ngClass]="{ 'is-invalid': submitted && f['ddlYear'].errors}"
                formControlName="ddlYear">
                <option value="">---Select Year---</option>
                <option [value]="year" *ngFor="let year of listOfYears">
                  <span>{{year}}</span>
                </option>
              </select>
            </div>
            <div class="col-12 col-md-1 text-right text-md-left px-1 py-1">
              <button type="btn" class="btn btn-outline-info px-2 btn-block" (click)="getGetFilteredReport()"><i
                  class="fa-filter fas font-size-13"></i> Search</button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="card-body pb-0">

      <div class="row">
        <div class="col-11 mx-auto">
          <div class="row">

            <div class="col-md-4 stretch-card grid-margin">
              <div class="card bg-gradient-success card-img-holder text-white mx-auto mr-md-4">

                <div class="card-body">
                  <div *ngIf="!loadEncounterRpt" class="loaderDashboard"></div>
                  <div *ngIf="loadEncounterRpt">
                    <img alt=' ' src="../../../assets/img/circle.svg" class="card-img-absolute" alt="circle-image">
                    <h4 class="font-weight-normal mb-3">
                      Encounters
                    </h4>
                    <h2>{{totalEncounterCount}}</h2>
                    <div class="icon-position">
                      <i class="fas fa-eye mx-1 cursor-pointer" style="font-size:30px;" title="Encounters Report Data"
                        data-toggle="modal" data-target="#encounterReportData"></i>
                      <i class="fa-chart-area fas mx-2 cursor-pointer" style="font-size:30px;" title="View Graph"
                        data-toggle="modal" data-target="#viewEncounterReport"></i>
                        <i class="fa-file-excel fas mx-2 cursor-pointer" style="font-size:28px;"
                        (keyup)="exportAsXLSXEncounter()" (click)="exportAsXLSXEncounter()" title="Export Excel"></i>
                    </div>
                    <img alt=' ' src="../../../assets/img/bar-chart.png" class="bottom-image">
                  </div>
                </div>
              </div>
            </div>

            <div class="col-md-4 stretch-card grid-margin">
              <div class="card bg-gradient-primary-card card-img-holder text-white mx-auto mr-md-4">
                <div class="card-body">
                  <div *ngIf="!loadCptRpt" class="loaderDashboard"></div>
                  <div *ngIf="loadCptRpt">
                    <img alt=' ' src="../../../assets/img/circle.svg" class="card-img-absolute" alt="circle-image">
                    <h4 class="font-weight-normal mb-3">
                      CPT Report
                    </h4>
                    <h2>{{totalCPTCount}}</h2>
                    <div class="icon-position">
                      <i class="fas fa-eye mx-1 cursor-pointer" style="font-size:30px;" title="CPT Report Data"
                        data-toggle="modal" data-target="#cptReportData"></i>
                      <i class="fa-chart-area fas mx-2 cursor-pointer" style="font-size:30px;" title="View Graph"
                        data-toggle="modal" data-target="#viewCPTReport"></i>
                        <i class="fa-file-excel fas mx-2 cursor-pointer" style="font-size:28px;"
                        (keyup)="exportAsXLSXCPT()" (click)="exportAsXLSXCPT()" title="Export Excel"></i>
                    </div>
                    <img alt=' ' src="../../../assets/img/bar-chart.png" class="bottom-image">
                  </div>
                </div>
              </div>
            </div>

            <div class="col-md-4 stretch-card grid-margin">
              <div class="card bg-gradient-secondary-card card-img-holder text-white mx-auto mr-md-4">
                <div class="card-body">
                  <div *ngIf="!loadTopEncounterRpt" class="loaderDashboard"></div>
                  <div *ngIf="loadTopEncounterRpt">
                    <img alt=' ' src="../../../assets/img/circle.svg" class="card-img-absolute" alt="circle-image">
                    <h4 class="font-weight-normal mb-3">
                      Top 5 Encounters
                    </h4>
                    <h2 style="display:none;">95,5741</h2>
                    <div class="icon-position">
                      <i class="fas fa-eye mx-1 cursor-pointer" style="font-size:30px;"
                        title="Encounter Top 5 Report Data" data-toggle="modal"
                        data-target="#encounterTop5ReprotData"></i>
                      <i class="fa-chart-area fas mx-2 cursor-pointer" style="font-size:30px;" title="View Graph"
                        data-toggle="modal" data-target="#viewTop5EncounterReport"></i>
                        <i class="fa-file-excel fas mx-2 cursor-pointer" style="font-size:28px;"
                        (keyup)="exportAsXLSXTop5Encounter()" (click)="exportAsXLSXTop5Encounter()" title="Export Excel"></i>
                    </div>
                    <img alt=' ' src="../../../assets/img/pie-chart.png" class="bottom-image">
                  </div>
                </div>
              </div>
            </div>

            <div class="col-md-4 stretch-card grid-margin">
              <div class="card bg-gradient-third-card card-img-holder text-white mx-auto mr-md-4">
                <div class="card-body">
                  <div *ngIf="!loadCptRpt" class="loaderDashboard"></div>
                  <div *ngIf="loadCptRpt">
                    <img alt=' ' src="../../../assets/img/circle.svg" class="card-img-absolute" alt="circle-image">
                    <h4 class="font-weight-normal mb-3">
                      Top 5 CPTs
                    </h4>
                    <h2 style="display:none;">95,5741</h2>
                    <div class="icon-position">
                      <i class="fas fa-eye mx-1 cursor-pointer" style="font-size:30px;" title="cptTop 5 Report Data"
                        data-toggle="modal" data-target="#cptTop5ReprotData"></i>
                      <i class="fa-chart-area fas mx-2 cursor-pointer" style="font-size:30px;" title="View Graph"
                        data-toggle="modal" data-target="#viewTop5CPTReport"></i>
                        <i class="fa-file-excel fas mx-2 cursor-pointer" style="font-size:28px;"
                        (keyup)="exportAsXLSXTop5CPT()" (click)="exportAsXLSXTop5CPT()" title="Export Excel"></i>
                    </div>
                    <img alt=' ' src="../../../assets/img/pie-chart.png" class="bottom-image">
                  </div>
                </div>
              </div>
            </div>

            <div class="col-md-4 grid-margin" routerLink="/report/cpt-summary">
              <div class="card bg-gradient-danger card-img-holder text-white mx-auto mr-md-4">
                <div class="card-body">
                  <img alt=' ' src="../../../assets/img/circle.svg" class="card-img-absolute" alt="circle-image">
                  <h4 class="font-weight-normal mb-3">
                    CPT Summary
                  </h4>
                  <h2 style="display: none;">$ 15,0000</h2>
                  <img alt=' ' src="../../../assets/img/profits.png" class="bottom-image">
                </div>
              </div>
            </div>

            <div class="col-md-4 stretch-card grid-margin" routerLink="/report/cpt-summary-btween-years">
              <div class="card bg-gradient-info card-img-holder text-white mx-auto mr-md-4">
                <div class="card-body">
                  <img alt=' ' src="../../../assets/img/circle.svg" class="card-img-absolute" alt="circle-image">
                  <h4 class="font-weight-normal mb-3">
                    CPT Summary b/w Years
                  </h4>
                  <h2 style="display: none;">$ 15,0000</h2>
                  <img alt=' ' src="../../../assets/img/pie-chart.png" class="bottom-image">
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>

</div>
<!-- Begin Page Content Ends -->
<!-- Begin Page Content Starts -->
<div class="container-fluid px-3" *ngIf="userAccess.kareoDashboardModuleAccess=='YES'">
  <!-- Content Row -->

  <div class="card shadow mb-4 border-blue">
    <div class="card-header py-2" style=" background:#0169ab;">
      <div class="align-items-center row mx-auto" [formGroup]="searchFormForKareo">
        <div class="col-12 px-0">

          <div class="row mb-2 mt-1 mx-auto">
            <div class="col-12 col-md-3 px-1 py-1">
              <select id="kareoFacility" placement="left" class="form-control w-100" name="kareoFacility"
                (change)="getPhysiciansByFacilityForKareo($any($event.target).value)"
                [ngClass]="{ 'is-invalid': submittedKareo && fKareo['kareoFacility'].errors}"
                formControlName="kareoFacility">
                <option value="">---Select Facility---</option>
                <option value="All">All Facilities</option>
                <option value="{{s.facilityName}}" *ngFor="let s of listOfFacilitiesForKareo">
                  {{s.facilityName}}
                </option>
              </select>
            </div>
            <div class="col-12 col-md-3 px-1 py-1">
              <select id="kareoPhysican" class="form-control custom-control"
                [ngClass]="{ 'is-invalid': submittedKareo && fKareo['kareoPhysican'].errors}"
                formControlName="kareoPhysican">
                <option value="">---Select Physician---</option>
                <option value="All">All</option>
                <option value="{{s.physicianEmaiId}}" *ngFor="let s of listOfPhysiciansForKareo">
                  {{s.physicianName}}
                </option>
              </select>
            </div>
            <div class="col-12 col-md-4 px-1 py-1">
              <select class="form-control w-100"
                [ngClass]="{ 'is-invalid': submittedKareo && fKareo['kareoYear'].errors}" formControlName="kareoYear">
                <option value="">---Select Year---</option>
                <option [value]="year" *ngFor="let year of listOfYears">
                  <span>{{year}}</span>
                </option>
              </select>
            </div>
            <div class="col-12 col-md-2 text-right text-md-left px-1 py-1">
              <button type="btn" class="btn btn-outline-info px-2 btn-block" (click)="getGetFilteredReportForKareo()"><i
                  class="fa-filter fas font-size-13"></i> Search</button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="card-body pb-0">
      <div class="row">
        <div class="col-11 mx-auto">
          <div class="row">
            <div class="col-md-4 stretch-card grid-margin">
              <div class="card bg-gradient-warning card-img-holder text-white mx-auto mr-md-4 min-height-25vh">
                <div class="card-body">
                  <div *ngIf="!loadKareoRpt" class="loaderDashboard"></div>
                  <div *ngIf="loadKareoRpt">
                    <img alt=' ' src="../../../assets/img/circle.svg" class="card-img-absolute" alt="circle-image">
                    <h4 class="font-weight-normal mb-3">
                      Kareo Encounters
                    </h4>
                    <div class="row mx-auto">
                      <div class="col-md px-0 px-md-1 text-center">
                        <h6>Billed in GR &nbsp;&nbsp;&nbsp;&nbsp;</h6>
                        <h6 class="font-weight-bold">{{totalBilledEncounterCount}}</h6>
                      </div>
                      <div class="col-md px-0 px-md-1 text-center">
                        <h6>Billing Pending in GR</h6>
                        <h6 class="font-weight-bold">{{listOfBillingPendingInGR}}</h6>
                      </div>
                    </div>
                    <hr class="border-top-1 border-white" />
                    <div class="row mx-auto mb-4 mb-md-4 mb-lg-5">
                      <div class="col-md px-0 px-md-1 text-center">
                        <h6>Kareo Approved</h6>
                        <h6 class="font-weight-bold">{{totalApprovedCount}}</h6>
                      </div>
                      <div class="col-md px-0 px-md-1 text-center">
                        <h6>Pending in Kareo</h6>
                        <h6 class="font-weight-bold">{{totalUnApprovedCount}}</h6>
                      </div>
                    </div>

                    <div class="icon-position">
                      <i class="fas fa-eye mx-1 cursor-pointer" style="font-size:30px;"
                        title="Kareo Encounter Report Data" data-toggle="modal"
                        data-target="#billedEncounterReportData"></i>
                      <i class="fa-chart-area fas mx-2 cursor-pointer" style="font-size:30px;" title="View Graph"
                        data-toggle="modal" data-target="#billedViewEncounterReport"></i>
                        <i class="fa-file-excel fas mx-2 cursor-pointer" style="font-size:28px;"
                          title="Export Excel" (click)="billedExportAsXLSXEncounter()" (keyup)="billedExportAsXLSXEncounter()"></i>
                    </div>
                    <img alt=' ' src="../../../assets/img/bar-chart.png" class="bottom-image">
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>

</div>
<!-- Begin Page Content Ends -->
<!-- Encounter data model popups starts -->
<div class="modal fade" id="viewEncounterReport" tabindex="-1" aria-labelledby="exampleModalCenterTitle"
  aria-hidden="true">
  <div class="modal-dialog  modal-max">
    <div class="modal-content">
      <div class="modal-header py-1">
        <h5 class="modal-title w-100" id="exampleModalCenterTitle">
          Total Encounters : {{totalEncounterCount}}
          <i class="fa fa-times float-right ml-2" data-dismiss="modal"
            style="width: 24px;cursor: pointer;font-size: 24px;color: white;"></i>
        </h5>
      </div>
      <div class="modal-body pop-mx-body" id="conatinerEncounterChart">

      </div>
    </div>
  </div>
</div>
<!-- Encounter chart model popups ends -->
<!-- Encounter data model popups starts -->
<div class="modal fade" style=" background-color:rgba(0, 0, 0, 0.33);" id="encounterReportData" tabindex="-1"
  aria-labelledby="exampleModalCenterTitle3" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
      <div class="modal-body">
        <!-- search start -->
        <div class="row">
          <div class="col-12">
            <!-- tabs start -->
              <nav>

                <div class="nav nav-tabs border-bottom-0" id="nav-tabpop" role="tablist">
                  <a class="nav-item nav-link pop-nav px-5" [routerLink]="" [class.active]="showERD&&!showERDwP" (click)="expandTabs(showERD,showERDwP)">Encounters Report Data</a>
                  <a class="nav-item nav-link pop-nav px-5" [routerLink]="" [class.active]="!showERD&&showERDwP" (click)="expandTabs(showERD,showERDwP)">Encounters Report Data By Physician</a>
                  
                </div>
              </nav>
              <div class="card px-3 py-2 shadow tab-content" id="nav-tabContent">
                <div class="tab-pane scrolling" [ngClass]="{'show active' :showERD&&!showERDwP,'fade':!showERD&&showERDwP}">
                  <div class="row mx-0">
                    <div class="col">
                      <div class="table-responsive">
                        <table class="table table-bordered" id="dataTable3 width-100per">
                          <thead>
                            <tr>
                              <th class="text-nowrap bg-gradient-primary text-white">Facility</th>
                              <th class="text-nowrap bg-gradient-primary text-white">JAN</th>
                              <th class="text-nowrap bg-gradient-primary text-white">FEB</th>
                              <th class="text-nowrap bg-gradient-primary text-white">MAR</th>
                              <th class="text-nowrap bg-gradient-primary text-white">APR</th>
                              <th class="text-nowrap bg-gradient-primary text-white">MAY</th>
                              <th class="text-nowrap bg-gradient-primary text-white">JUN</th>
                              <th class="text-nowrap bg-gradient-primary text-white">JUL</th>
                              <th class="text-nowrap bg-gradient-primary text-white">AUG</th>
                              <th class="text-nowrap bg-gradient-primary text-white">SEP</th>
                              <th class="text-nowrap bg-gradient-primary text-white">OCT</th>
                              <th class="text-nowrap bg-gradient-primary text-white">NOV</th>
                              <th class="text-nowrap bg-gradient-primary text-white">DEC</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr *ngFor="let item of lstOfEncounterReportData">
                              <td class="pname">{{item.patienT_FACILITY_NAME}}</td>
                              <td class="faci">{{item.jan}}</td>
                              <td class="faci">{{item.feb}}</td>
                              <td class="faci">{{item.march}}</td>
                              <td class="faci">{{item.april}}</td>
                              <td class="faci">{{item.may}}</td>
                              <td class="faci">{{item.june}}</td>
                              <td class="faci">{{item.july}}</td>
                              <td class="faci">{{item.aug}}</td>
                              <td class="faci">{{item.sep}}</td>
                              <td class="faci">{{item.oct}}</td>
                              <td class="faci">{{item.nov}}</td>
                              <td class="faci">{{item.dec}}</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="tab-pane scrolling" [ngClass]="{'show active' :!showERD&&showERDwP,'fade':showERD&&!showERDwP}">
                  <div class="row mx-0">
                    <div class="col">
                      <div class="table-responsive">
                        <table class="table table-bordered" id="dataTable3 width-100per">
                          <thead>
                            <tr>
                              <th class="text-nowrap bg-gradient-primary text-white">Facility</th>
                              <th class="text-nowrap bg-gradient-primary text-white">Physician</th>
                              <th class="text-nowrap bg-gradient-primary text-white">JAN</th>
                              <th class="text-nowrap bg-gradient-primary text-white">FEB</th>
                              <th class="text-nowrap bg-gradient-primary text-white">MAR</th>
                              <th class="text-nowrap bg-gradient-primary text-white">APR</th>
                              <th class="text-nowrap bg-gradient-primary text-white">MAY</th>
                              <th class="text-nowrap bg-gradient-primary text-white">JUN</th>
                              <th class="text-nowrap bg-gradient-primary text-white">JUL</th>
                              <th class="text-nowrap bg-gradient-primary text-white">AUG</th>
                              <th class="text-nowrap bg-gradient-primary text-white">SEP</th>
                              <th class="text-nowrap bg-gradient-primary text-white">OCT</th>
                              <th class="text-nowrap bg-gradient-primary text-white">NOV</th>
                              <th class="text-nowrap bg-gradient-primary text-white">DEC</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr *ngFor="let item of lstOfEncounterReportDataByPhysician">
                              <td class="pname">{{item.patienT_FACILITY_NAME}}</td>
                              <td class="faci">{{item.physicianMailId}}</td>
                              <td class="faci">{{item.jan}}</td>
                              <td class="faci">{{item.feb}}</td>
                              <td class="faci">{{item.march}}</td>
                              <td class="faci">{{item.april}}</td>
                              <td class="faci">{{item.may}}</td>
                              <td class="faci">{{item.june}}</td>
                              <td class="faci">{{item.july}}</td>
                              <td class="faci">{{item.aug}}</td>
                              <td class="faci">{{item.sep}}</td>
                              <td class="faci">{{item.oct}}</td>
                              <td class="faci">{{item.nov}}</td>
                              <td class="faci">{{item.dec}}</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>

              </div>
          </div>
        </div>
      </div>

     
      
      <div class="modal-footer py-2">

        <button class="btn btn-outline-info float-right" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
<!-- Encounter data model popups ends -->
<!-- CPT chart model popups starts -->
<div class="modal fade" id="viewCPTReport" tabindex="-1" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog  modal-max">
    <div class="modal-content">
      <div class="modal-header py-1">
        <h5 class="modal-title w-100" id="exampleModalCenterTitle">
          Total CPTs : {{totalCPTCount}}
          <i class="fa fa-times float-right ml-2" data-dismiss="modal"
            style="width: 24px;cursor: pointer;font-size: 24px;color: white;"></i>
        </h5>
      </div>
      <div class="modal-body pop-mx-body" id="conatinerCPTChart">

      </div>
    </div>
  </div>
</div>
<!-- CPT chart model popups view ends -->
<!-- CPT data model popups starts -->
<div class="modal fade" style=" background-color:rgba(0, 0, 0, 0.33);" id="cptReportData" tabindex="-1"
  aria-labelledby="exampleModalCenterTitle4" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
      <div class="modal-header py-2">
        <h5 class="modal-title" id="exampleModalCenterTitle4">CPT Report Data</h5>
        <i class="fa fa-times float-right ml-2" data-dismiss="modal"
          style="width: 24px;cursor: pointer;font-size: 24px;color: white;"></i>
      </div>
      <div class="modal-body p-2 modal-height">
        <div class="row mx-0">
          <div class="col">
            <div class="table-responsive">
              <table class="table table-bordered" id="dataTable4 width-100per">
                <thead>
                  <tr>
                    <th class="text-nowrap bg-gradient-primary text-white">Facility</th>
                    <th class="text-nowrap bg-gradient-primary text-white">JAN</th>
                    <th class="text-nowrap bg-gradient-primary text-white">FEB</th>
                    <th class="text-nowrap bg-gradient-primary text-white">MAR</th>
                    <th class="text-nowrap bg-gradient-primary text-white">APR</th>
                    <th class="text-nowrap bg-gradient-primary text-white">MAY</th>
                    <th class="text-nowrap bg-gradient-primary text-white">JUN</th>
                    <th class="text-nowrap bg-gradient-primary text-white">JUL</th>
                    <th class="text-nowrap bg-gradient-primary text-white">AUG</th>
                    <th class="text-nowrap bg-gradient-primary text-white">SEP</th>
                    <th class="text-nowrap bg-gradient-primary text-white">OCT</th>
                    <th class="text-nowrap bg-gradient-primary text-white">NOV</th>
                    <th class="text-nowrap bg-gradient-primary text-white">DEC</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let item of lstOfCPTReportData">
                    <td class="pname">{{item.facilityName}}</td>
                    <td class="faci">{{item.jan}}</td>
                    <td class="faci">{{item.feb}}</td>
                    <td class="faci">{{item.march}}</td>
                    <td class="faci">{{item.april}}</td>
                    <td class="faci">{{item.may}}</td>
                    <td class="faci">{{item.june}}</td>
                    <td class="faci">{{item.july}}</td>
                    <td class="faci">{{item.aug}}</td>
                    <td class="faci">{{item.sep}}</td>
                    <td class="faci">{{item.oct}}</td>
                    <td class="faci">{{item.nov}}</td>
                    <td class="faci">{{item.dec}}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer py-2">

        <button class="btn btn-outline-info float-right" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
<!-- CPT data model popups ends -->
<!-- encounter top5 chart model starts -->
<div class="modal fade" id="viewTop5EncounterReport" tabindex="-1" aria-labelledby="exampleModalCenterTitle"
  aria-hidden="true">
  <div class="modal-dialog  modal-max">
    <div class="modal-content">
      <div class="modal-header py-1">
        <h5 class="modal-title w-100" id="exampleModalCenterTitle">
          Top 5 CPTs
          <i class="fa fa-times float-right ml-2" data-dismiss="modal"
            style="width: 24px;cursor: pointer;font-size: 24px;color: white;"></i>
        </h5>
      </div>
      <div class="modal-body pop-mx-body" id="containertop10phybyEnt">

      </div>
    </div>
  </div>
</div>
<!-- encounter top5 chart model popups end -->
<!-- encounter top5 data model popups starts -->
<div class="modal fade" style=" background-color:rgba(0, 0, 0, 0.33);" id="encounterTop5ReprotData" tabindex="-1"
  aria-labelledby="exampleModalCenterTitle1" aria-hidden="true">
  <div class="modal-dialog custom-modal modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header py-2">
        <h5 class="modal-title" id="exampleModalCenterTitle1">Encounter Top 5 Report Data</h5>
        <i class="fa fa-times float-right ml-2" data-dismiss="modal"
          style="width: 24px;cursor: pointer;font-size: 24px;color: white;"></i>
      </div>
      <div class="modal-body p-2 modal-height">
        <div class="row mx-0">
          <div class="col">
            <div class="table-responsive">
              <table class="table table-bordered" id="dataTable1 width-100per">
                <thead>
                  <tr>
                    <th class="text-nowrap bg-gradient-primary text-white">Physician</th>
                    <th class="text-nowrap bg-gradient-primary text-white">Facility</th>
                    <th class="text-nowrap bg-gradient-primary text-white">Count</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let item of lstOfTop5EncounterReportData">
                    <td>{{item.physicianName}}</td>
                    <td>{{item.patienT_FACILITY_NAME}}</td>
                    <td class="faci">{{item.count}}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer py-2">
        <button class="btn btn-outline-info float-right" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
<!-- encounter top5 data model popups ends -->
<!-- cpt top5 chart model starts -->
<div class="modal fade" id="viewTop5CPTReport" tabindex="-1" aria-labelledby="exampleModalCenterTitle"
  aria-hidden="true">
  <div class="modal-dialog  modal-max">
    <div class="modal-content">
      <div class="modal-header py-1">
        <h5 class="modal-title w-100" id="exampleModalCenterTitle">
          Top 5 Physicians
          <i class="fa fa-times float-right ml-2" data-dismiss="modal"
            style="width: 24px;cursor: pointer;font-size: 24px;color: white;"></i>
        </h5>
      </div>
      <div class="modal-body pop-mx-body" id="containertop10phybyCPTs">

      </div>
    </div>
  </div>
</div>
<!-- cpt top5 chart model popups ends -->
<!-- cpt top5 data model popups starts -->
<div class="modal fade" style=" background-color:rgba(0, 0, 0, 0.33);" id="cptTop5ReprotData" tabindex="-1"
  aria-labelledby="exampleModalCenterTitle2" aria-hidden="true">
  <div class="modal-dialog custom-modal modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header py-2">
        <h5 class="modal-title" id="exampleModalCenterTitle2">Top 5 Physicians</h5>
        <i class="fa fa-times float-right ml-2" data-dismiss="modal"
          style="width: 24px;cursor: pointer;font-size: 24px;color: white;"></i>
      </div>
      <div class="modal-body p-2 modal-height">
        <div class="row mx-0">
          <div class="col">
            <div class="table-responsive">
              <table class="table table-bordered" id="dataTable2 width-100per">
                <thead>
                  <tr>
                    <th class="text-nowrap bg-gradient-primary text-white">Physician</th>
                    <th class="text-nowrap bg-gradient-primary text-white">Count</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let item of lstOfTop5CPTReportData">
                    <td>{{item.physicianName}}</td>
                    <td class="faci">{{item.count}}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer py-2">
        <button class="btn btn-outline-info float-right" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
<!-- cpt top5 data model popups ends -->
<!-- Encounter data model popups starts -->
<div class="modal fade" style=" background-color:rgba(0, 0, 0, 0.33);" id="billedEncounterReportData" tabindex="-1"
  aria-labelledby="exampleModalCenterTitle3" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
      <div class="modal-header py-2">
        <h5 class="modal-title" id="exampleModalCenterTitle3">Kareo Encounter Report Data</h5>
        <i class="fa fa-times float-right ml-2" data-dismiss="modal"
          style="width: 24px;cursor: pointer;font-size: 24px;color: white;"></i>
      </div>
      <div class="modal-body p-2 modal-height">
        <div class="row mx-0">
          <div class="col">
            <div class="table-responsive">
              <table class="table table-striped table-bordered table-hover billed_encounters_grid"
                id="dataTable3 width-100per">
                <thead>
                  <tr>
                    <th class="text-nowrap bg-gradient-primary text-white">Facility</th>
                    <th class="text-nowrap bg-gradient-primary text-white">JAN</th>
                    <th class="text-nowrap bg-gradient-primary text-white">FEB</th>
                    <th class="text-nowrap bg-gradient-primary text-white">MAR</th>
                    <th class="text-nowrap bg-gradient-primary text-white">APR</th>
                    <th class="text-nowrap bg-gradient-primary text-white">MAY</th>
                    <th class="text-nowrap bg-gradient-primary text-white">JUN</th>
                    <th class="text-nowrap bg-gradient-primary text-white">JUL</th>
                    <th class="text-nowrap bg-gradient-primary text-white">AUG</th>
                    <th class="text-nowrap bg-gradient-primary text-white">SEP</th>
                    <th class="text-nowrap bg-gradient-primary text-white">OCT</th>
                    <th class="text-nowrap bg-gradient-primary text-white">NOV</th>
                    <th class="text-nowrap bg-gradient-primary text-white">DEC</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let item of lstOfBilledReportData">
                    <td class="pname">{{item.facilityName}}</td>
                    <td>
                      <div class='{{item.janColor}}' title="In Kareo">{{item.ajan}}</div>
                      <hr class="hr_table">
                      <div class="billed_count" title="In Grand Rounds">{{item.jan}}</div>
                    </td>
                    <td>
                      <div class="{{item.febColor}}" title="In Kareo">{{item.afeb}}</div>
                      <hr class="hr_table">
                      <div class="billed_count" title="In Grand Rounds">{{item.feb}}</div>
                    </td>
                    <td>
                      <div class="{{item.marchColor}}" title="In Kareo">{{item.amarch}}</div>
                      <hr class="hr_table">
                      <div class="billed_count" title="In Grand Rounds">{{item.march}}</div>
                    </td>
                    <td>
                      <div class="{{item.aprilColor}}" title="In Kareo">{{item.aapril}}</div>
                      <hr class="hr_table">
                      <div class="billed_count" title="In Grand Rounds">{{item.april}}</div>
                    </td>
                    <td>
                      <div class="{{item.mayColor}}" title="In Kareo">{{item.amay}}</div>
                      <hr class="hr_table">
                      <div class="billed_count" title="In Grand Rounds">{{item.may}}</div>
                    </td>
                    <td>
                      <div class="{{item.juneColor}}" title="In Kareo">{{item.ajune}}</div>
                      <hr class="hr_table">
                      <div class="billed_count" title="In Grand Rounds">{{item.june}}</div>
                    </td>
                    <td>
                      <div class="{{item.julyColor}}" title="In Kareo">{{item.ajuly}}</div>
                      <hr class="hr_table">
                      <div class="billed_count" title="In Grand Rounds">{{item.july}}</div>
                    </td>
                    <td>
                      <div class="{{item.augColor}}" title="In Kareo">{{item.aaug}}</div>
                      <hr class="hr_table">
                      <div class="billed_count" title="In Grand Rounds">{{item.aug}}</div>
                    </td>
                    <td>
                      <div class="{{item.sepColor}}" title="In Kareo">{{item.asep}}</div>
                      <hr class="hr_table">
                      <div class="billed_count" title="In Grand Rounds">{{item.sep}}</div>
                    </td>
                    <td>
                      <div class="{{item.octColor}}" title="In Kareo">{{item.aoct}}</div>
                      <hr class="hr_table">
                      <div class="billed_count" title="In Grand Rounds">{{item.oct}}</div>
                    </td>
                    <td>
                      <div class="{{item.novColor}}" title="In Kareo">{{item.anov}}</div>
                      <hr class="hr_table">
                      <div class="billed_count" title="In Grand Rounds">{{item.nov}}</div>
                    </td>
                    <td>
                      <div class="{{item.decColor}}" title="In Kareo">{{item.adec}}</div>
                      <hr class="hr_table">
                      <div class="billed_count" title="In Grand Rounds">{{item.dec}}</div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer py-2">

        <button class="btn btn-outline-info float-right" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
<!-- Encounter data model popups ends -->
<!-- View Encounter data model popups starts -->
<div class="modal fade" id="billedViewEncounterReport" tabindex="-1" aria-labelledby="exampleModalCenterTitle"
  aria-hidden="true">
  <div class="modal-dialog  modal-max">
    <div class="modal-content">
      <div class="modal-header py-1">
        <h5 class="modal-title w-100" id="exampleModalCenterTitle">
          Kareo Encounters Report : {{totalBilledCount}}
          <i class="fa fa-times float-right ml-2" data-dismiss="modal"
            style="width: 24px;cursor: pointer;font-size: 24px;color: white;"></i>
        </h5>
      </div>
      <div class="modal-body pop-mx-body" id="billedConatinerEncounterChart">

      </div>
    </div>
  </div>
</div>
<!-- View Encounter chart model popups ends -->