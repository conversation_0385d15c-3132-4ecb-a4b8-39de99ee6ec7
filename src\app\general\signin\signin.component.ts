import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AppComponent } from 'src/app/app.component';
declare let $: any;

@Component({
  selector: 'app-signin',
  templateUrl: './signin.component.html',
  styleUrls: ['./signin.component.css']
})
export class SigninComponent implements OnInit {
  loggedIn = false;
  itemsPerSlide = 1;
  singleSlideOffset = false;
  noWrap = false;
  cycleInterval = 10000;
  slides = [
    { image: 'assets/params/images/safai-from-settings.jpeg' },
    { image: 'assets/params/images/enable-third-party-cookies-iphone-safari.png' }
  ];
  currentUrl = '';
  constructor(private readonly appComp: AppComponent, private readonly router: Router) {

    this.currentUrl = window.location.href;

    appComp.loggedIn = false;
    appComp.userName = '';
    appComp.userAccess = {};

  }
  idleTime = 0;
  timeoutHandle: any = null;

  ngOnInit(): void {
    $('#loading').hide();

    if (this.currentUrl.indexOf('signin') != -1) {
      this.timeoutHandle = window.setTimeout(() => {
        if (this.currentUrl.indexOf('signin') != -1) {
          let token = this.extractMSALToken();

          const pingNow: any = new Date();

          let lastPing: Date = localStorage.getItem('lastPing') ? new Date(localStorage.getItem('lastPing')!) : pingNow;

          this.idleTime = this.diff_minutes(lastPing);

          if (token && this.idleTime > 2) {

            history.pushState({}, '', '/#/');
            this.appComp.logout();

          }
        }
      }, 180000);
    }
  }
  preventBack() {
    const backlen = history.length;
    history.go(-backlen);
    window.location.href = '/#/signin';
  }
  login() {
    sessionStorage.setItem('loaded', '');
    let token = this.extractMSALToken();
    if (token) {
      this.router.navigate(['']);
    }
    else {
      this.appComp.login();
    }
  }
  extractMSALToken() {
    const timestamp = Math.floor((new Date()).getTime() / 1000);
    let token = null;
    for (const key of Object.keys(localStorage)) {
      if (key.includes('login.windows.net-accesstoken') && key.includes('user.read')) {
        const val: any = JSON.parse(localStorage.getItem(key)!);
        if (val.expiresOn) {
          // We have a (possibly expired) token

          if (val.expiresOn > timestamp && val.secret) {
            // Found the correct token
            token = val.secret;
          } else {
            console.log('will remove ' + key);
            // Clear old data
            localStorage.removeItem(key);
            this.clearExpiryData();
          }
        }
      }
    }
    if (token) { return token; } else { return null; }
  }

  diff_minutes(startDate) {
    // Do your operations
    const endDate = new Date();
    const seconds = (endDate.getTime() - startDate.getTime()) / 1000;
    const minutes = seconds / 60;
    return minutes;
  }

  clearExpiryData() {
    for (const key of Object.keys(localStorage)) {
      if (key.includes('login.windows.net') || key.includes('ng2Idle.main') || key.includes('lastPing')) {
        console.log('will remove ' + key);
        // Clear old data
        localStorage.removeItem(key);
      }
    }
  }

  ngOnDestroy(): void {
    window.clearTimeout(this.timeoutHandle);
  }
}
