<div class="modal fade" id="attachment" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="dvHeder">{{PatientObject.patient_Name}}-{{PatientObject.account_Number}}
                    Attachments</h5>
                <i class="fa fa-times closePopup" data-dismiss="modal"></i>
            </div>
            <div class="panel panel-info">
                <div class="modal-body py-3 bg-light">
                    <div class="row mx-auto">
                        <div class="col-12 col-md-5 pr-md-1 py-1">

                            <div class="input-group">
                                <div class="custom-file">
                                    <input #file type="file" id="attchFileId" (change)="uploadChange(file.files)"
                                        class="custom-file-input" aria-describedby="attchFileId">
                                    <label class="custom-file-label padding-5rem" for="inputGroupFile04">{{ fileName ? fileName :
                                        'Choose file'}}</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-5 px-md-1 py-1">
                            <input type="text" class="form-control" [(ngModel)]="comment" maxlength="1000"
                                [ngModelOptions]="{standalone: true}" placeholder="Write Comments">
                        </div>
                        <div class="col-12 col-md-2 pl-md-1 py-1">
                            <div class="input-group">
                                <button class="btn btn-primary btn-block" type="button" id="attchFileId"
                                    (click)="uploadSubmit(PatientObject)">Upload</button>
                            </div>
                        </div>

                    </div>
                    <div class="row mx-auto" *ngIf="submitted">
                        <div class="col">
                            <div class="text-danger" *ngIf="isFileRequired && submitted">
                                Please select file.
                            </div>
                            <div class="text-danger" *ngIf="isCommentLength && submitted">
                                Max length for comment is 400 characters.
                            </div>
                            <div class="text-danger" *ngIf="isFileValidName && submitted">
                                Please upload a file with valid name.
                            </div>
                            <div class="text-danger" *ngIf="isFile && submitted">
                                Please select a valid file to upload.
                            </div>
                            <div class="text-danger" *ngIf="isFileSize && submitted">
                                Uploaded file size should be less than 20 MB.
                            </div>
                        </div>
                    </div>

                    <div class="row mx-auto mt-3 mb-auto">
                        <div class="col-md-12">
                            <div class="table-responsive">
                                <table class="table table-bordered" id="tblPatDocs">
                                    <thead class="bg-blue text-white text-center font-weight-bold">
                                        <tr>
                                            <th>File Type</th>
                                            <th>Uploaded comments</th>
                                            <th>Created By</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let item of lisfOfAttachments">
                                            <td>
                                                <img alt=' '
                                                    *ngIf="item.doC_NAME_TYPE=='XLS'||item.doC_NAME_TYPE=='XLSX'"
                                                    src="../../../assets/img/excel-icon.png">
                                                <img alt=' '
                                                    *ngIf="item.doC_NAME_TYPE=='DOC'||item.doC_NAME_TYPE=='DOCX'||item.doC_NAME_TYPE=='DOCX'"
                                                    src="../../../assets/img/word.png">
                                                <img alt=' ' *ngIf="item.doC_NAME_TYPE=='PDF'"
                                                    src="../../../assets/img/pdf.png">
                                                <img alt=' ' *ngIf="item.doC_NAME_TYPE=='TXT'"
                                                    src="../../../assets/img/txt.png">
                                                <img alt=' ' *ngIf="item.doC_NAME_TYPE=='CSV'"
                                                    src="../../../assets/img/excel.png">
                                                <img alt=' ' *ngIf="item.doC_NAME_TYPE=='IMAGE'"
                                                    src="../../../assets/img/img.png">
                                                <img alt=' ' *ngIf="item.doC_NAME_TYPE=='UnKnown'"
                                                    src="../../../assets/img/unknown.png">
                                            </td>
                                            <td>{{item.comments}}</td>
                                            <td>{{item.createD_BY_NAME}}</td>
                                            <td>
                                                <a class="mx-1" (click)="downloadAttachment(item.doC_NAME)">
                                                    <i class="fas fa-download" title="Download"></i>
                                                </a>
                                                <a *ngIf="item.isDelete && userType!='COMMON'" class="mx-1 pointer"
                                                    (click)="deleteAttachment(lisfOfAttachments,item)">
                                                    <i class="far fa-trash-alt text-danger" title="Remove"></i>
                                                </a>
                                                <a *ngIf="item.doC_NAME_TYPE=='IMAGE'" class="mx-1">
                                                    <img alt=' ' src="../../../assets/img/view.png"
                                                        attr.data-target="#viewImg-{{item.doC_ID}}" data-toggle="modal">
                                                </a>
                                            </td>
                                            <div class="modal fade" id="viewImg-{{item.doC_ID}}" tabindex="-1"
                                                aria-hidden="true">
                                                <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            View Docs
                                                            <button type="button" class="close viewImgModel"
                                                                (click)="closeViewDocs(item.doC_ID)">
                                                                <span style="color: #fff">X</span>
                                                            </button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <img alt=' ' class="img-fluid" [src]="item.doC_TO_DOWNLOAD">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>