import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ReportsRoutingModule } from './reports-routing.module';
import { AlertsReportComponent } from './alerts-report.component';
import { BilledEncounterReportComponent } from './billed-encounter-report.component';
import { CptSummaryBetweenYearsComponent } from './cpt-summary-between-years.component';
import { CptSummaryComponent } from './cpt-summary.component';
import { MissingEncounterReportComponent } from './missing-encounter-report.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';
import { CptReportComponent } from './cpt-report.component';
import { EncounterReportComponent } from './encounter-report.component';
import { NoteReportComponent } from './note-report.component';
import { FacilitiesComponent } from './facilities.component';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { AssignmentAndEncounterMismatchReportComponent } from './assignment-and-encounter-mismatch-report.component';
import { NextGenEncounterReportComponent } from './next-gen-encounter-report.component';
import { MbscModule } from '@mobiscroll/angular-lite';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { SharedModule } from 'src/app/shared.module';
import { NgxDaterangepickerMd } from 'ngx-daterangepicker-material';

@NgModule({
  imports: [
    CommonModule,
    ReportsRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    NgxPaginationModule,
    NgMultiSelectDropDownModule.forRoot(),
    MbscModule,
    BsDatepickerModule.forRoot(),
    SharedModule,
    NgxDaterangepickerMd.forRoot()
  ],
  exports: [
    EncounterReportComponent,
    CptReportComponent,
    AlertsReportComponent,
    CptSummaryBetweenYearsComponent,
    CptSummaryComponent,
    BilledEncounterReportComponent,
    MissingEncounterReportComponent,
  ],
  declarations: [
    EncounterReportComponent,
    CptReportComponent,
    AlertsReportComponent,
    CptSummaryBetweenYearsComponent,
    CptSummaryComponent,
    BilledEncounterReportComponent,
    MissingEncounterReportComponent,
    NoteReportComponent,
    FacilitiesComponent,
    AssignmentAndEncounterMismatchReportComponent,
    NextGenEncounterReportComponent
  ],
  providers: [],
  bootstrap: [EncounterReportComponent]
})
export class ReportsModule { }
