<button class="btn btn-link p-0" *ngIf="PatientObject.isNonPrime!=0 && userType=='BILLER'" title="Update Patient's Details"
    (click)="openEditPatient(PatientObject)">
    <i class="far fa-edit font-size-1-5rem"></i>
</button>
<a (click)="getAttachments(PatientObject)">
    <img alt=' ' src="../../../assets/img/pin.png" title="Attachements" width="26px" data-target="#attachment"
        data-toggle="modal" class="mx-1">
</a>
<a class="text-center" (click)="openNotes(PatientObject)">
    <img alt=' ' src="../../../assets/img/notespin.png" title="Notes" width="26px" class="mx-1">
</a>
<a class="mx-1" *ngIf="userType!='PHYSICIAN'" (click)="getMissingEncounters(PatientObject)">
    <img alt=' ' src="../../../assets/img/treatment-plan.png" title="Missing Encounters" width="26px"
        data-target="#missingEn" data-toggle="modal">
</a>
<a class="mx-1" *ngIf="!PatientObject.discharge_Date && PatientObject.isNonPrime==1 && (userType=='BILLER' || userType=='COORDINATOR')"
    (click)="updateDischargeDate(PatientObject)">
    <img alt=' ' src="../../../assets/img/calenderpin.png" title="Discharged Date" width="26px" data-target="#discharge"
        data-toggle="modal">
</a>

<!-- attachment popup starts -->
<app-upload-attachment  (eventUpdateCount)="updateAttCount(patient)" [lisfOfAttachments]="lisfOfAttachments" [PatientObject]="PatientObject"
    [userType]="userType"></app-upload-attachment>
<!-- attachment popup ends -->

<!-- send note Popup Starts -->
<app-send-note [PatientObject]="PatientObject" [listOfUsersAndGroups]="listOfUsersAndGroups"
    [lisfOfSentNotes]="lisfOfSentNotes" [selectedUsersAndGroups]="selectedUsersAndGroups" (eventListOfNotes)="openNotes(PatientObject)"></app-send-note>
<!-- send note Popup Ends -->

<!-- Missing Encounter popup starts -->
<app-add-missing-encounter [lisfOfMissingEncounters]="lisfOfMissingEncounters" [PatientObject]="PatientObject"
    [listOfPhysicians]="listOfPhysicians"></app-add-missing-encounter>
<!-- Missing Encounter popup ends -->

<!-- Edit discharge date popup starts -->
<app-edit-discharge-date [PatientObject]="PatientObject" [userType]="userType"></app-edit-discharge-date>
<!-- Edit discharge date popup ends -->

<!-- Edit Patient Popup Starts -->
<app-edit-patient [patient]="patient" (eventListOfPatients)="refreshEditPatient(PatientObject)" [userType]="userType"></app-edit-patient>
<!-- Edit Patient Popup Ends -->