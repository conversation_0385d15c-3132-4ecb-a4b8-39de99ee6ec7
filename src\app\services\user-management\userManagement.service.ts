import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { apiUrl } from 'src/app/config';
import { EncrDecrServiceService } from '../common/encr-decr-service.service';

@Injectable({
  providedIn: 'root'
})
export class UserManagementService {

  constructor(private readonly http: HttpClient, private readonly encrDecr: EncrDecrServiceService) { }

  fetchActiveUsers() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/UserManagement/FetchActiveUsers', null, { headers: headers });
  }

  FetchFacilities() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/UserManagement/FetchFacilities', null, { headers: headers });
  }

  FetchGroups() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/UserManagement/FetchGroups', null, { headers: headers });
  }

  getUserFacilitiesAndGroups(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/UserManagement/GetUserFacilitiesAndGroups', request, { headers: headers });
  }

  insertOrUpdateFacilitiesAndGroupsForUser(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/UserManagement/InsertOrUpdateFacilitiesAndGroupsForUser', request, { headers: headers });
  }

  getUserTitlesSpecialitiesAndFacilities() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/UserManagement/GetUserTitlesSpecialitiesAndFacilities', null, { headers: headers });
  }

  getStatesandTimeZones() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/UserManagement/GetStatesandTimeZones', null, { headers: headers });
  }
  getTimeZonebyState(StateName) {
    const body = { StateName: this.encrDecr.set(StateName) };
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/UserManagement/GetTimeZoneByState', body, { headers: headers });
  }

  InsertOrUpdateFacilitiesDetails(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/UserManagement/InsertOrUpdateFacilitiesDetails', request, { headers: headers });
  }

  insertOrUpdateUserDetails(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/UserManagement/InsertOrUpdateUserDetails', request, { headers: headers });
  }

  deassignRoleFacilityOrGroup(request) {
    const body = {
      user_email_id: this.encrDecr.set(request.email_id), role: this.encrDecr.set(request.role), type: this.encrDecr.set(request.type)
      , facility_name: this.encrDecr.set(request.facility_name), group_name: this.encrDecr.set(request.group_name)
    };
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/UserManagement/DeassignRoleFacilityOrGroup', body, { headers: headers });
  }

  GetUsersbyFacilityGroupWise(FacilityName) {
    const body = { FacilityName: this.encrDecr.set(FacilityName) };
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/UserManagement/GetUsersbyFacilityGroupWise', body, { headers: headers });
  }

  GetAssignedUsersbyGroup(GroupID) {
    const body = { GroupID: this.encrDecr.set(GroupID) };
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/UserManagement/GetAssignedUsersbyGroup', body, { headers: headers });
  }

  getUserByUserId(email_id) {
    const body = { ParamOne: this.encrDecr.set(email_id) };
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/UserManagement/GetUserByUserId', body, { headers: headers });
  }

  InsertOrUpdateGroupDetails(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/UserManagement/InsertOrUpdateGroupDetails', request, { headers: headers });
  }

  getUsers() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/UserManagement/GetUsers', null, { headers: headers });
  }

  CopyProfile(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/UserManagement/CopyProfile', request, { headers: headers });
  }

  getGroupsByFacilities(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/UserManagement/GetGroupsByFacilities', request, { headers: headers });
  }




}