import { Component, Input } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { forkJoin } from 'rxjs';
import { PhysicianModel } from 'src/app/models/physician.model';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
declare let $: any;

@Component({
  selector: 'app-actions-of-view-modify-encounter',
  templateUrl: './actions-of-view-modify-encounter.component.html',
  styles: [
  ]
})
export class ActionsOfViewModifyEncounterComponent {
  public request: any = {};
  @Input() PatientObject: any;
  @Input() userType: string;
  @Input() listOfFacilities: Array<any>;
  public lisfOfMissingEncounters: Array<any> = [];
  public lisfOfAttachments: Array<any> = [];
  public lisfOfSentNotes: Array<any> = [];
  public listOfPhysicians = Array<PhysicianModel>();
  public patient: any = {};
  public listOfUsersAndGroups: Array<any> = [];
  public selectedUsersAndGroups: Array<any> = [];
  constructor(private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService, private readonly toastr: ToastrService) {
    
  }
  openEditPatient(item) {
    this.commonServ.startLoading();
    this.PatientObject = item;
    this.request.account_number = this.encrDecr.set(item.account_Number);
    this.request.facility_name = this.encrDecr.set(item.facility_Name);
    this.commonServ.getPatientInfo(this.request).subscribe((p: any) => {
      this.request = {};
      this.patient = p;
      this.patient.admit_datetime = new Date(this.patient.admit_datetime);
      $('#editPatient').modal('show');
      this.commonServ.stopLoading();
    },
      error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
  }
  

  getAttachments(pObj) {
    this.commonServ.startLoading();
    this.request.AccountNumber = this.encrDecr.set(pObj.account_Number);
    this.request.FacilityName = this.encrDecr.set(pObj.facility_Name);
    this.commonServ.getAttachments(this.request).subscribe((p: any) => {
      this.lisfOfAttachments = p;
      this.request = {};
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  openNotes(item) {
    this.PatientObject = item;
    this.selectedUsersAndGroups = [];
    this.commonServ.startLoading();
    this.request.ACCOUNT_NUMBER = this.encrDecr.set(item.account_Number);
    this.request.FACILITYNAME = this.encrDecr.set(item.facility_Name);
    forkJoin(
      this.commonServ.getUserGroups(item.facility_Name),
      this.commonServ.getNotes(this.request)
    ).subscribe((p: any) => {
      this.listOfUsersAndGroups = p[0];
      this.lisfOfSentNotes = p[1];
      $('#mdlNotes').modal('show');
      this.request = {};
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.toastr.error('Something went wrong!!!');
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  getMissingEncounters(pObj) {
    this.commonServ.startLoading();
    this.getPhysicians(pObj.facility_Name);
    this.request.AccountNumber = this.encrDecr.set(pObj.account_Number);
    this.request.FacilityName = this.encrDecr.set(pObj.facility_Name);
    this.commonServ.getMissingEncounters(this.request).subscribe((p: any) => {
      this.lisfOfMissingEncounters = p;
      this.request = {};
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      console.error(error.status);
      this.commonServ.stopLoading();
    });

  }

  getPhysicians(facilitystr) {
    if (facilitystr == 'All') {
      facilitystr = '';
    }
    this.commonServ.startLoading();

    this.commonServ.getPhysiciansByFacility(facilitystr).subscribe((p: any) => {
      this.listOfPhysicians = p;
    }, error => {
      this.commonServ.stopLoading();
    });
    //this.FilterForm.value.ddlPhysician = 'All';
  }

  updateDischargeDate(pObj) {
    this.PatientObject = pObj;
  }

  refreshEditPatient(item) {
    this.commonServ.startLoading();
    this.request.account_number = this.encrDecr.set(item.account_Number);
    this.request.facility_name = this.encrDecr.set(item.facility_Name);
    this.commonServ.getPatientInfo(this.request).subscribe((p: any) => {
      this.patient = p;
      this.PatientObject.ssn = p.ssn;
      this.PatientObject.room_Number = p.room_number;
      $('#editPatient').modal('hide');
      this.commonServ.stopLoading();
    },
      error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
  }

  updateAttCount(patient) {
    console.log(patient);
  }

}
