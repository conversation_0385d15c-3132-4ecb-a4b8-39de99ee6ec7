<div class="modal fade" id="confirmAndReasonsDeleteCPT" style="z-index: 1500;" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" [formGroup]="reasonForm">
            <div class="modal-header py-2">
                <h5 class="modal-title" id="exampleModalCenterTitle"> Reasons for Modified or Deleted CPT Codes</h5>
            </div>
            <div class="modal-body">
                <div class="row mx-0 align-items-center">

                    <div class="col-12" style="color: black; font-size:medium;">
                        <label for="txtMessage" class="col-form-label">Please provide reasons for deleting cpt code :
                            "{{ cptCode }}"</label>
                        <textarea id="txtMessage" class="form-control" formControlName="txtMessage" maxlength="1000"
                            [ngClass]="{ 'is-invalid': submitted && f['txtMessage'].errors}"> </textarea>

                    </div>
                    <div *ngIf="submitted && f['txtMessage'].errors && f['txtMessage'].errors?.['required']"
                        class="form-group col-12 justify-content-end">
                        <div class="col-sm-8 text-danger">
                            Message is required
                        </div>
                    </div>
                    <div *ngIf="submitted && f['txtMessage'].errors && f['txtMessage'].errors?.['minlength']"
                        class="form-group col-12 justify-content-end">
                        <div class="col-sm-8 text-danger">
                            Atleast min length is 3 characters.
                        </div>
                    </div>
                    <div *ngIf="submitted && f['txtMessage'].errors && f['txtMessage'].errors?.['maxlength']"
                        class="form-group col-12 justify-content-end">
                        <div class="col-sm-8 text-danger">
                            Max length is 400 characters.
                        </div>
                    </div>

                    <div class="col-12 text-center mt-4">
                        <button class="btn btn-outline-info float-right" style="margin-left:10px" type="submit"
                            (click)="saveReasons()">Save</button>
                        <button class="btn btn-outline-info float-right" data-dismiss="modal"
                            (click)="cancelSaveReasons()">Cancel</button>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-2">
            </div>
        </div>
    </div>
</div>