<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <div class="card card-footer">
                    <h1 class="text-center">Data Subject Access Request Form</h1>
                    <p class="text-center">Please fill in the information below. The website administrator or data
                        protection officer will be notified of
                        your request within 24 hours, and will need an appropriate amount of time to respond.</p>
                </div>
                <div class="card-body px-md-3 px-2 pt-md-3 p-2" [formGroup]="reqiuestForm">
                    <div class="row mx-0">
                        <div class="col-12 px-0">
                            <div class="card shadow border-blue">

                                <div class="row mx-0">
                                    <div class="col-12 col-md-9">
                                        <div class="form-group">
                                            <label for="txtWebsite"><b>Website</b></label>
                                            <input class="form-control input-border"
                                                [ngClass]="{ 'is-invalid': submitted && f['txtWebsite'].errors}"
                                                type="text" formControlName="txtWebsite" id="txtWebsite"
                                                placeholder="My Great New Website / App">
                                            <div class="text-danger"
                                                *ngIf="submitted && f['txtWebsite'].errors?.['required']">Website
                                                required</div>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-9">
                                        <div class="form-group">
                                            <label for="txtName"><b>Your Name</b></label>
                                            <input class="form-control input-border" id='txtName'
                                                [ngClass]="{ 'is-invalid': submitted && f['txtName'].errors}"
                                                type="text" formControlName="txtName" id="txtName"
                                                placeholder="Enter Your Name">
                                            <div class="text-danger"
                                                *ngIf="submitted && f['txtName'].errors?.['required']"> Name required.
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12 col-md-9">
                                        <div class="form-group">
                                            <label for="txtEmail"><b>What email address do you use to access the above
                                                    website / app?</b></label>
                                            <input class="form-control input-border"
                                                [ngClass]="{ 'is-invalid': submitted && f['txtEmail'].errors}"
                                                type="text" formControlName="txtEmail" id="txtEmail"
                                                placeholder="Enter your email address">
                                            <div class="text-danger"
                                                *ngIf="submitted && f['txtEmail'].errors?.['required']">Email required
                                            </div>
                                            <div class="text-danger"
                                                *ngIf="submitted && f['txtEmail'].errors?.['email']">Invalid Email
                                                format</div>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-9">
                                        <div class="form-group">
                                            <div><b>You are submitting this request as</b></div>
                                            <div>
                                                <input type="radio" class="mr-2" name="requestType" checked="checked"
                                                    formControlName="rdoRequestType" id="rdoRequestType"
                                                    value="Whose name appears above">
                                                <label for="rdoRequestType">The person, or the parent / guardian of the
                                                    person, whose name appears above.</label>
                                            </div>
                                            <div>
                                                <input type="radio" class="mr-2" name="requestType"
                                                    formControlName="rdoRequestType" id="rdoRequestType"
                                                    value="An authorized agent">
                                                <label for="rdoRequestType">An agent authorized by the consumer to make
                                                    this request on their behalf['</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12 col-md-9">
                                        <div class="form-group">
                                            <label for="DateofBirth"><b>Please leave details regarding your action
                                                    request or question</b></label>
                                            <textarea col="10" rows="10" class="form-control input-border"
                                                [ngClass]="{ 'is-invalid': submitted && f['txtComments'].errors}"
                                                formControlName="txtComments" id="txtComments"
                                                placeholder="Please leave details regarding your action request or question.">
                                            </textarea>
                                            <div class="text-danger"
                                                *ngIf="submitted && f['txtComments'].errors?.['pattern']">Please enter
                                                valid text (* allowing alphanumeric, spaces and punctuations).</div>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-10">
                                        <div class="form-group">
                                            <div><b>I confirm that</b></div>
                                            <div>
                                                <input type="checkbox" class="mr-2" formControlName="chkConfirm1"
                                                    id="chkConfirm1" value="true">
                                                <label for="chkConfirm1">Under penalty of perjury, I declare all the
                                                    above information to be true and accurate.</label>
                                            </div>
                                            <div>
                                                <input type="checkbox" class="mr-2" formControlName="chkConfirm2"
                                                    id="chkConfirm2" value="true">
                                                <label for="chkConfirm2">I understand that the deletion or restriction
                                                    of my personal data is irreversible and may result in the
                                                    termination of services with My Great New Website / App.</label>
                                            </div>
                                            <div>
                                                <input type="checkbox" class="mr-2" formControlName="chkConfirm3"
                                                    id="chkConfirm3" value="true">
                                                <label for="chkConfirm1">I understand that I will be required to
                                                    validate my request by email, and I may be contacted in order to
                                                    complete the request.</label>
                                            </div>
                                            <div class="text-danger"
                                                *ngIf="submitted && (f['chkConfirm1'].errors?.['required']||f['chkConfirm2'].errors?.['required']||f['chkConfirm3'].errors?.['required'])">
                                                confirm required</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="card-footer px-md-3 pb-md-3 px-2 pb-3">
                    <button class="btn btn-sm btn-outline-info float-right btn-style" (click)="submit()">Submit</button>
                </div>
            </div>
        </div>
    </div>
</div>