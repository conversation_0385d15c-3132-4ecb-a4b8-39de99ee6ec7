import { Component, Input } from '@angular/core';
import { BillingService } from 'src/app/services/billing/billing.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { CommonService } from 'src/app/services/common/common.service';

@Component({
  selector: 'app-remove-from-billing-queue',
  templateUrl: './remove-from-billing-queue.component.html',
  styles: []
})
export class RemoveFromBillingQueueComponent {
  @Input() PatientObject: any = {};
  public request: any = {};
  constructor(private readonly billingServ: BillingService, private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService) { }


  confirmRemoveBillingQueue(pObj) {
    this.commonServ.startLoading();
    this.request.Account_Number = this.encrDecr.set(pObj.account_Number);
    this.request.Facility_Name = this.encrDecr.set(pObj.facility_Name);
    this.billingServ.removeFromBillingQueue(this.request).subscribe((p: any) => {
      this.request = {};
      if (p > 0) {
        pObj.account_Number = "";
      }
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
      console.error(error.status);
    });
  }

}
