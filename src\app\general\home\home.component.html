<div class="container">
    <!--vertical align on self-->
    <div *ngIf="!accessToken && !siginIn" class="row h-100">
        <div class="col-sm-12 align-self-center">
            <div
                class="card bg-light text-info border-gradient border-gradient-info-green border-primary col-12 col-sm-4 col-md-4 col-lg-4 m-auto p-0">
                <div class="card-body text-center">
                    <img alt=' ' class="mb-2" src="./assets/img/prime-white.jpg" height="25px" width="25px">
                    <div class="image-gradient mb-2 bg-gradient mx-auto text-center"></div>
                    <h2 class="card-title h2 mb-4 text-gradient"><span class="text-bringel font-weight-bold">Prime Grand
                            Rounds</span></h2>
                    <button class="btn btn-lg btn-primary btn-block bg-gradient" type="button" (click)="login()">Sign
                        in</button>
                </div>
            </div>
        </div>
    </div>
</div>