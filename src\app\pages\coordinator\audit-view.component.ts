import { DatePipe } from '@angular/common';
import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import * as moment from 'moment';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { CoordinatorService } from 'src/app/services/coordinator/coordinator.service';
import { ReportsService } from 'src/app/services/reports/reports.service';
declare let $: any;

@Component({
  encapsulation: ViewEncapsulation.None,
  selector: 'app-audit-view',
  templateUrl: './audit-view.component.html',
  styleUrls: ['./audit-view.component.css']
})
export class AuditViewComponent implements OnInit {
  public listOfFacilities: Array<any> = [];
  public listOfGroups: Array<any> = [];
  public FilterForm: FormGroup;
  public request: any = {};
  public listOfPatients: Array<any> = [];
  public Patients: Array<any> = [];
  public listOfPhysicians: Array<any> = [];
  public totalCount: number;
  public p: number = 1;
  public searchByName: string = '';
  public submitted: boolean = false;
  public daterange: any = {};
  public arrivalLabel: string;
  public PatientAccountNumber : string = "";
  public arrivalDateVal: boolean = false;
  public isPatientView:  boolean = true;
  public isPreviousView:  boolean = false;
  public isNextView:  boolean = false;
  public startDate: Date;
  public month: number;
  public year: number;
  public endDate: Date;
  public startDateP: Date;
  public firstDate: Date;
  public lastDate: Date;
  public PatientName: string = "";
  device = false;
  public search = '';
  public ranges: any = {
    'This Week': [moment().startOf('week'), moment().endOf('week')],
    'Last 6 Months': [moment().subtract(6, 'month').startOf('month'), moment()],
    'Last Year': [moment().startOf('year').subtract(1, 'year'), moment().endOf('year').subtract(1, 'year')],
    'This Year': [moment().startOf('year'), moment()]

  };
  public dateRangeLable: any = ''; 
  viewType: string = 'Week';
  listOfColumns: Array<any> = [];

  constructor(private readonly coordinatorServ: CoordinatorService, private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService
    , private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly datePipe: DatePipe, private readonly reptServ: ReportsService) {
    this.loadInitialDateRange();
  }

  ngOnInit() {
    this.appComp.loadPageName('Audit View', 'coordinatorTab');
    this.FilterForm = this.fb.group({
      ddlViewType: ['Week', Validators.required],
      ddlFacility: ['All', Validators.required],
      ddlGroups: ['All', Validators.required],
      ddlPhysician: ['All', Validators.required],
      selectedRange: ['', Validators.required],
      chkRule1: [true, Validators.required],
      chkRule2: [true, Validators.required],
      chkRule3: [true, Validators.required],
      chkRule4: [true, Validators.required],
      dateRangeLable:[this.dateRangeLable]
    });
    if (/Android|webOS|iPhone|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) && (window.innerWidth <= 575 || window.innerWidth >= 575)) {
      // some code..
      this.device = true;
    } else {
      this.device = false;
    }
    this.getFacilitiesByUserType();
  }

  get f() { return this.FilterForm.controls; };

  loadInitialDateRange() {
    let from_date: any = this.datePipe.transform(moment().startOf('week').toDate(), 'yyyy-MM-dd');
    let to_date: any = this.datePipe.transform(moment().endOf('week').toDate(), 'yyyy-MM-dd');
    this.dateRangeLable = { "startDate": new Date(from_date), "endDate": new Date(to_date) };
    this.daterange.start = new Date(moment().startOf('week').toDate());
    this.daterange.end = new Date(moment().endOf('week').toDate());
    this.startDate = new Date(this.daterange.start);
    this.startDateP = new Date(this.daterange.start);
    this.firstDate = new Date(this.daterange.start);
    this.lastDate = new Date(this.daterange.end);
    this.month = new Date(this.daterange.start).getMonth() + 1;
    this.year = new Date(this.daterange.start).getFullYear();
    this.listOfColumns = this.viewType == "Week" ? this.getWeekDates(new Date(this.daterange.start), new Date(this.daterange.end)) : this.getMonthDates(new Date(this.daterange.start), new Date(this.daterange.end));
  }

  selectedDate(value: any) {
    if (value.startDate && value.endDate) {
      this.daterange.start = new Date(value.startDate.$d);
      this.daterange.end = new Date(value.endDate.$d)
      this.daterange.label = this.datePipe.transform(new Date(value.startDate.$d), 'MM/dd/yyyy') + ' - ' + this.datePipe.transform(new Date(value.endDate.$d), 'MM/dd/yyyy');
      this.arrivalLabel = this.daterange.label;
      this.arrivalDateVal = false;
      this.startDate = new Date(value.startDate.$d);
      this.endDate = new Date(value.endDate.$d);
      this.firstDate = new Date(value.startDate.$d);
      this.lastDate = new Date(value.endDate.$d);
      this.listOfPatients = [];
      this.listOfColumns = this.viewType == "Week" ? this.getWeekDates(new Date(value.startDate.$d), new Date(value.endDate.$d)) : this.getMonthDates(new Date(value.startDate.$d), new Date(value.endDate.$d));
    }
  }
  selectedView(value: any) {
    this.listOfPatients = [];
    this.listOfColumns = this.viewType=="Week"? this.getWeekDates(new Date(this.firstDate), new Date(this.lastDate)) : this.getMonthDates(new Date(this.firstDate), new Date(this.lastDate));
  }
  getFacilitiesByUserType() {
    this.commonServ.getFacilitiesByUserType('COORDINATOR').subscribe((p: any) => {
      this.listOfFacilities = p;
      if (this.listOfFacilities.length > 0) {
        this.FilterForm.get('ddlFacility')?.setValue(this.listOfFacilities[0].facilityName);
        this.getGroupsByFacility(this.listOfFacilities[0].facilityName);
        this.fetchData(1);
      }
    });
  }

  getGroupsByFacility(facillity) {
    this.commonServ.startLoading();
    this.reptServ.getGroupNameByFacility(facillity).subscribe((p: any) => {
      this.FilterForm.get('ddlGroups')?.setValue('All');
      this.listOfGroups = [];
      this.listOfGroups = p;
      this.commonServ.stopLoading();
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  getPhysiciansByGroup() {
    let group_name: string = $('#ddlGroups option:selected').text().trim();
    this.commonServ.startLoading();
    this.reptServ.getPhysiciansByGroup(group_name).subscribe((p: any) => {
      this.commonServ.stopLoading();
      this.FilterForm.get('ddlPhysician')?.setValue('All');
      this.listOfPhysicians = [];
      this.listOfPhysicians = p;
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  fetchData(pno) {
    this.commonServ.startLoading();
    let dateRanges = this.FilterForm.value.selectedRange;
    this.request.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.Formatted_EncounterSeenStartDate = this.encrDecr.set(this.datePipe.transform(dateRanges?dateRanges[0] : this.startDate, 'yyyy-MM-dd'));
    this.request.Formatted_EncounterSeenEndDate = this.encrDecr.set(this.datePipe.transform(dateRanges?dateRanges[1]:this.endDate, 'yyyy-MM-dd'));
    this.request.GroupId = this.encrDecr.set(this.FilterForm.value.ddlGroups);
    this.request.PhysicianMailId = this.encrDecr.set(this.FilterForm.value.ddlPhysician);
    this.request.Rule1 = this.encrDecr.set(this.FilterForm.value.chkRule1);
    this.request.Rule2 = this.encrDecr.set(this.FilterForm.value.chkRule2);
    this.request.Rule3 = this.encrDecr.set(this.FilterForm.value.chkRule3);
    this.request.Rule4 = this.encrDecr.set(this.FilterForm.value.chkRule4);
    this.coordinatorServ.getDataCalendarView(this.request).subscribe((p: any) => {
      this.submitted = false;
      this.arrivalDateVal = false;
      this.listOfPatients = p.patientDate;
      
      this.request = {};
      if(new Date(this.lastDate.setHours(0,0,0,0))<= new Date(this.endDate.setHours(0,0,0,0)))    
        this.isNextView = true;
      else
       this.isNextView = false;

      if(new Date(this.firstDate.setHours(0,0,0,0)) >= new Date(this.startDate.setHours(0,0,0,0)))
        this.isPreviousView = true;
      else
       this.isPreviousView = false;

      this.commonServ.stopLoading();
    }, error => { 
      console.error(error.status); }
    )
  }

  fetchMonthViewData() {
    this.commonServ.startLoading();
    this.Patients =[];
    this.request.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.Month = this.encrDecr.set(this.month);
    this.request.Year = this.encrDecr.set(this.year);
    this.request.GroupId = this.encrDecr.set(this.FilterForm.value.ddlGroups);
    this.request.PhysicianMailId = this.encrDecr.set(this.FilterForm.value.ddlPhysician);
    this.request.PatientAccountNumber = this.encrDecr.set(this.PatientAccountNumber);
    this.coordinatorServ.getPatientDataMonthView(this.request).subscribe((p: any) => {
      this.submitted = false;
      this.arrivalDateVal = false;
      this.Patients = p;
      this.request = {};
      this.commonServ.stopLoading();
    }, error => { console.error(error.status); }
    )
  }

  getCalendarDataByPatient(AccountNumber,PatientName)
  {
    this.PatientAccountNumber = AccountNumber;
    this.PatientName = PatientName;
    this.startDateP = new Date(this.startDate);
    this.month = this.startDateP.getMonth()+1;
    this.year = this.startDateP.getFullYear();
    this.fetchMonthViewData();
    $('#staticBackdrop').modal('show');
  }

  getMonthDates(sDate,eDate) {  
    const week: any[] = [];
    const firstDay = new Date(sDate);    
    this.endDate = new Date(eDate);  
    firstDay.setDate(sDate.getDate()); 
    const dayLbl = new Date(this.endDate);    
    this.endDate = this.viewType=="Week" ?  new Date(dayLbl.setDate(firstDay.getDate() + 6)) : new Date(new Date(firstDay).setMonth(firstDay.getMonth() + 1));
    
    
    this.endDate =  new Date(this.endDate) < new Date(this.lastDate) ? new Date(this.endDate) : new Date(this.lastDate);

    this.arrivalLabel = this.datePipe.transform(firstDay, 'MM/dd/yyyy') + ' - ' + this.datePipe.transform(this.endDate, 'MM/dd/yyyy');
    let currentDate = new Date(firstDay);    
   
    
    while (currentDate <= this.endDate) {
      let colDay = currentDate.toLocaleDateString('en-US', { day: '2-digit' });
      let colMonth = currentDate.toLocaleDateString('en-US', { month: '2-digit' });
      let colYear = currentDate.toLocaleDateString('en-US', { year: 'numeric' });
      let wdate = this.formatDate(currentDate);
      let item: any = {
        day: wdate.split(' ')[0],
        monthyear: wdate.split(' ')[2] +' '+ wdate.split(' ')[3],
        weekday: wdate.split(' ')[1],
        cptColoName: 'CPTS_PHY_' + colYear + colMonth + colDay,
      };
      week.push(item);
      currentDate.setDate(currentDate.getDate() + 1);
    }   
    return week;
  }


  getWeekDates(sDate,eDate) {

    const week: any[] = [];
    const firstDayOfWeek = new Date(sDate);
    const dayLbl = new Date(firstDayOfWeek);  
    this.endDate = new Date(dayLbl.setDate(firstDayOfWeek.getDate() + 6));
    this.arrivalLabel = this.datePipe.transform(firstDayOfWeek, 'MM/dd/yyyy') + ' - ' + this.datePipe.transform(this.endDate, 'MM/dd/yyyy');
 
    this.endDate =  new Date(this.endDate) < new Date(this.lastDate) ? this.endDate : new Date(this.lastDate);
    
    let currentDate = new Date(firstDayOfWeek);
    while (currentDate <= this.endDate) {
      let colDay = currentDate.toLocaleDateString('en-US', { day: '2-digit' });
      let colMonth = currentDate.toLocaleDateString('en-US', { month: '2-digit' });
      let colYear = currentDate.toLocaleDateString('en-US', { year: 'numeric' });
      let wdate = this.formatDate(currentDate);
      let item: any = {
        day: wdate.split(' ')[0],
        monthyear: wdate.split(' ')[2] +' '+ wdate.split(' ')[3],
        weekday: wdate.split(' ')[1],
        cptColoName: 'CPTS_PHY_' + colYear + colMonth + colDay,
      };
      week.push(item);
      currentDate.setDate(currentDate.getDate() + 1);
    }   
    return week;
  }

  formatDate(date) {
    date = new Date(date);
    const optionDay = { weekday: 'short', day: 'numeric' };
    const optionMonth = {month: 'short', year:'numeric'}
   
    const weekDay = date.toLocaleDateString('en-US', optionDay);
    const monthYear = date.toLocaleDateString('en-US', optionMonth);
    return weekDay+' '+monthYear;
    
  }

  previous() {   

    this.startDate.setDate( this.viewType=="Week" ? new Date(this.startDate).getDate() - 7 : new Date(this.startDate).getDate() - 30);
    this.endDate.setDate(this.viewType=="Week" ?  new Date(this.endDate).getDate()- 7  : new Date(this.endDate).getDate() - 30);
   
    this.startDate =  new Date(this.firstDate) > new Date(this.startDate) ?  new Date(this.firstDate) :   new Date(this.startDate);
    // this.endDate =  this.lastDate > new Date(this.endDate) ? this.lastDate : new Date(this.endDate);
    this.listOfPatients = [];
    this.listOfColumns = this.getMonthDates(new Date(this.startDate),new Date(this.endDate));
    this.fetchData(1);
  };

  next() {

    this.startDate.setDate( this.viewType=="Week" ? new Date(this.startDate).getDate()  + 7 : new Date(this.startDate).getDate() + 30);
    this.endDate.setDate(this.viewType=="Week" ?  new Date(this.endDate).getDate() + 7  : new Date(this.endDate).getDate() + 30);
   
    //this.startDate =  this.firstDate < new Date(this.startDate) ?  new Date(this.startDate) : new Date(this.firstDate);
    this.endDate =  new Date(this.lastDate) < new Date(this.endDate) ? new Date(this.lastDate) : new Date(this.endDate);
    this.listOfPatients = [];
    this.listOfColumns = this.getMonthDates(new Date(this.startDate),new Date(this.endDate));
    this.fetchData(1);
  };

 
  
  previousP() {   
    if(this.startDateP.getMonth()==0)
    {
      this.startDateP = new Date(this.year - 1,11,1) ;      
    }
    else{
      this.startDateP = new Date(this.startDateP.getFullYear(), this.startDateP.getMonth()-1,1);    
    }    
    this.month = this.startDateP.getMonth()+1;
    this.year = this.startDateP.getFullYear();
    this.fetchMonthViewData();
  };

  nextP() {
    if(this.startDateP.getMonth()+1==12)
      {
        this.startDateP = new Date((this.startDateP.getFullYear()+1),0,1) 
      }
      else{
        this.startDateP = new Date(this.startDateP.getFullYear(),this.startDateP.getMonth()+1,1) 
      }    
      this.month = this.startDateP.getMonth()+1;
      this.year = this.startDateP.getFullYear();
      this.fetchMonthViewData();
  };


  cptSplit(cpt) {
    let reqCpt: string = '';
    if (cpt) {
      let cptsp = cpt.split('||||');
      cptsp.forEach(ct => {
        reqCpt += reqCpt + ct.split('-')[0] + ',';
      });
    }
    return reqCpt;
  }
  showPatientCalendar()
  { 
    this.isPatientView = false;
  }
  chkChangeEvent(event,ruleId) {   
    this.fetchData(1);
    } 
}
