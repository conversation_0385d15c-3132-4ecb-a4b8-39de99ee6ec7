<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">

        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new px-0 py-2" style="background: #0169ab;">
                    <div class="align-items-center mx-0 row">
                        <div class="col-12 col-md-8 py-1 pl-2 pr-1 position-relative">
                            <div class="input-group">
                                <input type="text" class="form-control small" maxlength="1000"
                                    placeholder="Search for..." aria-label="Search" aria-describedby="basic-addon2"
                                    [(ngModel)]="search" [ngModelOptions]="{standalone: true}">
                                <div class="input-group-append">
                                    <button class="bg-gradient-light btn text-dark" type="button">
                                        <i class="fas fa-search fa-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-2 py-1 px-1">
                            <button class="btn btn-outline-info btn-block"
                                [routerLink]="['/usermanagement/edit-user-details']"
                                [state]="{user:addUserObject,type:'add',filterObj:{p:p,searchKey:search}}"><i
                                    class="fas fa-user-plus"></i> Add User</button>
                        </div>
                        <div class="col-12 col-md-2 py-1 pl-1 pr-2" *ngIf="adminModuleAccess=='YES'">
                            <button class="btn btn-outline-info btn-block" (click)="openCopyProfile()"
                                data-toggle="modal" data-target="#copyProfilepopup"><i class="far fa-copy"></i> Copy
                                Profile</button>
                        </div>
                    </div>
                </div>
                <!-- Card Body -->
                <div class="card-body p-2">
                    <div class="row mx-0">
                        <div class="col-12 px-0">

                            <div class="row m-auto">
                                <div class="col-sm-12 px-0">
                                    <table
                                        class="table table-hover table-striped table-bordered table-responsive-md m-auto">
                                        <thead class="text-info">
                                            <tr>
                                                <th class="text-center" scope="col">Name</th>
                                                <th class="text-center" scope="col">Provider Role(s)</th>
                                                <th class="text-center" class="width-11" scope="col">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr
                                                *ngFor="let item of listOfUsers|gridFilter:{display_name:search,email_id:search}:false |paginate:{ id:'loginID',itemsPerPage:10,currentPage:p};let i=index">

                                                <td>
                                                    <div class="user-name">
                                                        <h6 class="text-secondary d-inline-block font-weight-bold">
                                                            {{item.display_name}}</h6>
                                                        <p class="text-info">{{item.email_id}}</p>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="d-inline-block delete"
                                                        *ngIf="item.physicianModuleAccess=='YES'">
                                                        <div class="alert alert-light text-white bg-secondary alert-dismissible  
                                                                                                        fade alert-content show"
                                                            tooltip="{{item.physicianFacilities}}">
                                                            <strong>Physician</strong>
                                                            <button type="button" class="btn close"
                                                                *ngIf="adminModuleAccess=='YES'"
                                                                (click)="deassignRoleFacilityOrGroup(item.email_id,'PHYSICIAN')">
                                                                <span aria-hidden="true">×</span></button>
                                                        </div>
                                                    </div>
                                                    <div class="d-inline-block delete"
                                                        *ngIf="item.coordinatorModuleAccess=='YES'">
                                                        <div class="alert alert-light text-white bg-secondary alert-dismissible  
                                                                                                        fade alert-content show"
                                                            tooltip="{{item.coordinatorFacilities}}">
                                                            <strong>Coordinator</strong>
                                                            <button type="button" class="btn close"
                                                                *ngIf="adminModuleAccess=='YES'"
                                                                (click)="deassignRoleFacilityOrGroup(item.email_id,'COORDINATOR')">
                                                                <span aria-hidden="true">×</span></button>
                                                        </div>
                                                    </div>
                                                    <div class="d-inline-block delete"
                                                        *ngIf="item.auditorModuleAccess=='YES'">
                                                        <div class="alert alert-light text-white bg-secondary alert-dismissible  
                                                                                                        fade alert-content show"
                                                            tooltip="{{item.auditorFacilities}}">
                                                            <strong>Auditor</strong>
                                                            <button type="button" class="btn close"
                                                                *ngIf="adminModuleAccess=='YES'"
                                                                (click)="deassignRoleFacilityOrGroup(item.email_id,'AUDITOR')">
                                                                <span aria-hidden="true">×</span></button>
                                                        </div>
                                                    </div>
                                                    <div class="d-inline-block delete"
                                                        *ngIf="item.billerModuleAccess=='YES'">
                                                        <div class="alert alert-light text-white bg-secondary alert-dismissible  
                                                                                                        fade alert-content show"
                                                            tooltip="{{item.billerFacilities}}">
                                                            <strong>Biller</strong>
                                                            <button type="button" *ngIf="adminModuleAccess=='YES'"
                                                                class="btn close"
                                                                (click)="deassignRoleFacilityOrGroup(item.email_id,'BILLER')">
                                                                <span aria-hidden="true">×</span>
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="d-inline-block delete"
                                                        *ngIf="item.userManagementAccess=='YES'">
                                                        <div class="alert alert-light text-white bg-secondary alert-dismissible  
                                                                                                        fade alert-content show"
                                                            tooltip="{{item.userManagementFacilities}}">
                                                            <strong>User Management</strong>
                                                            <button type="button" class="btn close"
                                                                *ngIf="adminModuleAccess=='YES'"
                                                                (click)="deassignRoleFacilityOrGroup(item.email_id,'USERMANAGEMENT')">
                                                                <span aria-hidden="true">×</span></button>
                                                        </div>
                                                    </div>
                                                    <div class="d-inline-block delete"
                                                        *ngIf="item.adminModuleAccess=='YES'">
                                                        <div
                                                            class="alert alert-light text-white bg-secondary alert-dismissible fade alert-content show">
                                                            <strong>Administrator</strong>
                                                            <button type="button" class="btn close"
                                                                *ngIf="adminModuleAccess=='YES'"
                                                                (click)="deassignRoleFacilityOrGroup(item.email_id,'ADMIN')">
                                                                <span aria-hidden="true">×</span></button>
                                                        </div>
                                                    </div>
                                                    <div class="d-inline-block delete"
                                                        *ngIf="item.residentModuleAccess=='YES'">
                                                        <div class="alert alert-light text-white bg-secondary alert-dismissible  
                                                                                                        fade alert-content show"
                                                            tooltip="{{item.residentFacilities}}">
                                                            <strong>Resident</strong>
                                                            <button type="button" class="btn close"
                                                                *ngIf="adminModuleAccess=='YES'"
                                                                (click)="deassignRoleFacilityOrGroup(item.email_id,'RESIDENT')">
                                                                <span aria-hidden="true">×</span></button>
                                                        </div>
                                                    </div>
                                                    <div class="d-inline-block delete"
                                                        *ngIf="item.allowPhysicianRounds=='YES'">
                                                        <div
                                                            class="alert alert-light text-white bg-secondary alert-dismissible fade alert-content show">
                                                            <strong>Mobile Access</strong>
                                                            <button type="button" class="btn close"
                                                                *ngIf="adminModuleAccess=='YES'"
                                                                (click)="deassignRoleFacilityOrGroup(item.email_id,'MOBILE')">
                                                                <span aria-hidden="true">×</span></button>
                                                        </div>
                                                    </div>

                                                </td>
                                                <td class="text-center">
                                                    <button class="btn btn-sm btn-outline-info mb-1 text-nowrap"
                                                        [routerLink]="['/usermanagement/add-modify-role']"
                                                        [state]="{user:item,filterObj:{p:p,searchKey:search}}"><i
                                                            class="fas fa-user"></i> Assign Role</button>
                                                    <button class="btn btn-sm btn-outline-info mb-1 ml-1 text-nowrap"
                                                        [routerLink]="['/usermanagement/edit-user-details']"
                                                        [state]="{user:item,type:'edit',filterObj:{p:p,searchKey:search}}"><i
                                                            class="fas fa-edit"></i> Edit User</button>
                                                </td>
                                            </tr>

                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="row m-auto">
                                <div class="col-sm-12 col-md-5">
                                </div>
                                <div class="col-sm-12 col-md-7">
                                    <div class="dataTables_paginate paging_simple_numbers float-right"
                                        id="dataTable_paginate">
                                        <pagination-controls id='loginID' previousLabel="Previous" nextLabel="Next"
                                            screenReaderPaginationLabel="Pagination" screenReaderPageLabel="page"
                                            screenReaderCurrentLabel="You're on page" (pageChange)="p=$event"
                                            (pageBoundsCorrection)="p=$event">
                                        </pagination-controls>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>

                </div>

            </div>
        </div>
        <!-- Area Chart Ends -->
    </div>
</div>
<app-copyprofile></app-copyprofile>

<!-- Begin Page Content Ends -->