import { Component, OnInit } from '@angular/core';
import { CommonService } from 'src/app/services/common/common.service';
import { ReportsService } from 'src/app/services/reports/reports.service';
import { AppComponent } from 'src/app/app.component';
import { ExcelServices } from 'src/app/services/common/ExcelService';
declare let $: any;

@Component({
  selector: 'app-alerts-report',
  templateUrl: './alerts-report.component.html',
  styles: []
})
export class AlertsReportComponent implements OnInit {
  public listOfFacilities: Array<any> = [];
  public listOfReports: Array<any> = [];
  public p: number;
  public searchByName: string;
  public submitted: boolean = false;
  public img1: string = '.././../../assets/img/excel.png';

  constructor(private readonly reportsServ: ReportsService, private readonly commonServ: CommonService, private readonly appComp: AppComponent, private readonly excelService: ExcelServices) { }

  ngOnInit() {
    this.appComp.loadPageName('Monitor Alerts Report', 'reportsTab');
    this.getFacilities();
  }

  getFacilities() {
    this.commonServ.getFacilities().subscribe((p: any) => {
      this.listOfFacilities = p;
    }, error => { console.error(error.status); });
  }

  exportAsXLSX(): void {
    let fileName = "";
    fileName = $('#ddlFacility option:selected').text();
    this.excelService.exportAsExcelFile(this.listOfReports, fileName);
  }

  getAlertReportData(valu) {
    if (valu != '') {
      this.submitted = true;
      this.commonServ.startLoading();
      this.reportsServ.getMonitorAlerts_Report(valu).subscribe((p: any) => {
        if (p) {
          this.listOfReports = p;
        }
        this.commonServ.stopLoading();
      }, error => {
        this.commonServ.stopLoading();
      });
    }
    else
      this.listOfReports = [];
  }

}

