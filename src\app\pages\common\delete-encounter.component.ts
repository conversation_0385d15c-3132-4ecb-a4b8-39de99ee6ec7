import { Component, Input } from '@angular/core';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { CommonService } from 'src/app/services/common/common.service';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'app-delete-encounter',
  templateUrl: './delete-encounter.component.html',
  styles: []
})
export class DeleteEncounterComponent {
  @Input() encounterObj:any;
  @Input() PatientObject: any;
  @Input() userType:string;
  public request:any={};
  public deleteReason:string="";
  constructor(private readonly commonServ:CommonService,private readonly encrDecr: EncrDecrServiceService, public datepipe: DatePipe) { }


  confirmDeleteEncounter(eObj){
    this.commonServ.startLoading();
    this.request.sEncounter_Id=this.encrDecr.set(eObj.encounteR_ID);
    this.request.Role=this.encrDecr.set(this.userType);
    this.request.Deleted_Reason=this.encrDecr.set(this.deleteReason);
    this.commonServ.deleteEncounter(this.request,this.userType).subscribe((p: any) => {
      this.request={};
      if(p>0)
      {
        eObj.encounteR_ID="";
        this.deleteReason = "";
        if(this.userType=='COORDINATOR'){
          this.PatientObject.last_activity_date = this.datepipe.transform(new Date(), 'MM/dd/yyyy h:mm:ss a');
        }
      }
      this.commonServ.stopLoading();
    },error => {
      this.request={};
      this.commonServ.stopLoading();
      console.error(error.status);
    });
  }
  handleClear(){
    this.deleteReason = "";
  }

}
