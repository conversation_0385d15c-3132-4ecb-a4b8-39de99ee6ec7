import { Component, OnInit } from '@angular/core';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { forkJoin } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
declare let $: any;

@Component({
  selector: 'app-attachment',
  templateUrl: './attachment.component.html',
  styles: [
  ]
})
export class AttachmentComponent implements OnInit {
  public PatientObject: any = {};
  public lisfOfAttachments: Array<any> = [];
  public FilterForm: FormGroup;
  public listOfFacilities: Array<any> = [];
  public submitted: boolean = false;
  public request: any = {};
  constructor(private readonly appComp: AppComponent, private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService
    , private readonly toastr: ToastrService, private readonly fb: FormBuilder) { }

  ngOnInit() {
    this.appComp.loadPageName('Attachments', 'AttachmentsTab');
    this.getFacilities();
    this.FilterForm = this.fb.group({
      ddlFacility: ['', Validators.required],
      txtAcNo: ['', [Validators.required, Validators.maxLength(50)]]
    });
  }
  get f() { return this.FilterForm.controls; }

  getFacilities() {
    this.commonServ.getFacilities().subscribe((p: any) => {
      this.listOfFacilities = p;
    }, error => { console.error(error.status); });
  }


  getPatientDetails() {
    this.submitted = true;
    if (this.FilterForm.invalid) {
      return;
    }
    this.commonServ.startLoading();
    let request: any = {
      PATIENTACCOUNTNUMBER: this.encrDecr.set(this.FilterForm.value.txtAcNo),
      FACILITYNAME: this.encrDecr.set(this.FilterForm.value.ddlFacility)
    };
    let request2: any = {
      AccountNumber: this.encrDecr.set(this.FilterForm.value.txtAcNo),
      FacilityName: this.encrDecr.set(this.FilterForm.value.ddlFacility)
    };

    forkJoin(
      this.commonServ.getPatientDetails(request),
      this.commonServ.getAttachments(request2)
    ).subscribe((p: any) => {
      this.submitted = false;
      if (p[0] != null) {
        this.PatientObject = p[0];
        this.lisfOfAttachments = p[1];
        $("#attachment").modal('show');
        this.FilterForm.get('txtAcNo')?.setValue('');
        this.FilterForm.get('ddlFacility')?.setValue('');
        this.commonServ.stopLoading();
      }
      else {
        this.FilterForm.get('txtAcNo')?.setValue('');
        this.commonServ.stopLoading();
        this.toastr.error("No Patient Exist with this Account Number.");
      }
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  updateAttCount(patient) {
    console.log(patient);
  }

}
