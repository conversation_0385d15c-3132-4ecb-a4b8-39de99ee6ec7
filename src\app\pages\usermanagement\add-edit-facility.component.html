<div class="modal fade" id="AddorEditFacility" tabindex="-1" maria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg">
        <div class="modal-content" [formGroup]="AddorEditFacilityForm">
            <div class="modal-header py-2">
                <h5 class="modal-title" id="exampleModalCenterTitle">Add/Edit Facility</h5>
                <i class="fa fa-times closePopup" data-dismiss="modal"></i>
            </div>
            <div class="modal-body p-md-2 p-1">
                <div class="row mx-auto py-md-4 py-2">
                    <div class="col-12 col-md-6">
                        <div class="form-group">
                            <label for="FacilityFullName">Facility Full Name</label>
                            <input class="form-control input-border" [(ngModel)]="facilityObj.facility_Full_Name"
                                type="text" formControlName="txtFacilityFullName" id="FacilityFullName"
                                placeholder="Facility Full Name">
                        </div>
                    </div>
                    <div class="col-12 col-md-6">
                        <div class="form-group">
                            <label for="FacilityName">Facility Code</label>
                            <input class="form-control input-border" maxlength="125"
                                [(ngModel)]="facilityObj.facilityName"
                                [ngClass]="{ 'is-invalid': submitted && f['txtFacilityName'].errors}" type="text"
                                formControlName="txtFacilityName" id="FacilityName" placeholder="Facility Name">
                        </div>
                    </div>
                    <div class="col-12 col-md-12">
                        <div class="form-group">
                            <label for="Address">Address</label>
                            <textarea class="form-control input-border" [(ngModel)]="facilityObj.facility_Address"
                                [ngClass]="{ 'is-invalid': submitted && f['txtAddress'].errors}"
                                formControlName="txtAddress" id="Address" placeholder="Address">
                      </textarea>
                        </div>
                    </div>
                    <div class="col-12 col-md-6">
                        <div class="form-group">
                            <label for="States">State</label>
                            <select [(ngModel)]="facilityObj.statE_NAME" class="form-control"
                                [ngClass]="{ 'is-invalid': submitted && f['ddlStates'].errors}"
                                formControlName="ddlStates" (change)="GetTimeZones($any($event.target).value)"
                                id="ddlStates">
                                <option value="">Choose States</option>
                                <option [value]="s.stateName" *ngFor="let s of listOfStates">
                                    <span>{{s.stateName}}</span>
                                </option>
                            </select>
                        </div>
                    </div>

                    <div class="col-12 col-md-6">
                        <div class="form-group">
                            <label for="City">City</label>
                            <input class="form-control input-border" maxlength="125" [(ngModel)]="facilityObj.city"
                                [ngClass]="{ 'is-invalid': submitted && f['txtCity'].errors}" type="text"
                                formControlName="txtCity" id="City" placeholder="City">
                        </div>
                    </div>

                    <div class="col-12 col-md-6">
                        <div class="form-group">
                            <label for="ZipCode">Zip Code</label>
                            <input class="form-control input-border" maxlength="5" [(ngModel)]="facilityObj.zip_Code"
                                [ngClass]="{ 'is-invalid': submitted && f['txtZipCode'].errors}" type="text"
                                formControlName="txtZipCode" id="ZipCode" placeholder="Zip Code">
                        </div>
                    </div>

                    <div class="col-12 col-md-6">
                        <div class="form-group">
                            <label for="TimeZone">Time Zone</label>
                            <select class="form-control disabled" [(ngModel)]="facilityObj.timezoneID"
                                [ngClass]="{ 'is-invalid': submitted && f['ddlTimeZone'].errors}"
                                formControlName="ddlTimeZone" id="ddlTimeZone">
                                <option [value]="t.timeZoneID" *ngFor="let t of listOfTimeZone">
                                    <span>{{t.timeZoneName}}</span>
                                </option>
                            </select>
                        </div>
                    </div>

                    <div class="col-12 col-md-12">
                        <div class="form-group">
                            <div class="form-check d-inline-block">
                                <input type="checkbox" id="IsPrimeHospital" [(ngModel)]="facilityObj.isPrimeHospital"
                                    formControlName="chkIsPrimeHospital">
                                <label for="IsPrimeHospital" class="ml-1">Is Prime Hospital</label>
                            </div>
                            <div class="form-check d-inline-block">
                                <input type="checkbox" id="IsAdmissionDateEdit"
                                    [(ngModel)]="facilityObj.isAdissionDateEdit" formControlName="chkAdmissionDateEdit">
                                <label for="IsAdmissionDateEdit" class="ml-1">Is Admission Date Edit</label>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-2">
                <button class="btn btn-outline-info float-right" *ngIf="facilityObj.facilityID == 0"
                    (click)="submittedFacility(facilityObj,'INSERT')">Submit</button>
                <button class="btn btn-outline-info float-right" *ngIf="facilityObj.facilityID != 0"
                    (click)="submittedFacility(facilityObj,'UPDATE')">Update</button>
                <button class="btn btn-outline-info float-right" (click)="ReloadFacilities()"
                    data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>