import { Component, OnInit } from '@angular/core';
import { CommonService } from 'src/app/services/common/common.service';
import { ReportsService } from 'src/app/services/reports/reports.service';
import { AppComponent } from 'src/app/app.component';
import { ExcelServices } from 'src/app/services/common/ExcelService';
import { DatePipe } from '@angular/common';
import * as moment from 'moment';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
declare let $: any;

@Component({
  selector: 'app-note-report',
  templateUrl: './note-report.component.html',
  styles: [
  ]
})
export class NoteReportComponent implements OnInit {
  public listOfFacilities: Array<any> = [];
  public listOfReports: Array<any> = [];
  public p: number;
  public searchByName: string;
  public submitted: boolean = false;
  public daterange: any = {};
  public arrival = '';
  public arrivalLabel: string = "";
  public arrivalDateVal: boolean = false;
  public img1: string = '.././../../assets/img/excel.png';
  startDate: any = moment().subtract(6, 'month').startOf('month');
  endDate: any = moment();
  updateDate: any = '';
  public ranges: any = {
    'Last 6 Months': [moment().subtract(6, 'month').startOf('month'), moment()],
    'Last Year': [moment().startOf('year').subtract(1, 'year'), moment().endOf('year').subtract(1, 'year')],
    'This Year': [moment().startOf('year'), moment()]
  };
  public dateRangeLable: any = '';

  constructor(private readonly reportsServ: ReportsService, private readonly commonServ: CommonService, private readonly appComp: AppComponent, private readonly excelService: ExcelServices
    , private readonly datePipe: DatePipe, private readonly encrDecr: EncrDecrServiceService) {
      this.loadInitialDateRange();
  }

  ngOnInit() {
    this.appComp.loadPageName('Notes Report', 'reportsTab');
    this.getAlertReportData();
  }
  loadInitialDateRange() {
    let from_date: any = this.datePipe.transform(moment().subtract(6, 'month').startOf('month').toDate(), 'yyyy-MM-dd');
    let to_date: any = this.datePipe.transform(moment().toDate(), 'yyyy-MM-dd');
    this.dateRangeLable = { "startDate": new Date(from_date), "endDate": new Date(to_date) };
    this.daterange.start = new Date(moment().subtract(6, 'month').startOf('month').toDate());
    this.daterange.end = new Date(moment().toDate());
    this.daterange.label = 'Last 6 Months Admission Data';
    this.arrival = this.datePipe.transform(this.daterange.start, 'MM/dd/yyyy') + ' - ' + this.datePipe.transform(this.daterange.end, 'MM/dd/yyyy');
    this.arrivalLabel = this.daterange.label;
  }
  selectedDate(value: any) {
    if (value.startDate && value.endDate) {
      this.daterange.start = new Date(value.startDate.$d);
      this.daterange.end = new Date(value.endDate.$d)
      this.daterange.label = this.datePipe.transform(new Date(value.startDate.$d), 'MM/dd/yyyy') + ' - ' + this.datePipe.transform(new Date(value.endDate.$d), 'MM/dd/yyyy');;
      this.arrival = this.datePipe.transform(this.daterange.start, 'MM/dd/yyyy') + ' - ' + this.datePipe.transform(this.daterange.end, 'MM/dd/yyyy');
      this.arrivalLabel = this.daterange.label;
      this.updateDate = JSON.stringify(value);
      this.arrivalDateVal = false;
      this.getAlertReportData();
    }
  }
  exportAsXLSX(): void {
    this.excelService.exportAsExcelFile(this.listOfReports, 'NotesReport_' + this.daterange.label);
  }

  getAlertReportData() {
    this.commonServ.startLoading();
    this.p = 1;
    let fromDate = this.datePipe.transform(this.daterange.start, 'yyyy-MM-dd');
    let toDate = this.datePipe.transform(this.daterange.end, 'yyyy-MM-dd');
    let request = {
      created_start_date: this.encrDecr.set(fromDate),
      created_end_date: this.encrDecr.set(toDate)
    }
    this.reportsServ.getNotesReport(request).subscribe((p: any) => {
      if (p) {
        this.listOfReports = p;
      }
      this.commonServ.stopLoading();
    }, error => {
      this.commonServ.stopLoading();
    });
  }

}
