import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { SubjectAccessComponent } from './subject-access.component';
import { NewPrivacyPolicyComponent } from './new-privacy-policy.component';

const routes: Routes = [
  { path: '', component: NewPrivacyPolicyComponent },
  { path: 'data-request-form', component: SubjectAccessComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PrivacyPolicyRoutingModule { }
