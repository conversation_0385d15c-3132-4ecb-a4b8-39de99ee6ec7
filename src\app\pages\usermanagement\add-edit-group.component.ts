import { Component, OnInit } from '@angular/core';
import { FormBuilder, Validators, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { UserManagementService } from 'src/app/services/user-management/userManagement.service';

declare let $;

@Component({
  selector: 'app-add-edit-group',
  templateUrl: './add-edit-group.component.html',
  styles: []
})
export class AddEditGroupComponent implements OnInit {
  public submitted: boolean = false;
  public listOfFacilities: Array<any> = [];
  public listOfUsersList: Array<any> = [];
  public listOfAssignedUsers: Array<any> = [];
  public listOfPhysicianList: Array<any> = [];
  public listOfCoordinatorList: Array<any> = [];
  public listOfAuditorsList: Array<any> = [];
  public listOfBillersList: Array<any> = [];
  public listOfResudentList: Array<any> = [];
  public listOfselectedPhysicians: Array<any> = [];
  public listOfSelectedCoordinator: Array<any> = [];
  public listOfSelectedAuditors: Array<any> = [];
  public listOfSelectedBillers: Array<any> = [];
  public listOfSelectedResudents: Array<any> = [];
  public listofGroupHead: Array<any> = [];
  public addeditgroupForm: FormGroup;
  public mDdlPhysicians: any = {};
  public mDdlCoordinator: any = {};
  public mDdlAuditor: any = {};
  public mDdlBiller: any = {};
  public mDdlResident: any = {};
  public groupItem: any = {};
  public request: any = {};
  public filterObj: any = {};

  constructor(private readonly userSer: UserManagementService, private readonly commonServ: CommonService, private readonly fb: FormBuilder, private readonly encrDecr: EncrDecrServiceService,
    private readonly toastr: ToastrService, private readonly router: Router) { }

  ngOnInit(): void {
    this.filterObj = history.state.filterObj;
    this.groupItem = history.state.group;
    if (this.groupItem.status == 'In Active') {
      this.groupItem.status = true;
    } else {
      this.groupItem.status = false;
    }

    this.addeditgroupForm = this.fb.group({
      txtgroupName: ['', Validators.required],
      ddlFacility: [''],
      ddlPhysicians: [''],
      ddlCoordinator: [''],
      ddlAuditor: [''],
      ddlBiller: [''],
      ddlResident: [''],
      chkspecilist: [false],
      chkIsAlertCheck: [false],
      chkInActive: [false],
      ddlGroupHead: ['', Validators.required],
      chkCoordinatorAppRqrd: [false],
      chkIsAuditorApp: [false],
      ddlIntegrationType: ['']

    });
    this.mDdlPhysicians = {
      singleSelection: false,
      idField: 'userid',
      textField: 'username',
      enableCheckAll: false,
      itemsShowLimit: 20,
      allowSearchFilter: true
    };
    this.mDdlCoordinator = {
      singleSelection: false,
      idField: 'userid',
      textField: 'username',
      enableCheckAll: false,
      itemsShowLimit: 20,
      allowSearchFilter: true
    };
    this.mDdlAuditor = {
      singleSelection: false,
      idField: 'userid',
      textField: 'username',
      enableCheckAll: false,
      itemsShowLimit: 20,
      allowSearchFilter: true
    };
    this.mDdlBiller = {
      singleSelection: false,
      idField: 'userid',
      textField: 'username',
      enableCheckAll: false,
      itemsShowLimit: 20,
      allowSearchFilter: true
    };
    this.mDdlResident = {
      singleSelection: false,
      idField: 'userid',
      textField: 'username',
      enableCheckAll: false,
      itemsShowLimit: 20,
      allowSearchFilter: true
    };
    this.fetchFacilities();

    if (this.groupItem.groupID != 0) {
      this.GetAssignedandUnAssigneduser();
    }
  }

  get f() { return this.addeditgroupForm.controls; }
  fetchFacilities() {

    this.commonServ.startLoading();
    this.userSer.FetchGroups().subscribe((p: any) => {
      this.listOfFacilities = p.facilityReqorResps;
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }
  GetUsersbyfacility(facilityName) {

    this.commonServ.startLoading();
    this.userSer.GetUsersbyFacilityGroupWise($('#ddlFacility option:selected').text()).subscribe((p: any) => {
      this.listOfUsersList = p.usersListbyFacilities;
      if (this.listOfUsersList.length > 0) {
        this.listOfPhysicianList = this.listOfUsersList.filter(x => x.physicianrole == 'YES');
        this.listOfCoordinatorList = this.listOfUsersList.filter(x => x.coordinatorrole == 'YES');
        this.listOfAuditorsList = this.listOfUsersList.filter(x => x.auditorrole == 'YES');
        this.listOfBillersList = this.listOfUsersList.filter(x => x.billerrole == 'YES');
        this.listOfResudentList = this.listOfUsersList.filter(x => x.residentrole == 'YES');
        this.listofGroupHead = this.listOfUsersList.filter(x => x.physicianrole == 'YES');
      }
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  GetAssignedandUnAssigneduser() {

    this.commonServ.startLoading();
    this.userSer.GetAssignedUsersbyGroup(this.groupItem.groupID).subscribe((p: any) => {
      this.listOfUsersList = p.usersListbyFacilities;
      this.listOfAssignedUsers = p.selectedUserlistbyGroup;
      if (this.listOfUsersList.length > 0) {
        this.listOfPhysicianList = this.listOfUsersList.filter(x => x.physicianrole == 'YES');
        this.listOfCoordinatorList = this.listOfUsersList.filter(x => x.coordinatorrole == 'YES');
        this.listOfAuditorsList = this.listOfUsersList.filter(x => x.auditorrole == 'YES');
        this.listOfBillersList = this.listOfUsersList.filter(x => x.billerrole == 'YES');
        this.listOfResudentList = this.listOfUsersList.filter(x => x.residentrole == 'YES');
      }
      if (this.listOfAssignedUsers.length > 0) {
        this.listOfselectedPhysicians = this.listOfAssignedUsers.filter(x => x.physicianrole == 'YES');
        this.listOfSelectedCoordinator = this.listOfAssignedUsers.filter(x => x.coordinatorrole == 'YES');
        this.listOfSelectedAuditors = this.listOfAssignedUsers.filter(x => x.auditorrole == 'YES');
        this.listOfSelectedBillers = this.listOfAssignedUsers.filter(x => x.billerrole == 'YES');
        this.listOfSelectedResudents = this.listOfAssignedUsers.filter(x => x.residentrole == 'YES');
        this.listofGroupHead = this.listOfAssignedUsers.filter(x => x.physicianrole == 'YES');
      }
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  InsertGroup(groupobj) {
    this.submitted = true;
    if (this.addeditgroupForm.invalid) {
      return;
    }

    this.commonServ.startLoading();
    this.request.GroupName = this.encrDecr.set(this.addeditgroupForm.value.txtgroupName);
    this.request.FacilityName = this.encrDecr.set($('#ddlFacility option:selected').text());
    this.request.strFacilityID = this.encrDecr.set(this.addeditgroupForm.value.ddlFacility);
    this.request.strGroupHead = this.encrDecr.set(this.addeditgroupForm.value.ddlGroupHead);
    this.request.Flag = this.encrDecr.set("INSERT");
    this.request.strSpecility = this.encrDecr.set(this.addeditgroupForm.value.chkspecilist);
    this.request.ChkIsAlertCheck = this.encrDecr.set(this.addeditgroupForm.value.chkIsAlertCheck);
    this.request.SelectedPhysicians = this.listOfselectedPhysicians;
    this.request.SelectedCoordinators = this.listOfSelectedCoordinator;
    this.request.SelectedAuditors = this.listOfSelectedAuditors;
    this.request.SelectedBillers = this.listOfSelectedBillers;
    this.request.SelectedResidents = this.listOfSelectedResudents;
    this.request.strCoordinatorAppRequired = this.encrDecr.set(this.addeditgroupForm.value.chkCoordinatorAppRqrd);
    this.request.strIsAuditRequired = this.encrDecr.set(this.addeditgroupForm.value.chkIsAuditorApp);
    this.request.IntegrationType = this.encrDecr.set(this.addeditgroupForm.value.ddlIntegrationType);

    this.userSer.InsertOrUpdateGroupDetails(this.request).subscribe((p: any) => {
      this.request = {};
      this.submitted = false;
      this.commonServ.stopLoading();
      if (p == 'SUCCESS') {
        this.toastr.success('Group Details added Successfully.', '', { timeOut: 2500 });
        this.router.navigate(['/usermanagement/add-modify-groups'], { state: { filterObj: this.filterObj } });

      }
      else {
        this.toastr.error('Group already exist');
      }

    },
      error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });

  }

  UpdateGroup(groupobj) {

    this.submitted = true;
    if (this.addeditgroupForm.invalid) {
      return;
    }

    this.commonServ.startLoading();
    this.request.GroupName = this.encrDecr.set(this.addeditgroupForm.value.txtgroupName);
    this.request.strGroupID = this.encrDecr.set(this.groupItem.groupID);
    this.request.FacilityName = this.encrDecr.set($('#ddlFacility option:selected').text());
    this.request.strFacilityID = this.encrDecr.set(this.addeditgroupForm.value.ddlFacility);
    this.request.strGroupHead = this.encrDecr.set(this.addeditgroupForm.value.ddlGroupHead);
    this.request.Flag = this.encrDecr.set("UPDATE");
    if (!this.addeditgroupForm.value.chkInActive) {
      this.request.Status = this.encrDecr.set("Active");
    }
    else { this.request.Status = this.encrDecr.set("In Active"); }
    this.request.strSpecility = this.encrDecr.set(this.addeditgroupForm.value.chkspecilist);
    this.request.ChkIsAlertCheck = this.encrDecr.set(this.addeditgroupForm.value.chkIsAlertCheck);
    this.request.SelectedPhysicians = this.listOfselectedPhysicians;
    this.request.SelectedCoordinators = this.listOfSelectedCoordinator;
    this.request.SelectedAuditors = this.listOfSelectedAuditors;
    this.request.SelectedBillers = this.listOfSelectedBillers;
    this.request.SelectedResidents = this.listOfSelectedResudents;
    this.request.strCoordinatorAppRequired = this.encrDecr.set(this.addeditgroupForm.value.chkCoordinatorAppRqrd);
    this.request.IntegrationType = this.encrDecr.set(this.addeditgroupForm.value.ddlIntegrationType);
    this.request.strIsAuditRequired = this.encrDecr.set(this.addeditgroupForm.value.chkIsAuditorApp);
    this.userSer.InsertOrUpdateGroupDetails(this.request).subscribe((p: any) => {
      this.request = {};
      this.submitted = false;
      this.commonServ.stopLoading();
      if (p == 'SUCCESS') {
        this.toastr.success('Group Details updated Successfully.', '', { timeOut: 2500 });
        this.router.navigate(['/usermanagement/add-modify-groups'], { state: { filterObj: this.filterObj } });
      }
      else {
        this.toastr.error('Group already exist');
      }

    },
      error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });

  }

}
