import { Component, OnInit } from '@angular/core';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { PhysicianService } from 'src/app/services/physician/physician.service';

@Component({
  selector: 'app-unbilled-encounters',
  templateUrl: './unbilled-encounters.component.html',
  styles: [
  ]
})
export class UnbilledEncountersComponent implements OnInit {
  public listOfPatients: Array<any> = [];
  public msrlistOfPatients: Array<any> = [];
  public searchKey: string = '';
  public p: number = 1;
  public request: any = {};
  device = false;
  public filterObj: any = {};
  orderBy = 'desc';
  orderByPatient = 'desc';
  orderByAccount = 'desc';
  orderByFacility = 'desc';
  orderByencounterseendate = 'desc';
  orderByadmit_Datetime = 'desc';
  sortColumnBy = 'admission_Date_sort';
  public timeout: any = null;

  hideSideNav = false;
  toggleType = 'close';
  sortOrders = ['desc', 'asc'];

  sortOptions = [
    {
      id: 'account_Number', name: 'Account Number', sortOrders: ['desc', 'asc']
    },
    {
      id: 'patient_Name', name: 'Patient Name', sortOrders: ['desc', 'asc']
    },
    {
      id: 'facility_Name', name: 'Facility', sortOrders: ['desc', 'asc']
    },
    {
      id: 'encounterseendate_sort', name: 'Encounter Seen Date', sortOrders: ['desc', 'asc']
    },
    {
      id: 'admission_Date_sort', name: 'Admission Date', sortOrders: ['desc', 'asc']
    },
  ];

  constructor(private readonly physicianService: PhysicianService, private readonly commonServ: CommonService, private readonly appComp: AppComponent, private readonly encrDecr: EncrDecrServiceService) { }

  ngOnInit(): void {
    this.appComp.loadPageName('Unbilled Encounters', 'physicianTab');
    if (history.state.filterObj) {
      this.filterObj = history.state.filterObj;
      this.searchKey = this.filterObj.searchByName;
      this.p = this.filterObj.p;
    }

    if (/Android|webOS|iPhone|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) && (window.innerWidth <= 575 || window.innerWidth >= 575)) {
      // some code..
      this.device = true;
    } else {
      this.device = false;
    }
    this.getUnBilledEncounters();
  }

  getUnBilledEncounters() {
    this.commonServ.startLoading();
    this.physicianService.getUnBilledEncounters().subscribe((p: any) => {
      if (p?.length) {
        this.listOfPatients = this.sortOrderBy(p, this.sortColumnBy, this.orderBy);
        this.msrlistOfPatients = [...this.listOfPatients];
      }
      else {
        this.listOfPatients = [];
        this.msrlistOfPatients = [];
      }
      this.commonServ.stopLoading();
    }, error => {
      this.commonServ.stopLoading();
      console.error(error.status);
    });
  }

  openDeleteConfirmPopup(item) {
    if (confirm('Do you want to contine to Delete this Missing Encounter?')) {
      this.commonServ.startLoading();
      this.request.sID = this.encrDecr.set(item.notesCount);
      this.commonServ.DeleteMissingEncounter(this.request).subscribe((p: any) => {
        this.request = {};
        this.getUnBilledEncounters();
        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
    }
  }

  sortColumn(columnBy) {
    this.sortColumnBy = columnBy;

    if (columnBy == 'patient_Name') {
      this.orderByPatient = this.orderByPatient == 'asc' ? 'desc' : 'asc';
      this.orderBy = this.orderByPatient;
    }
    else if (columnBy == 'account_Number') {
      this.orderByAccount = this.orderByAccount == 'asc' ? 'desc' : 'asc';
      this.orderBy = this.orderByAccount;
    }
    else if (columnBy == 'facility_Name') {
      this.orderByFacility = this.orderByFacility == 'asc' ? 'desc' : 'asc';
      this.orderBy = this.orderByFacility;
    }
    else if (columnBy == 'encounterseendate_sort') {
      this.orderByencounterseendate = this.orderByencounterseendate == 'asc' ? 'desc' : 'asc';
      this.orderBy = this.orderByencounterseendate;
    }
    else if (columnBy == 'admission_Date_sort') {
      this.orderByadmit_Datetime = this.orderByadmit_Datetime == 'asc' ? 'desc' : 'asc';
      this.orderBy = this.orderByadmit_Datetime;
    }
    this.listOfPatients = this.sortOrderBy(this.listOfPatients, columnBy, this.orderBy)
    this.msrlistOfPatients = [...this.listOfPatients];

  }

  sortOrderBy(list, sortBy, sortOrder) {
    let newArray = [];
    this.p = 1;
    if (sortOrder == 'asc') {
      newArray = list.sort(
        function (a, b) {
          let sortNest: number = (b[sortBy] > a[sortBy]) ? -1 : 0;
          let sort: number = (a[sortBy] > b[sortBy]) ? 1 : sortNest;
          return sort;
        }
      );
    }
    else if (sortOrder == 'desc') {
      newArray = list.sort(
        function (a, b) {
          let sortNest: number = (b[sortBy] > a[sortBy]) ? 1 : 0;
          let sort: number = (a[sortBy] > b[sortBy]) ? -1 : sortNest;
          return sort;
        });
    }
    return newArray;
  }

  search(searchKey) {

    clearTimeout(this.timeout);

    this.timeout = setTimeout(() => {
      if (this.msrlistOfPatients.length)
        this.listOfPatients = this.filterByValue(this.msrlistOfPatients, searchKey)
    }, 2000);

  }

  filterByValue(array, string) {
    return array.filter(o => {
      return Object.keys(o).some(k => {
        if (typeof o[k] === 'string')
          return o[k].toLowerCase().includes(string.toLowerCase());
      });
    });
  }

  toggleSideNav(type): void {
    this.toggleType = type;
    this.hideSideNav = !this.hideSideNav;
  }
  onChangeForSort(deviceValue) {
    this.sortOrders = this.sortOptions.filter(x => x.id == deviceValue)[0].sortOrders;
    this.sortColumn(deviceValue);
  }
  onChangeForSortOrder(deviceValue) {
    this.orderBy = deviceValue;
    this.sortColumn(this.sortColumnBy);
  }

}
