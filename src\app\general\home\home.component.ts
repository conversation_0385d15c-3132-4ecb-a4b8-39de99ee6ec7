import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AppComponent } from 'src/app/app.component';
import { MsalService } from '@azure/msal-angular';
import { Subscription } from 'rxjs';
import { AuthenticationResult } from '@azure/msal-browser';
import { ToastrService } from 'ngx-toastr';
import { JsonAppConfigService } from 'src/app/config/json-app-config.service';
import { CommonService } from 'src/app/services/common/common.service';
import { DataService } from 'src/app/services/common/data.service';
declare let $: any;
@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css']
})
export class HomeComponent implements OnInit {

  features: any;
  url = '';

  isIframe = false;
  loggedIn = false;

  public userIdleTime = 0;
  public userIdleTimeStarted = 30;
  public subscriptions: Subscription;
  loaded = '';
  accessToken = '';
  public isManageUsers: any;
  public isManageRoles: any;
  public isManageAuthorizations: any;
  public isManagePrivileges: any;
  public isManagePermissions: any;
  public isMyRoundings: any;
  public isPatientRoundingAssignments: any;
  public isSearchPatients: any;
  public numberOfUserPermissions = 0;
  public isManageFacilities: any;
  public isManageAlerts: any;
  public currentUrl = '';
  errorCode = '';
  // public loggedInUserName: any;
  msalCount = 0;
  pingCount = 0;
  public userName: string;
  idleState = 'Not started.';
  timedOut = false;
  lastPing?: Date = new Date();
  device = false;
  idleTime = 0;
  preferences = false;

  asyncLocalStorage = {
    setItem(key, value) {
      return Promise.resolve().then(() => {
        sessionStorage.setItem(key, value);
      });
    },
    getItem(key) {
      return Promise.resolve().then(() => {
        return sessionStorage.getItem(key);
      });
    }
  };
  public isReport: any;
  public isDischarge: any;
  public isDashboard: any;
  public isFacAdmin: any;
  message: string;
  siginIn = false;
  subscription: Subscription;
  userAccess: any;

  constructor(
    private readonly appComp: AppComponent,
    private readonly authService: MsalService,
    private readonly router: Router, private readonly jsonAppConfigService: JsonAppConfigService,
    private readonly data: DataService, private readonly toastr: ToastrService,
    private readonly commonServ: CommonService
  ) {
    this.loaded = sessionStorage.getItem('loaded')!;
    const url = window.location.href;

    if (url.indexOf('code=') != -1) {
      this.siginIn = true;
    }
    else if (url.indexOf('code=') == -1 && url.indexOf('code') != -1) {
      this.siginIn = false;
      history.pushState({}, '', '/#/');
    }
    else {
      this.siginIn = false;
    }


  }

  ngOnInit(): void {
    this.currentUrl = history.state.url ? history.state.url : '';
    this.accessToken = history.state.token ? history.state.token : this.commonServ.extractMSALToken();

    if (this.accessToken && history.state.token && !this.loaded) {
      this.startAppMethods(this.accessToken);
    }

    if (!this.router.getCurrentNavigation()?.extras.state) {
      this.accessToken = this.commonServ.extractMSALToken()!;
      if (this.accessToken && !this.loaded) {
        this.startAppMethods(this.accessToken);
      }
      else if (this.siginIn && !this.accessToken) {
        this.appComp.login();
      }
    }

    this.subscription = this.data.userDetails.subscribe(user => this.userName = user);
    this.subscription = this.data.accessKey.subscribe(key => this.accessToken = key);
    this.subscription = this.data.isLoggedIn.subscribe(status => this.loggedIn = status);
    this.subscription = this.data.currentUpateURL.subscribe(url => this.currentUrl = url);
    this.subscription = this.data.isUserAccess.subscribe(access => this.userAccess = access);
    this.authService.handleRedirectObservable().subscribe({
      next: (result: AuthenticationResult) => {
        if (result) {
          this.authService.instance.setActiveAccount(result.account);
          this.data.updatenewURL(this.currentUrl);
          if (result?.accessToken) {
            this.startAppMethods(result?.accessToken);
          }
        }
      },
      error: (error) => console.log(error)
    });
  }

  getURLParameterValues(parameterName, url) {
    if (!url) { url = window.location.href; }
    parameterName = parameterName.replace(/[[\]]/g, '\\><');
    const regularExpression =
      new RegExp('[?&]' + parameterName + '(=([^&#]*)|&|#|$)');
    const results = regularExpression.exec(url);
    if (!results) { return null; }
    if (!results[2]) { return ''; }
    return decodeURIComponent(results[2].replace(/\+/g, ' '));
  }

  onActivate() {
    window.scroll(0, 0);
  }

  ngOnDestroy() {
    // $('#loading').hide();
    this.subscription.unsubscribe();
  }
  login() {
    this.appComp.login();
  }
  logout() {
    this.appComp.logout();
  }

  async getUserAccess() {
    this.loaded = 'true';
    sessionStorage.setItem('loaded', this.loaded);
    this.commonServ.getUserAccess().subscribe((p: any) => {
      this.checkForUpdates();
      this.userAccess = p;
      this.data.updateAccesssForUser(p);
      this.currentUrl = sessionStorage.getItem('url') ? sessionStorage.getItem('url')! : this.currentUrl;
      if (this.userAccess.status == 'NO ACCESS') {
        this.navigateTo('/no-access');
      }
      else{
        this.navigate(this.userAccess,this.currentUrl);
      }

    }, error => {
      this.toastr.error('something went wrong!!!');
    });

    if (/Android|webOS|iPhone|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) && (window.innerWidth <= 575)) {
      // some code..
      this.device = true;
    } else {
      this.device = false;
    }
  }

  navigate(userAccess, currentUrl) {
    if (currentUrl.indexOf('physician/') != -1 || currentUrl.indexOf('coordinator/') != -1 || currentUrl.indexOf('report/') != -1 || currentUrl.indexOf('auditor/') != -1 || currentUrl.indexOf('biller/') != -1 || currentUrl.indexOf('report/') != -1 || currentUrl.indexOf('dashboard/') != -1 || currentUrl.indexOf('dashboard') != -1 || currentUrl.indexOf('message-hub') != -1) {
      const url = currentUrl.substring(currentUrl.lastIndexOf('#') + 2);
      this.navigateTo(url);
    }
    else if (userAccess.physicianModuleAccess == 'YES' || userAccess.residentModuleAccess == 'YES') {
      this.navigateTo('/physician/my-patients');
    }
    else if (userAccess.dashboardModuleAccess == 'YES' || userAccess.kareoDashboardModuleAccess == 'YES') {
      this.navigateTo('/dashboard/cpt-analysis');
    }
    else if (userAccess.coordinatorModuleAccess == 'YES') {
      this.navigateTo('/coordinator/search');
    }
    else if (userAccess.auditorModuleAccess == 'YES') {
      this.navigateTo('/auditor/auditor-edit');
    }
    else if (userAccess.billerModuleAccess == 'YES') {
      this.navigateTo('/biller/index');
    }
    else if (userAccess.userManagementAccess == 'YES') {
      this.navigateTo('/usermanagement/add-modify-user');
    }
    else if (userAccess.reportModuleAccess == 'YES') {
      this.navigateTo('/report/encounter-report');
    }
    else {
      this.navigateTo('/no-access');
    }
  }

  navigateTo(url){
    this.router.navigate([url]);
  }

  async startAppMethods(accessKey) {
    if (accessKey) {
      this.data.updateAcessKey(accessKey);
      $('#loading').show();
      await this.jsonAppConfigService.getKeys(accessKey);
      await this.getUserDetail(accessKey);
      await this.getUserAccess();
    }
  }

  async getUserDetail(token) {
    this.jsonAppConfigService.getUser(token).subscribe((p: any) => {
      this.userName = p?.displayName;
      this.data.updateUser(p?.displayName);
    },
      error => {
        this.toastr.error('something went wrong!!!');
      });
  }
  
  checkForUpdates() {
    this.commonServ.checkForUpdates()
      .subscribe((timestamp: any) => {
        this.appComp.timestamp=timestamp;
        const lastTimestamp = localStorage.getItem('appTimestamp');
        if (lastTimestamp && lastTimestamp !== timestamp) {
          console.log('New deployment detected. Reloading...');
          $('#newVersionModel').modal('show');
        } else {
          console.log('No New deployment detected');
          localStorage.setItem('appTimestamp', timestamp);
        }
      });
  }
}
