import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { MsalGuard } from '@azure/msal-angular';
import { StartNewEncounterComponent } from './start-new-encounter.component';
import { DischargePatientsComponent } from './discharge-patients.component';
import { HospitalCensusComponent } from './hospital-census.component';
import { MyGroupPatientsComponent } from './my-group-patients.component';
import { MyPatientsComponent } from './my-patients.component';
import { UnbilledEncountersComponent } from './unbilled-encounters.component';
import { ViewModifyEncountersComponent } from './view-modify-encounters.component';
import { PendingApprovalEncountersComponent } from './pending-approval-encounters.component';
import { AddPatientComponent } from '../common/add-patient.component';
import { ApprovePendingEncounterComponent } from './approve-pending-encounter.component';

const routes: Routes = [
  { path: "my-patients", component: MyPatientsComponent, canActivate: [MsalGuard] },
  { path: "my-group-patients", component: MyGroupPatientsComponent, canActivate: [MsalGuard] },
  { path: "hospital-census", component: HospitalCensusComponent, canActivate: [MsalGuard] },
  { path: "discharge-patients", component: DischargePatientsComponent, canActivate: [MsalGuard] },
  { path: "pending-approval-encounters", component: PendingApprovalEncountersComponent, canActivate: [MsalGuard] },
  { path: "view-modify-encounters", component: ViewModifyEncountersComponent, canActivate: [MsalGuard] },
  { path: "add-patient", component: AddPatientComponent, canActivate: [MsalGuard] },
  { path: "unbilled-encounters", component: UnbilledEncountersComponent, canActivate: [MsalGuard] },
  { path: "start-new-encounter", component: StartNewEncounterComponent, canActivate: [MsalGuard] },
  { path: "approve-pending-encounter", component: ApprovePendingEncounterComponent, canActivate: [MsalGuard] }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PhysicianRoutingModule { }
