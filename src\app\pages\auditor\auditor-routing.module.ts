import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { MsalGuard } from '@azure/msal-angular';
import { AuditorEditComponent } from '../auditor/auditor-edit.component';

const routes: Routes = [

    { path: "auditor-edit", component: AuditorEditComponent, canActivate: [MsalGuard] },
]
@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class AuditorRoutingModule { }
