<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new p-2" style="background: #0169ab;" [formGroup]="FilterForm">
                    <div class="align-items-center row mx-auto">
                        <div class="col-12 px-0">

                            <div class="row mt-1 mx-auto">
                                <div class="col-12 col-md-3 px-1 py-1">
                                    <select id="ddlFacility" class="form-control" formControlName="ddlFacility"
                                        (change)="loadDataInFaciliytChangeEvent(1)">
                                        <option value="All">All Facilities</option>
                                        <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                                            <span>{{s.facilityName}}</span>
                                        </option>
                                    </select>
                                </div>
                                <div class="col-12 col-md-3 px-1 py-1">
                                    <select id="ddlDepartment" class="form-control custom-control"
                                        formControlName="ddlDepartment" (change)="getCoordinatorPatients(1)">
                                        <option value="All">All Departments</option>
                                        <option [value]="s.patienT_LOCATION" *ngFor="let s of listofDepartments">
                                            {{s.patienT_LOCATION}}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-12 col-md-3 px-1 py-1">
                                    <select id="ddlGroups" class="form-control custom-control"
                                        formControlName="ddlGroups" (change)="getCoordinatorPatients(1)">
                                        <option value="All">All Groups</option>
                                        <option [value]="s.group_name" *ngFor="let s of listOfGroups">
                                            {{s.group_name}}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-12 col-md-2 px-1 py-1">
                                    <select id="ddlPhysician" class="form-control custom-control"
                                        formControlName="ddlPhysician" (change)="getCoordinatorPatients(1)">
                                        <option value="All">All Physicians</option>
                                        <option *ngIf="listOfPhysicians.length>0" value="Un-Assigned">Un-Assigned
                                        </option>
                                        <option [value]="s.attendinG_PHYSICIANS" *ngFor="let s of listOfPhysicians">
                                            {{s.attendinG_PHYSICIANS}}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-12 col-md-1 text-right text-md-left px-1 py-1">
                                    <img alt=' ' *ngIf="listOfPatients.length>0" [src]="img1"
                                        class="downloadIcon shadow" (keyup)="exportAsXLSX()" (click)="exportAsXLSX()"
                                        title="Export Excel">
                                </div>
                            </div>

                            <div class="row mt-1 mx-auto align-items-center" [formGroup]="swapForm"
                                *ngIf="totalCount>0 && FilterForm.value.ddlFacility!='All' && (FilterForm.value.ddlPhysician!='All' && FilterForm.value.ddlPhysician!='Un-Assigned')">
                                <div class="col-12 col-md-6 px-1 py-1 align-self-center">
                                    <span class="text-white small my-auto bg-blue p-1 rounded border border-white">
                                        <span *ngIf="!swapForm.value.ddlSwapPhysician">
                                            Clicking Assign, All the below {{totalCount}} patient(s) will be removed
                                            from
                                            Dr.{{FilterForm.value.ddlPhysician}}'s Rounding list.
                                            You may also select a different physician in order to swap
                                            Dr.{{FilterForm.value.ddlPhysician}}'s patients</span>
                                        <span *ngIf="swapForm.value.ddlSwapPhysician">
                                            Once you click Assign, below {{totalCount}} patient(s) will be assigned to
                                            Dr.{{swapForm.value.ddlSwapPhysician}}
                                            from Dr.{{FilterForm.value.ddlPhysician}} </span>
                                    </span>
                                </div>
                                <div class="col-12 col-md-3 px-1 py-1 align-self-center">
                                    <select id="ddlSwapPhysician" class="form-control custom-control"
                                        formControlName="ddlSwapPhysician"
                                        [ngClass]="{ 'is-invalid': submitted && f2['ddlSwapPhysician'].errors}">
                                        <option value='null'>Select Physician</option>
                                        <option [value]="s.attendinG_PHYSICIANS" *ngFor="let s of listOfPhysicians">
                                            {{s.attendinG_PHYSICIANS}}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-12 col-md-2 text-right text-md-left px-1 py-1 align-self-center">
                                    <button title="Swap Physician" class="btn btn-outline-info px-2 btn-block"
                                        type="submit" (click)="openSwapPhysician()">Assign</button>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="row my-1 mx-auto">
                        <div class="col-12 col-md-10 px-1 py-1">
                            <div class="input-group">
                                <input type="text" class="form-control small" maxlength="1000"
                                    placeholder="Search for Account No#, Patient Name or Room" aria-label="Search"
                                    aria-describedby="basic-addon2" [(ngModel)]="searchByName"
                                    [ngModelOptions]="{standalone: true}" (keyup)="onKeySearchCoordinatorPatients()">
                                <div class="input-group-append">
                                    <button class="bg-white btn text-dark"><i class="fas fa-search fa-sm"></i></button>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-2 text-right text-md-left px-1 py-1 align-self-center">
                            <button title="Save Assignments" class="btn btn-outline-info px-2 btn-block" type="submit"
                                (click)="saveAssignments()">Save Assignments</button>
                        </div>
                    </div>
                </div>

                <!-- Card Body -->
                <div class="card-body px-2 py-1 scroll-x">
                    <div class="row mx-0">
                        <div class="col-12 px-0">
                            <!-- table start -->
                            <div>
                                <table class="table mb-0 text-small width-100per" border="1">
                                    <thead>
                                        <tr>
                                            <th class="width-4per text-center pb-0 p0erm">Account No#</th>
                                            <th class="width-3per text-center pb-0 p0erm">Patient Name</th>
                                            <th class="width-2per text-center pb-0 p0erm">SSN</th>
                                            <th class="width-2per text-center pb-0 p0erm">DOB</th>
                                            <th class="width-2per text-center mr-1 pb-0 p0erm">Admit Date</th>
                                            <th class="width-2per text-center mr-1 pb-0 p0erm">Admit Type</th>
                                            <th class="width-4per text-center mr-1 pb-0 p0erm">Primary Coverage</th>
                                            <th class="width-4per text-center pb-0 p0erm">Facility</th>
                                            <th class="width-4per text-center pb-0 p0erm">Room</th>
                                            <th class="min-width-11 text-center pb-0 p0erm">Notes</th>
                                            <th class="min-width-11 text-center pb-0 p0erm">Attending Provider</th>
                                            <th class="min-width-11 text-center pb-0 p0erm">Groups</th>
                                            <th class="width-2per text-center pb-0 p0erm">Messages</th>
                                            <th class="width-2per text-center pb-0 p0erm" *ngIf='nonPrimeCount>0'>Edit
                                                Patient</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr
                                            *ngFor="let item of listOfPatients | paginate: { itemsPerPage: 10, currentPage: p,totalItems:totalCount}; let i= index;">
                                            <td class="width-4per text-center p0erm">{{item.account_number}}</td>
                                            <td class="width-3per text-center p0erm">{{item.patient_name}}</td>
                                            <td class="width-2per text-center p0erm">{{item.ssn}}</td>
                                            <td class="width-2per text-center p0erm">{{item.dob | date: 'MM/dd/yyyy'}}
                                            </td>
                                            <td class="width-2per text-center p0erm">{{item.admit_datetime | date:
                                                'MM/dd/yyyy'}}</td>
                                            <td class="width-2per text-center p0erm">{{item.admission_type}}</td>
                                            <td class="width-4per text-center p0erm">{{item.primary_coverage}}</td>
                                            <td class="width-4per text-center p0erm">{{item.facility_name}}</td>
                                            <td class="width-4per text-center p0erm">{{item.room_number}}</td>
                                            <td class="min-width-11 text-center p1erm">
                                                <div class="row mx-auto">
                                                    <div class="col-10 p-1">
                                                        <textarea class="form-control m-auto"
                                                            [(ngModel)]="item.coordinator_notes"
                                                            [ngModelOptions]="{standalone: true}"></textarea>
                                                    </div>
                                                    <div class="col-2 p-1 my-auto">
                                                        <ng-container class="vertical-align-middle">
                                                            <button class="btn btn-link p-0"
                                                                title="Save Coordinator Note"
                                                                (click)="saveCoordinatorNotes(item)">
                                                                <i class="far fa-save font-size-1-5rem"></i>
                                                            </button>
                                                        </ng-container>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="min-width-11 text-center p-1 multiselect-relative">
                                                <ng-multiselect-dropdown [placeholder]="'None Selected'"
                                                    [data]="item.listofAttendingProviders"
                                                    [(ngModel)]="item.listofSelectedAttendingProviders"
                                                    [settings]="mDdlAttendingProviderSettings"
                                                    (onSelect)="saveAttendingProvider(item,'onSelect')"
                                                    (onDeSelect)="saveAttendingProvider(item,'onDeSelect')"
                                                    (onSelectAll)="saveAttendingProvider(item,'onSelectAll')"
                                                    (onDeSelectAll)="saveAttendingProvider(item,'onDeSelectAll')">
                                                </ng-multiselect-dropdown>
                                                {{item.attending_physician_inapp}}
                                            </td>
                                            <td class="min-width-11 text-center p-1 multiselect-relative">
                                                <ng-multiselect-dropdown [placeholder]="'None Selected'"
                                                    [data]="item.listofGroups" [(ngModel)]="item.listofSelectedGroups"
                                                    [settings]="mDdlGroupSettings"
                                                    (onSelect)="saveGroups(item,'onSelect')"
                                                    (onDeSelect)="saveGroups(item,'onDeSelect')"
                                                    (onSelectAll)="saveGroups(item,'onSelectAll')"
                                                    (onDeSelectAll)="saveGroups(item,'onDeSelectAll')">
                                                </ng-multiselect-dropdown>
                                                {{item.selectedgroups}}
                                            </td>
                                            <td class="width-2per text-center p-1 vertical-align-middle">
                                                <button class="btn btn-link p-0" title="View or Send Notes"
                                                    (click)="openNotes(item)">
                                                    <i class="far fa-comment-alt font-size-1-5rem"></i>
                                                </button>
                                            </td>
                                            <td class="width-2per text-center p-1 vertical-align-middle"
                                                *ngIf='nonPrimeCount>0'>
                                                <button *ngIf="!item.is_prime_hospital" class="btn btn-link p-0"
                                                    title="Update Patient's Details" (click)="openEditPatient(item)">
                                                    <i class="far fa-edit font-size-1-5rem"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr *ngIf="totalCount==0">
                                            <td colSpan="14" class="text-center">
                                                <span
                                                    *ngIf="(FilterForm.value.ddlPhysician=='All' || FilterForm.value.ddlPhysician=='Un-Assigned');else noPtsFEls">No
                                                    patients found</span>
                                                <ng-template #noPtsFEls>
                                                    <span>No patients are assigned to this physician</span>
                                                </ng-template>
                                            </td>
                                        </tr>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <td colspan="14" class="m-0 p-0" style="background: white !important;">
                                                <pagination-controls previousLabel="" nextLabel=""
                                                    (pageChange)="getCoordinatorPatients($event)"></pagination-controls>
                                            </td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            <!-- table end -->
                        </div>
                    </div>

                </div>

            </div>
        </div>
    </div>
</div>
<!-- Page Content Ends -->


<!-- Swap Physician Modal Start -->
<div class="modal fade" id="swapConfModel" tabindex="-1" aria-labelledby="swapConfModelLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title h5" id="swapConfModelLabel">Assign Physician</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <span>Are you sure want to proceed?</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary btn-sm" data-dismiss="modal">No</button>
                <button type="button" class="btn btn-outline-info btn-sm" (click)="swapPhysician()">Yes</button>
            </div>
        </div>
    </div>
</div>
<!-- Swap Physician End -->

<!-- send Note Popup Starts -->
<app-send-note [PatientObject]="patient" [listOfUsersAndGroups]="listOfUsersAndGroups"
    [lisfOfSentNotes]="lisfOfSentNotes" [selectedUsersAndGroups]="selectedUsersAndGroups" (eventListOfNotes)="openNotes(patient)"></app-send-note>
<!-- send note Popup Ends -->

<!-- Edit Patient Popup Starts -->
<app-edit-patient [patient]="patient" (eventListOfPatients)="getCoordinatorPatients(p)" [userType]="'COORDINATOR'"></app-edit-patient>
<!-- Edit Patient Popup Ends -->