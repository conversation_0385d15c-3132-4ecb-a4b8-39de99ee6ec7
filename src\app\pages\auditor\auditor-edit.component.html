<!-- Begin Page Content Starts -->
<div class="container-fluid" [ngClass]="{disabledNoOfCasesDiv: isDisabled}">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new p-2" style="background: #0169ab;" [formGroup]="FilterForm">
                    <div class="align-items-center row mx-auto">
                        <div class="col-12 px-0">
                            <div class="row mb-2 mt-1 mx-auto">

                                <div class="col-12 col-md-2 px-1 py-1">
                                    <select id="ddlFacility" class="form-control" formControlName="ddlFacility"
                                        (change)="getPhysiciansByFacility($any($event.target).value)"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                                        <option value="">---Select Facility---</option>
                                        <option value="All">All Facilities</option>
                                        <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                                            <span>{{s.facilityName}}</span>
                                        </option>
                                    </select>
                                    <div *ngIf="submitted && f['ddlFacility'].errors" class="invalid-feedback">
                                        <div *ngIf="f['ddlFacility'].errors['required']">Facility is required</div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-2 px-1 py-1">
                                    <select id="ddlPhysician" class="form-control custom-control"
                                        formControlName="ddlPhysician"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlPhysician'].errors}">
                                        <option value="All">All Physicians</option>
                                        <option [value]="s.physicianEmaiId" *ngFor="let s of listOfAllPhysicians">
                                            {{s.physicianName}}
                                        </option>
                                    </select>
                                    <div *ngIf="submitted && f['ddlPhysician'].errors" class="invalid-feedback">
                                        <div *ngIf="f['ddlPhysician'].errors['required']">Physician is required</div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-2 px-1 py-1">
                                    <input id="encounterSeenDateFrom" type="date" class="form-control"
                                        formControlName="encounterSeenDateFrom" title="Encounter Seen Date From"
                                        [ngClass]="{ 'is-invalid': submitted && f['encounterSeenDateFrom'].errors}">
                                    <div *ngIf="submitted && f['encounterSeenDateFrom'].errors"
                                        class="invalid-feedback">
                                    </div>
                                </div>
                                <div class="col-12 col-md-2 px-1 py-1">
                                    <input id="encounterSeenDateTo" type="date" class="form-control"
                                        formControlName="encounterSeenDateTo" title="Encounter Seen Date To"
                                        [ngClass]="{ 'is-invalid': submitted && f['encounterSeenDateTo'].errors}">
                                    <div *ngIf="submitted && f['encounterSeenDateTo'].errors" class="invalid-feedback">
                                    </div>
                                </div>
                                <div class="col-12 col-md-2 px-1 py-1">
                                    <input type="text" formControlName="dateRangeLable" ngxDaterangepickerMd [(ngModel)]="dateRangeLable"
                                        [showCustomRangeLabel]="true" [alwaysShowCalendars]="false" [ranges]="ranges" (change)="selectedDate($event)"
                                        [showRangeLabelOnInput]="true" [showDropdowns]="true" title="Choose Admission Date Range"
                                        placeholder="Choose Admission Date Range" class="form-control btn-lavender" readonly />
                                </div>
                                <div class="col-12 col-md-2 px-1 py-1"
                                    [ngClass]="{'col-md-1': FilterForm.value.encounterSeenDateFrom||FilterForm.value.encounterSeenDateTo, 'col-md-2': !FilterForm.value.encounterSeenDateFrom&&!FilterForm.value.encounterSeenDateTo}">
                                    <button title="Submit Filter"
                                        class="btn btn-outline-info px-2 btn-block d-m-inline-block" type="submit"
                                        (click)="getPatientsViewCharges(1)">
                                        <i class="fa-filter fas font-size-13"></i>
                                        Filter
                                    </button>
                                </div>
                                <div class="col-12 col-md-1 px-1 py-1"
                                    *ngIf="FilterForm.value.encounterSeenDateFrom||FilterForm.value.encounterSeenDateTo">
                                    <button title="Clear Dates"
                                        class="btn btn-outline-info px-2 btn-block d-m-inline-block" type="submit"
                                        (click)="clearFilterPatients()">
                                        <i class="fa fa-window-close" aria-hidden="true"></i> Clear
                                    </button>
                                </div>
                            </div>
                            <div class="row mb-2 mt-1 mx-auto"
                                *ngIf="(submitted && (f['encounterSeenDateFrom'].errors?.['endDateError']||f['encounterSeenDateTo'].errors?.['endDateError']))">
                                <div class="col text-danger px-1 py-1 text-center">Encounter Seen From date should be
                                    lesser than Encounter Seen To date.
                                </div>
                            </div>
                            <div class="row mb-2 mt-1 mx-auto" *ngIf="submitted && arrivalDateVal">
                                <div class="col text-danger px-1 py-1 text-center">Encounter Seen From or To date should
                                    be lesser than five days from Admission From date or To date.</div>
                            </div>
                            <div class="row mb-2 mt-1 mx-auto">
                                <div class="col-12 col-md-8 px-1 py-1">
                                    <div class="input-group">
                                        <input type="text" class="form-control small" (keyup)="SearchPatientData()"
                                            maxlength="1000"
                                            placeholder="Search for Patient Name, Account No, MRN or Room"
                                            aria-label="Search" aria-describedby="basic-addon2"
                                            [(ngModel)]="searchByName" [ngModelOptions]="{standalone: true}">
                                        <div class="input-group-append">
                                            <button class="bg-white btn text-dark"><i
                                                    class="fas fa-search fa-sm"></i></button>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-md-4 px-1 py-1" id="auditRequired">
                                    <select id="ddlBill" class="form-control custom-control"
                                        formControlName="ddlMarkAsApp">
                                        <option value="">Select Coded Status</option>
                                        <option value="All">All</option>
                                        <option value="Coded">Coded</option>
                                        <option value="NotCoded">Not Coded</option>
                                    </select>
                                </div>

                            </div>
                        </div>

                    </div>

                </div>
                <!-- Card Body -->
                <div class="card-body px-2 py-1">
                    <div class="row mx-0">
                        <div class="col-12 px-0">
                            <!-- table start -->
                            <div class="tablechange table-responsive">
                                <table class="table table-borderless">
                                    <thead>
                                        <tr>
                                            <th class="width-13per pb-0">Patient Name</th>
                                            <th *ngIf="!device" class="width-10per text-center pb-0">Account No.</th>
                                            <th *ngIf="!device" class="width-10per text-center pb-0">MRN</th>
                                            <th *ngIf="!device" class="width-8per text-center pb-0">DOB</th>
                                            <th *ngIf="!device" class="width-4per text-center pb-0">Sex</th>
                                            <th *ngIf="!device" class="width-8per text-center pb-0">SSN</th>
                                            <th class="width-6per text-center pb-0">Facility</th>
                                            <th *ngIf="!device" class="width-6per text-center pb-0">Room</th>
                                            <th *ngIf="!device" class="width-11per small-scroll mr-1 pb-0">Admission
                                                Date</th>
                                            <th *ngIf="!device" class="width-11per small-scroll ml-1 pb-0">Discharge
                                                Date</th>
                                            <th class="width-10per small-scroll-2 small-scroll ml-2 pb-0">Primary
                                                Coverage</th>

                                        </tr>
                                    </thead>
                                    <tbody>
                                        <ng-container
                                            *ngFor="let item of listOfPatients | paginate: { itemsPerPage: 14, currentPage: p,totalItems:totalCount}; let i= index;">
                                            <tr *ngIf="item.account_Number!=''">
                                                <td (keyup)="getEncountersByPatient(item,true)"
                                                    (click)="getEncountersByPatient(item,true)"
                                                    class="bill-arrow collapsed" data-toggle="collapse"
                                                    attr.data-target="#maintable{{i}}" aria-expanded="false"
                                                    [ngClass]="{'width-13per text-nowrap position-relative': !device}">
                                                    <span
                                                        class="overflow-hidden pl-3 text-nowrap overflow-text cursor-pointer font-weight-bold">{{item.patient_Name}}</span>
                                                    <span *ngIf="item.discharge_Date" class="ml-1"><i class="fa fa-bed"
                                                            title="Discharged"></i></span>
                                                    <span *ngIf="item?.billervisitstatus=='0'" class="ml-1"><i
                                                            class="fa fa-user-plus" title="New"></i></span>
                                                </td>
                                                <td *ngIf="!device" class="width-10per text-center ">
                                                    {{item.account_Number}}</td>
                                                <td *ngIf="!device" class="width-10per text-center">{{item.mrn}}</td>
                                                <td *ngIf="!device" class="width-8per text-center">{{item.dob}}</td>
                                                <td *ngIf="!device" class="width-4per text-center">{{item.sex}}</td>
                                                <td *ngIf="!device" class="width-8per text-center"><span
                                                        *ngIf="item.ssn">******</span>{{item.ssn | slice:6:9}}</td>
                                                <td class="width-6per text-center">{{item.facility_Name}}</td>
                                                <td *ngIf="!device" class="width-6per text-center">{{item.room_Number}}
                                                </td>
                                                <td *ngIf="!device" class="width-6per text-center">
                                                    {{item.admission_Date}}</td>
                                                <td *ngIf="!device" class="width-6per text-center">
                                                    {{item.discharge_Date}}</td>
                                                <td class="pb-0"
                                                    [ngClass]="{'width-10per small-scroll-2 small-scroll ml-2': !device}">
                                                    {{item.primary_Coverage}}
                                                </td>

                                            </tr>
                                            <tr *ngIf="item.account_Number==envAccountNo&&item.mrn==envMrnNo&&item.facility_Name==envFacility"
                                                id="maintable{{i}}" class="collapse">
                                                <td colspan="11" class="inner-table-boder"
                                                    style="background: #fff !important;">
                                                    <div class="row mx-0 innerscroll">
                                                        <div class="col-12 px-md-1 px-0">
                                                            <!-- innerconetct start -->
                                                            <div class="row mx-0">
                                                                <div class="col-12 ml-auto text-right pb-1 px-0">
                                                                    <app-actions-of-view-modify-encounter [PatientObject]="PatientObject" [userType]="'AUDITOR'"
                                                                        [listOfFacilities]="listOfFacilities"></app-actions-of-view-modify-encounter>
                                                                </div>
                                                            </div>
                                                            <div *ngIf="device"
                                                                class="row mx-0 text-secondary border bg-light my-2">
                                                                <div class="col-12 m-auto p-1">
                                                                    <div
                                                                        class="d-inline-block pr-md-2 pr-2 text-left m-auto callout callout-primary bg-white">
                                                                        <h6 class="card-title small my-auto px-1">Name-:
                                                                            <span>{{item.patient_Name}}</span>
                                                                        </h6>
                                                                    </div>
                                                                    <div
                                                                        class="d-inline-block pr-md-2 pr-2 text-left m-auto callout callout-primary bg-white">
                                                                        <h6 class="card-title small my-auto px-1">
                                                                            Account:
                                                                            <span>{{item.account_Number}}</span>
                                                                        </h6>
                                                                    </div>
                                                                    <div
                                                                        class="d-inline-block pr-md-2 pr-2 text-left m-auto callout callout-primary bg-white">
                                                                        <h6 class="card-title small my-auto px-1">MRN:
                                                                            <span>{{item.mrn}}</span>
                                                                        </h6>
                                                                    </div>
                                                                    <div
                                                                        class="d-inline-block pr-md-2 pr-2 text-left m-auto callout callout-primary bg-white">
                                                                        <h6 class="card-title small my-auto px-1">DOB:
                                                                            <span>{{item.dob}}</span>
                                                                        </h6>
                                                                    </div>
                                                                    <div
                                                                        class="d-inline-block pr-md-2 pr-2 text-left m-auto callout callout-primary bg-white">
                                                                        <h6 class="card-title small my-auto px-1">Sex:
                                                                            <span>{{item.sex}}</span>
                                                                        </h6>
                                                                    </div>
                                                                    <div
                                                                        class="d-inline-block pr-md-2 pr-2 text-left m-auto callout callout-primary bg-white">
                                                                        <h6 class="card-title small my-auto px-1">SSN:
                                                                            <span><span
                                                                                    *ngIf="item.ssn">******</span>{{item.ssn
                                                                                | slice:6:9}}</span>
                                                                        </h6>
                                                                    </div>
                                                                    <div
                                                                        class="d-inline-block pr-md-2 pr-2 text-left m-auto callout callout-primary bg-white">
                                                                        <h6 class="card-title small my-auto px-1">
                                                                            Facility:
                                                                            <span>{{item.facility_Name}}</span>
                                                                        </h6>
                                                                    </div>
                                                                    <div
                                                                        class="d-inline-block pr-md-2 pr-2 text-left m-auto callout callout-primary bg-white">
                                                                        <h6 class="card-title small my-auto px-1">Room:
                                                                            <span>{{item.room_Number}}</span>
                                                                        </h6>
                                                                    </div>
                                                                    <div
                                                                        class="d-inline-block pr-md-2 pr-2 text-left m-auto callout callout-primary bg-white">
                                                                        <h6 class="card-title small my-auto px-1">Age:
                                                                            <span>{{item.admission_Date}}</span>
                                                                        </h6>
                                                                    </div>
                                                                    <div
                                                                        class="d-inline-block pr-md-2 pr-2 text-left m-auto callout callout-primary bg-white">
                                                                        <h6 class="card-title small my-auto px-1">Adm.:
                                                                            <span>{{item.discharge_Date}}</span>
                                                                        </h6>
                                                                    </div>
                                                                    <div
                                                                        class="d-inline-block pr-md-2 text-left m-auto callout callout-primary bg-white">
                                                                        <h6 class="card-title small my-auto px-1">
                                                                            Primary Coverage:
                                                                            <span>{{item.primary_Coverage}}</span>
                                                                        </h6>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <!-- encounter starts -->
                                                            <app-encounter [lisfOfEncounters]="lisfOfEncounters"
                                                                [PatientObject]="item"
                                                                [firstEcounterIdToOpen]="firstEcounterIdToOpen"></app-encounter>
                                                            <!-- encouter ends -->
                                                            <!-- innerconetct end -->
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        </ng-container>
                                        <tr *ngIf="totalCount==0">
                                            <td colspan="11" class="text-center">No patients found</td>
                                        </tr>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <td colspan="11" class="m-0 p-0" style="background: white !important;">
                                                <pagination-controls previousLabel="" nextLabel=""
                                                    (pageChange)="getPatientsViewCharges($event)"></pagination-controls>
                                            </td>
                                        </tr>
                                    </tfoot>
                                </table>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Delete Encounter popup starts -->
<app-delete-encounter [encounterObj]="encounterObj" [userType]="'AUDITOR'"></app-delete-encounter>
<!-- Delete Encounter popup ends -->

<!-- Alerts popup starts -->
<app-alert [groupAlerts]="groupAlerts" [PatientObject]="PatientObject"></app-alert>
<!-- Alerts popup end -->

<!-- Edit Encounter Seen Date starts -->
<app-edit-encounter-seen-date [encounterObj]="encounterObj" [PatientObject]="PatientObject" [userType]="'AUDITOR'">
</app-edit-encounter-seen-date>
<!-- Edit Encounter Seen Date ends -->

<!---- View History Start-->
<app-view-encounter-history [listOfHistory]='listOfHistory'></app-view-encounter-history>
<!---- View History End-->
