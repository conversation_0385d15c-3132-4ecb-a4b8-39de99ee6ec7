import { Component, OnInit, Input } from '@angular/core';
import { BillingService } from 'src/app/services/billing/billing.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { CommonService } from 'src/app/services/common/common.service';
import { BillingComponent } from './billing.component';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { DatePipe } from '@angular/common';
import { ToastrService } from 'ngx-toastr';
declare let $: any;

@Component({
  selector: 'app-finalise-all-icds',
  templateUrl: './finalise-all-icds.component.html',
  styles: []
})
export class FinaliseAllIcdsComponent implements OnInit {
  @Input() lisfOfGroupEncountersForKareo: Array<any> = [];
  public groupOfIcds: Array<any> = [];
  @Input() kareoPreConfirmationMassage: string = "";
  public request: any = {};
  @Input() confirmationMessage: string = "";
  @Input() PatientObject: any;
  public isValidationMessage: boolean = false;
  @Input() ssnConfirmationMessage: string = "";
  public patientForm: FormGroup;
  public submitted: boolean = false;
  public HighlightRow = -1;
  constructor(private readonly billingServ: BillingService, private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService, private readonly billComp: BillingComponent
    , private readonly fb: FormBuilder, private readonly toastr: ToastrService, public datepipe: DatePipe) { }

  ngOnInit() {
    this.patientForm = this.fb.group({
      txtPatientName: [''],
      txtAccountNo: [''],
      txtFacilityName: [''],
      txtSSN: ['', [Validators.pattern(/^-?(0|[1-9]\d*)?$/), Validators.maxLength(9)]]
    });
  }

  get f() { return this.patientForm.controls; }

  confirmIcds() {
    this.confirmationMessage = "You are submitting encounter(s) for the below physician(s) <br/>";
    this.confirmationMessage = this.confirmationMessage + this.kareoPreConfirmationMassage + "Please click submit to send the selected encounter(s) to Kareo.<br/>Note: Once you submit the encounter(s) to Kareo, You can't make changes to the encounter(s) in Grand Rounds.";
  }

  ssnConfirm() {
    this.ssnConfirmationMessage = 'SSN verified.'
    this.confirmationMessage = "You are submitting encounter(s) for the below physician(s) <br/>";
    this.confirmationMessage = this.confirmationMessage + this.kareoPreConfirmationMassage + "Please click submit to send the selected encounter(s) to Kareo.<br/>Note: Once you submit the encounter(s) to Kareo, You can't make changes to the encounter(s) in Grand Rounds.";
  }

  updateSSNConfirm() {
    this.submitted = true;
    if (this.patientForm.invalid) {
      return;
    }
    this.commonServ.startLoading();
    this.request.account_number = this.encrDecr.set(this.PatientObject.account_Number);
    this.request.facility_name = this.encrDecr.set(this.PatientObject.facility_Name);
    this.request.user_type = this.encrDecr.set('BILLER');
    this.request.ssn = this.encrDecr.set(this.patientForm.value.txtSSN);
    this.commonServ.updatePrimePatient(this.request).subscribe((p: any) => {
      this.PatientObject.ssn = this.patientForm.value.txtSSN;
      this.request = {};
      this.submitted = false;
      this.ssnConfirm();
      this.commonServ.stopLoading();
      this.toastr.success('Patient Updated Successfully!', '', { timeOut: 2500 });
      $('#editPatient').modal('hide');
    },
      error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });

  }

  back1() {
    this.kareoPreConfirmationMassage = "";
    this.groupOfIcds = [];
  }

  back2() {
    this.confirmationMessage = "";
  }

  back3() {
    this.ssnConfirmationMessage = "";
  }



  chkChangeEvent(event) {
    for (let item of this.lisfOfGroupEncountersForKareo) {
      if (item.encounteR_ID == event.target.id) {
        item.checked = event.target.checked;
        this.isValidationMessage = false;
      }
    }
  }

  finalizeICdsAll(lstOfGEnvs) {
    let message: string = "";
    lstOfGEnvs.forEach(x => {
      if (x.checked) {
        x.listOfIcds.forEach(y => {
          if (!this.groupOfIcds.includes(y)) {
            this.groupOfIcds.push(y);
          }
        });
        message = message + x.posT_TO_BILLED_BY + " for date of service " + x.encounterseendate + "<br/>";
      }
    });
    if (message != "")
      this.kareoPreConfirmationMassage = message;
    else
      this.isValidationMessage = true;
  }

  submitEncounterToKareoAll(lstEnvs, pObj) {
    this.commonServ.startLoading();
    this.groupOfIcds = [];
    let listOfFinalizedIcds: Array<any> = [];
    $('select[id*="ddlIcdAll"] option').each(function (index, value) {
      listOfFinalizedIcds.push(value.innerHTML);
    });
    this.request.listOfEncounters = lstEnvs.filter(x => x.checked);
    this.request.IcdsArray = listOfFinalizedIcds;
    listOfFinalizedIcds = [];
    this.billingServ.sendAllEncounterToKareo(this.request).subscribe((p: any) => {
      this.request = {};
      $("#finalizeICdsAll").modal('hide');
      this.kareoPreConfirmationMassage = "";
      this.confirmationMessage = "";
      this.ssnConfirmationMessage = "";
      if (p > 0) {
        this.billComp.refreshEncountersByPatient(pObj, '');
      }
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
      console.error(error.status);
    });

  }

  clickedRow(rowIndex) {
    this.HighlightRow = rowIndex;
  }
  moveSelectedUp() {
    let newIndex = this.HighlightRow - 1;
    if (this.HighlightRow>-1 && newIndex >= 0 && newIndex <= this.groupOfIcds.length - 1) {
      let currentICDs = this.groupOfIcds[this.HighlightRow];
      this.groupOfIcds[this.HighlightRow] = this.groupOfIcds[newIndex];
      this.groupOfIcds[newIndex] = currentICDs;
      this.HighlightRow = newIndex;
    }
  }
  moveSelectedDown() {
    let newIndex = this.HighlightRow + 1;
    if (this.HighlightRow>-1 && newIndex >= 0 && newIndex <= this.groupOfIcds.length - 1) {
      let currentICDs = this.groupOfIcds[this.HighlightRow];
      this.groupOfIcds[this.HighlightRow] = this.groupOfIcds[newIndex];
      this.groupOfIcds[newIndex] = currentICDs;
      this.HighlightRow = newIndex;
    }
  }

}
