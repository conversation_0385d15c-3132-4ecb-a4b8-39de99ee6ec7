import { Component, OnInit } from '@angular/core';
import { CommonService } from 'src/app/services/common/common.service';
import { ReportsService } from 'src/app/services/reports/reports.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { AppComponent } from 'src/app/app.component';
import { ExcelServices } from 'src/app/services/common/ExcelService';
import { DatePipe } from '@angular/common';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
declare let $: any;

@Component({
  selector: 'app-encounter-report',
  templateUrl: './encounter-report.component.html',
  styles: []
})
export class EncounterReportComponent implements OnInit {
  public listOfFacilities: Array<any> = [];
  public listOfPhysicians: Array<any> = [];
  public listOfReports: Array<any> = [];
  public listOfReportsInitial: Array<any> = [];
  public FilterForm: FormGroup;
  public request: any = {};
  public submitted: boolean = false;
  public p: number;
  public searchByName: string;
  public monthNames: Array<any> = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  orderBy = 'desc';
  orderByPatient = 'desc';
  orderByAccount = 'desc';
  orderByPhysician = 'desc';
  orderByEncounterSeenDate = 'desc';
  orderByAuditorAuditRequired = 'desc';
  orderByAuditorAuditDate = 'desc';
  orderByCoordinatorApprovalRequired = 'desc';
  orderByCoordinatorApproval = 'desc';
  orderByCoordinatorApprovedDate = 'desc';
  orderByPatientMRNDOB = 'desc'
  orderByMarkeD_AS_BILL_BY = 'desc';
  orderByMarkeD_AS_BILLED_DATE = 'desc';
  orderByRecorD_REMOVED_BY = 'desc';
  orderByRecorD_REMOVED_DATE = 'desc';
  orderBycreatedDate = 'desc';
  orderBydeleted_On = 'desc';
  orderBydeleted_By = 'desc';
  orderBydeleted_Reason = 'desc';
  orderBydeleted_Role = 'desc';
  sortColumnBy = 'encounterseendate';
  public timeout: any = null;
  public mDdlGroupsSettings: any = {};
  public listOfGroups: Array<any> = [];
  public selectedListOfGroups: Array<any> = [];
  public mDdlPhysicianSettings: any = {};
  public selectedListOfPhysicians: Array<any> = [];
  public fileName = '';
  public img1: string = '.././../../assets/img/excel.png';
  constructor(private readonly reportsServ: ReportsService, private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService,
    private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly excelService: ExcelServices, public datepipe: DatePipe, 
    private readonly toastr: ToastrService, private readonly router: Router) {
    if (this.router.url === '/report/encounter-report') {
      this.appComp.loadPageName('Encounter Report', 'reportsTab');
    }
    else if (this.router.url === '/report/deleted-encounter-report') {
      this.appComp.loadPageName('Deleted Encounter Report', 'reportsTab');
    }
  }

  ngOnInit() {
    this.getFacilities();
    this.FilterForm = this.fb.group({
      ddlFacility: ['', Validators.required],
      ddlPhysician: ['', Validators.required],
      ddlGroups: ['', Validators.required],
      encounterSeenDateFrom: ['', [Validators.required]],
      encounterSeenDateTo: ['', [Validators.required]],
      ddlBill: [''],
      removedFromBilligQueue: [false],
      discharged: [false]
    });
    this.mDdlGroupsSettings = {
      singleSelection: false,
      idField: 'group_name',
      textField: 'group_name',
      enableCheckAll: true,
      itemsShowLimit: 1,
      allowSearchFilter: true,
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
    };
    this.mDdlPhysicianSettings = {
      singleSelection: false,
      idField: 'physicianEmaiId',
      textField: 'physicianName',
      enableCheckAll: true,
      itemsShowLimit: 1,
      allowSearchFilter: true,
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
    };
  }

  get f() { return this.FilterForm.controls; }

  getFacilities() {
    this.commonServ.startLoading();
    this.reportsServ.getGroupWiseFacilities().subscribe((p: any) => {
      this.commonServ.stopLoading();
      this.listOfFacilities = p;
    }, error => { console.error(error.status); });
  }

  getPhysiciansByFacility(facillity) {
    this.commonServ.startLoading();
    this.reportsServ.getGroupNameByFacility(facillity).subscribe((p: any) => {
      this.commonServ.stopLoading();
      this.selectedListOfPhysicians = [];
      this.selectedListOfGroups = [];
      this.listOfGroups = [];
      this.listOfPhysicians = [];
      this.listOfGroups = p;
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  exportAsXLSX(): void {
    this.excelService.exportAsExcelFile(this.listOfReports, this.fileName);
  }

  filterReport() {

    this.submitted = true;
    if (this.FilterForm.invalid) {
      return;
    }
    let fromDate = new Date(this.FilterForm.value.encounterSeenDateFrom);
    let toDate = new Date(this.FilterForm.value.encounterSeenDateTo);
    let time = toDate.getTime() - fromDate.getTime();
    let days = time / (1000 * 3600 * 24);
    if (days >= 365) {
      this.toastr.error("Please select the Encounter seen dates with in 1 year", '', { timeOut: 9000 });
      return;
    }
    if (fromDate > toDate) {
      this.toastr.error("From Date can't be greater than To Date", '', { timeOut: 9000 });
      return;
    }

    this.commonServ.startLoading();
    let rfBQuere: string = '0';
    if (this.FilterForm.value.removedFromBilligQueue)
      rfBQuere = '1';
    let dchged: string = '0';
    if (this.FilterForm.value.discharged)
      dchged = '1';
    let group_names: string = '';
    this.selectedListOfGroups.forEach((grp: any) => {
      group_names = group_names + grp.group_name + ',';
    });
    let physyn_names: string = '';
    this.selectedListOfPhysicians.forEach((grp: any) => {
      physyn_names = physyn_names + grp.physicianEmaiId + ',';
    });
    this.request.FACILITY = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.GROUPNAMES = this.encrDecr.set(group_names);
    this.request.PHYSICIANS = this.encrDecr.set(physyn_names);
    this.request.FROMDATE = this.encrDecr.set(this.FilterForm.value.encounterSeenDateFrom);
    this.request.TODATE = this.encrDecr.set(this.FilterForm.value.encounterSeenDateTo);
    this.request.BillStatus = this.encrDecr.set(this.FilterForm.value.ddlBill);
    this.request.sRemoveBillingQueue = this.encrDecr.set(rfBQuere);
    this.request.sIsDischargeDate = this.encrDecr.set(dchged);
    this.fileName = "";
    this.reportsServ.getEncounterAndDeletedEncounterReportData(this.request,this.router.url).subscribe((p: any) => {
      this.p = 1;
      this.listOfReports = p;
      this.listOfReportsInitial = p;
      this.request = {};
      let fDate = new Date(this.FilterForm.value.encounterSeenDateFrom);
      let fDateInFormat = this.datepipe.transform(fDate, 'dd-MMM-yyyy');
      let tDate = new Date(this.FilterForm.value.encounterSeenDateTo);
      let tDateInFormat = this.datepipe.transform(tDate, 'dd-MMM-yyyy');
      this.fileName = this.FilterForm.value.ddlFacility + '_' + fDateInFormat + '_' + tDateInFormat + '_' + $('#ddlPhysician option:selected').text();
      this.commonServ.stopLoading();
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  updateOrderByPatient(){
    this.orderByPatient = this.orderByPatient == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByAccount(){
    this.orderByAccount = this.orderByAccount == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByPatientMRNDOB(){
    this.orderByPatientMRNDOB = this.orderByPatientMRNDOB == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByPhysician(){
    this.orderByPhysician = this.orderByPhysician == 'asc' ? 'desc' : 'asc';
  }
  updateOrderBycreatedDate(){
    this.orderBycreatedDate = this.orderBycreatedDate == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByEncounterSeenDate(){
    this.orderByEncounterSeenDate = this.orderByEncounterSeenDate == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByAuditorAuditRequired(){
    this.orderByAuditorAuditRequired = this.orderByAuditorAuditRequired == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByAuditorAuditDate(){
    this.orderByAuditorAuditDate = this.orderByAuditorAuditDate == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByCoordinatorApprovalRequired(){
    this.orderByCoordinatorApprovalRequired = this.orderByCoordinatorApprovalRequired == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByCoordinatorApproval(){
    this.orderByCoordinatorApproval = this.orderByCoordinatorApproval == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByCoordinatorApprovedDate(){
    this.orderByCoordinatorApprovedDate = this.orderByCoordinatorApprovedDate == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByMarkeD_AS_BILL_BY(){
    this.orderByMarkeD_AS_BILL_BY = this.orderByMarkeD_AS_BILL_BY == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByMarkeD_AS_BILLED_DATE(){
    this.orderByMarkeD_AS_BILLED_DATE = this.orderByMarkeD_AS_BILLED_DATE == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByRecorD_REMOVED_BY(){
    this.orderByRecorD_REMOVED_BY = this.orderByRecorD_REMOVED_BY == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByRecorD_REMOVED_DATE(){
    this.orderByRecorD_REMOVED_DATE = this.orderByRecorD_REMOVED_DATE == 'asc' ? 'desc' : 'asc';
  }
  updateOrderBydeleted_On(){
    this.orderBydeleted_On = this.orderBydeleted_On == 'asc' ? 'desc' : 'asc';
  }
  updateOrderBydeleted_By(){
    this.orderBydeleted_By = this.orderBydeleted_By == 'asc' ? 'desc' : 'asc';
  }
  updateOrderBydeleted_Reason(){
    this.orderBydeleted_Reason = this.orderBydeleted_Reason == 'asc' ? 'desc' : 'asc';
  }
  updateOrderBydeleted_Role(){
    this.orderBydeleted_Role = this.orderBydeleted_Role == 'asc' ? 'desc' : 'asc';
  }

  sortColumn(columnBy) {
    this.sortColumnBy = columnBy;
    switch (columnBy) {
      case ('patienT_NAME'): {
        this.updateOrderByPatient();
        this.orderBy = this.orderByPatient;
        break;
      }
      case ('patientaccountnumber'): {
        this.updateOrderByAccount();
        this.orderBy = this.orderByAccount;
        break;
      }
      case ('dob'): {
        this.updateOrderByPatientMRNDOB();
        this.orderBy = this.orderByPatientMRNDOB;
        break;
      }
      case ('physiciaN_NAME'): {
        this.updateOrderByPhysician();
        this.orderBy = this.orderByPhysician;
        break;
      }
      case ('createdDate'): {
        this.updateOrderBycreatedDate();
        this.orderBy = this.orderBycreatedDate;
        break;
      }
      case ('encounterseendate'): {
        this.updateOrderByEncounterSeenDate();
        this.orderBy = this.orderByEncounterSeenDate;
        break;
      }
      case ('auditorAuditRequired'): {
        this.updateOrderByAuditorAuditRequired();
        this.orderBy = this.orderByAuditorAuditRequired;
        break;
      }
      case ('auditorAuditDate'): {
        this.updateOrderByAuditorAuditDate();
        this.orderBy = this.orderByAuditorAuditDate;
        break;
      }
      case ('coordinatorApprovalRequired'): {
        this.updateOrderByCoordinatorApprovalRequired();
        this.orderBy = this.orderByCoordinatorApprovalRequired;
        break;
      }
      case ('coordinatorApproval'): {
        this.updateOrderByCoordinatorApproval();
        this.orderBy = this.orderByCoordinatorApproval;
        break;
      }
      case ('coordinatorApprovedDate'): {
        this.updateOrderByCoordinatorApprovedDate();
        this.orderBy = this.orderByCoordinatorApprovedDate;
        break;
      }
      case ('markeD_AS_BILL_BY'): {
        this.updateOrderByMarkeD_AS_BILL_BY();
        this.orderBy = this.orderByMarkeD_AS_BILL_BY;
        break;
      }
      case ('markeD_AS_BILLED_DATE'): {
        this.updateOrderByMarkeD_AS_BILLED_DATE();
        this.orderBy = this.orderByMarkeD_AS_BILLED_DATE;
        break;
      }
      case ('recorD_REMOVED_BY'): {
        this.updateOrderByRecorD_REMOVED_BY();
        this.orderBy = this.orderByRecorD_REMOVED_BY;
        break;
      }
      case ('recorD_REMOVED_DATE'): {
        this.updateOrderByRecorD_REMOVED_DATE();
        this.orderBy = this.orderByRecorD_REMOVED_DATE;
        break;
      }
      case ('deleted_On'): {
        this.updateOrderBydeleted_On();
        this.orderBy = this.orderBydeleted_On;
        break;
      }
      case ('deleted_By'): {
        this.updateOrderBydeleted_By();
        this.orderBy = this.orderBydeleted_By;
        break;
      }
      case ('deleted_Reason'): {
        this.updateOrderBydeleted_Reason();
        this.orderBy = this.orderBydeleted_Reason;
        break;
      }
      case ('deleted_Role'): {
        this.updateOrderBydeleted_Role();
        this.orderBy = this.orderBydeleted_Role;
        break;
      }
      default: {
        break;
      }
    }

    this.listOfReports = this.sortOrderBy(this.listOfReports, columnBy, this.orderBy)
  }

  sortOrderBy(list, sortBy, sortOrder) {
    let newArray = [];
    this.p = 1;
    if (sortOrder == 'asc') {
      newArray = list.sort(
        function (a, b) {
          let sortNest: number = (b[sortBy] > a[sortBy]) ? -1 : 0;
          let sort: number = (a[sortBy] > b[sortBy]) ? 1 : sortNest;
          return sort;
        }
      );
    }
    else if (sortOrder == 'desc') {
      newArray = list.sort(
        function (a, b) {
          let sortNest: number = (b[sortBy] > a[sortBy]) ? 1 : 0;
          let sort: number = (a[sortBy] > b[sortBy]) ? -1 : sortNest;
          return sort;
        });
    }
    return newArray;
  }

  search(searchKey) {
    clearTimeout(this.timeout);

    this.timeout = setTimeout(() => {
      if (this.listOfReports.length)
        this.listOfReports = this.filterByValue(this.listOfReportsInitial, searchKey)
    }, 2000);

  }

  filterByValue(array, string) {
    return array.filter(o => {
      return Object.keys(o).some(k => {
        if (typeof o[k] === 'string')
          return o[k].toLowerCase().includes(string.toLowerCase());
      });
    });
  }

  onGroupSelect(item: any) {
    this.commonServ.startLoading();
    let group_names: string = '';
    this.selectedListOfGroups.forEach((grp: any) => {
      group_names = group_names + grp.group_name + ',';
    });
    this.reportsServ.getPhysiciansByGroup(group_names).subscribe((p: any) => {
      this.commonServ.stopLoading();
      this.selectedListOfPhysicians = [];
      this.listOfPhysicians = [];
      this.listOfPhysicians = p;
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  onGroupSelectAll(item: any, type: string) {
    this.commonServ.startLoading();
    let group_names: string = '';
    if (type == 'S') {
      this.listOfGroups.forEach((grp: any) => {
        group_names = group_names + grp.group_name + ',';
      });
      this.reportsServ.getPhysiciansByGroup(group_names).subscribe((p: any) => {
        this.commonServ.stopLoading();
        this.selectedListOfPhysicians = [];
        this.listOfPhysicians = [];
        this.listOfPhysicians = p;
      }, error => {
        this.commonServ.stopLoading();
      });
    }
    else {
      this.commonServ.stopLoading();
      this.selectedListOfPhysicians = [];
      this.listOfPhysicians = [];
    }
  }

}
