<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">

        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new px-0 py-2" style="background: #0169ab;">
                    <div class="align-items-center mx-0 row">
                        <div class="col-12 col-md-4 py-1 pl-2 pr-1 position-relative">
                            <div class="input-group">
                                <input type="text" class="form-control small" maxlength="1000"
                                    placeholder="Search for facility code, facility full name, address, city, state, zip & time zone"
                                    aria-label="Search" aria-describedby="basic-addon2" [(ngModel)]="search"
                                    [ngModelOptions]="{standalone: true}">
                                <div class="input-group-append">
                                    <button class="bg-gradient-light btn text-dark" type="button">
                                        <i class="fas fa-search fa-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 px-1 py-1 small text-white">
                            <div class="form-check form-check-inline">
                                <input type="checkbox" class="form-check-input" id="IsPrimeHospital"
                                    [(ngModel)]="searchIsPrimeHospital">
                                <label for="IsPrimeHospital" class="form-check-label">Is Prime Hospital</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input type="checkbox" class="form-check-input" id="IsCoordinatorApproval"
                                    [(ngModel)]="searchIsCoordinatorApproval">
                                <label for="IsCoordinatorApproval" class="form-check-label">Is Coordinator
                                    Approval</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input type="checkbox" class="form-check-input" id="IsAuditorApproval"
                                    [(ngModel)]="searchIsAuditorApproval">
                                <label for="IsAuditorApproval" class="form-check-label">Is Auditor Approval</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input type="checkbox" class="form-check-input" id="IsAdmissionDateEdit"
                                    [(ngModel)]="searchIsAdmissionDateEdit">
                                <label for="IsAdmissionDateEdit" class="form-check-label">Is Admission Date Edit</label>
                            </div>
                        </div>
                        <div class="col-12 col-md-2 py-1 pl-1 pr-2">
                            <button class="btn btn-outline-info btn-block" (click)='openAddpopup()' data-toggle="modal"
                                data-target="#AddorEditFacility"><i class="far fa-hospital"></i> Add Facility</button>
                        </div>
                    </div>
                </div>
                <!-- Card Body -->
                <div class="card-body px-2 py-1">
                    <div class="row mx-0">
                        <div class="col-12 px-0">

                            <table class="table table-striped table-bordered table-hover">
                                <tr>
                                    <th class="text-center">Facility Name</th>
                                    <th class="text-center">Time Zone</th>
                                    <th class="text-center">Address</th>
                                    <th class="text-center">City</th>
                                    <th class="text-center">State</th>
                                    <th class="text-center">Zip Code</th>
                                    <th class="text-center">Is Prime Hospital</th>
                                    <th class="text-center">Is Admission Date Edit</th>
                                    <th class="text-center">Action</th>
                                </tr>
                                <tr
                                    *ngFor="let item of listOfFacilities|gridFilter:{isPrimeHospital:searchIsPrimeHospital}:false|gridFilter:{coordinatorAppRequired:searchIsCoordinatorApproval}:false|gridFilter:{isAuditRequired:searchIsAuditorApproval}:false|gridFilter:{isAdissionDateEdit:searchIsAdmissionDateEdit}:false|gridFilter:{facility_Full_Name:search,facilityName:search,timE_ZONE_NAME:search,facility_Address:search,city:search,statE_NAME:search,zip_Code:search}:false |paginate:{ itemsPerPage:10,currentPage:p};let i=index">
                                    <td><span>{{item.facility_Full_Name}}</span><span>(</span><span>{{item.facilityName}}</span><span>)</span>
                                    </td>
                                    <td class="text-center">{{item.timE_ZONE_NAME}}</td>
                                    <td>{{item.facility_Address}}</td>
                                    <td class="text-center">{{item.city}}</td>
                                    <td class="text-center">{{item.statE_NAME}}</td>
                                    <td class="text-center">{{item.zip_Code}}</td>
                                    <td class="text-center">{{item.isPrimeHospital?'YES':'NO'}}</td>
                                    <td class="text-center">{{item.isAdissionDateEdit?'YES':'NO'}}</td>
                                    <td class="text-center"> <button
                                            class="btn btn-sm btn-outline-info mb-1 text-nowrap"
                                            (click)='openEditpopup(item)' data-toggle="modal"
                                            data-target="#AddorEditFacility"><i class="fa-edit fas"></i> Edit
                                            Facility</button></td>
                                </tr>
                                <tr>
                                    <td colspan="10" class="m-0 p-0" style="background: white !important;">
                                        <pagination-controls previousLabel="" nextLabel="" (pageChange)="p=$event"
                                            (pageBoundsCorrection)="p=$event"></pagination-controls>
                                    </td>
                                </tr>
                            </table>

                        </div>
                    </div>

                </div>

            </div>
        </div>
        <!-- Area Chart Ends -->
    </div>
</div>
<app-add-edit-facility [facilityObj]='facilityObj'></app-add-edit-facility>
<!-- Begin Page Content Ends -->