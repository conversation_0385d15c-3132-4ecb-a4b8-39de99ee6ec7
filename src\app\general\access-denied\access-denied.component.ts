import { Component, OnInit } from '@angular/core';
declare let $: any;

@Component({
  selector: 'app-access-denied',
  templateUrl: './access-denied.component.html',
  styleUrls: ['./access-denied.component.css']
})
export class AccessDeniedComponent implements OnInit {
  errorCode = '';
  errorDesc = '';
  errorTitle = '';

  ngOnInit(): void {
    $('#loading').hide();
    this.errorCode = history.state.errorCode ? history.state.errorCode : '';
    if (this.errorCode === 'consent_required') {
      this.errorTitle = 'Delegation Does Not Exist';
      this.errorDesc = 'The user or administrator has not consented to use the application with ID XXX-XXXX-XXXXX-XXXX. Send an interactive authorization request for this user and resource.';
    }
    else if (this.errorCode === 'interaction_required') {
      this.errorTitle = 'Interaction Required';
      this.errorDesc = 'The access grant requires interaction.';
    }
    else if (this.errorCode === 'login_required') {
      this.errorTitle = 'Login Required';
      this.errorDesc = 'This can happen if the third party cookies have been disabled in your browser. Please Re-enable the third party cookies in your browser and try to login again.';
    }
    else if (this.errorCode === 'unsupported_response_type') {
      this.errorTitle = 'Unsupported Response Type';
      this.errorDesc = 'The app returned an unsupported response type due to the following reasons: response type \'token\' is not enabled for the app response type \'id_token\' requires the \'OpenID\' scope -contains an unsupported OAuth parameter value in the encoded wctx.';
    }
    else if (this.errorCode === 'cookies_enable_required') {
      this.errorTitle = 'Login Required';
      this.errorDesc = 'This can happen if the cookies have been disabled in your browser. Please Re-enable the cookies in your browser and try to login again.';
    }
    else if (this.errorCode === 'popup-blocked') {
      this.errorTitle = 'popups are blocked in the browser';
      this.errorDesc = 'Error opening popup window. This can happen if you are using IE or if popups are blocked in the browser.';
    }
    else if (this.errorCode === 'Bad Request' || this.errorCode === 'Unknown Error' || this.errorCode === 'Unauthorized' || this.errorCode === 'Forbidden' || this.errorCode === 'Not Found' || this.errorCode === 'Request Timeout') {
      this.errorTitle = 'Something Went Wrong';
      this.errorDesc = 'Please contact your domain administrator';
    }
    else {
      this.errorTitle = 'Access Denied';
      this.errorDesc = 'Please contact your domain administrator for access';
    }
  }

}
