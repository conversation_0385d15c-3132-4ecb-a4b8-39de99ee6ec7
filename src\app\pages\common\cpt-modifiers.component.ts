import { Component, Input } from '@angular/core';
import { CommonService } from 'src/app/services/common/common.service';
declare let $: any;

@Component({
  selector: 'app-cpt-modifiers',
  templateUrl: './cpt-modifiers.component.html',
  styleUrls: ['./cpt-modifiers.component.css']
})
export class CptModifiersComponent {
  @Input() listOfModifier: Array<any> = [];
  @Input() cptType: string = "";
  @Input() encounterObj: any = {};
  public searchModfier: string;

  constructor(private readonly commonServ: CommonService) { }

  chkChangeEvent(val, event) {
    for (let item of this.listOfModifier) {
      if (item.modifierS_ID == event.target.id) {
        item.isExist = event.target.checked;
      }
    }
  }

  addModifiers(eObj, cptCode) {
    this.commonServ.startLoading();
    let modiArray = "";
    this.listOfModifier.forEach(x => {
      if (x.isExist) {
        modiArray = modiArray + ',' + x.modifiersname.split('-')[0];
      }
    });
    const index: number = eObj.listOfCpts.indexOf(cptCode);
    if (index !== -1 && modiArray) {
      for (let i = eObj.listOfCpts.length - 1; i >= 0; i--) {
        if (eObj.listOfCpts[i] === cptCode) {
          eObj.listOfCpts[i] = cptCode.split('(--,')[0] + '(--' + modiArray + ')';
        }
      }
      let cptEixt: any = eObj.groupedListOfCpts.find(x => x.cpt_name == cptCode);
      cptEixt.cpt_name = cptCode.split('(--,')[0] + '(--' + modiArray + ')';
      $(".btnSave" + eObj.encounteR_ID).show();
    } else if (index !== -1) {
      eObj.listOfCpts[index] = cptCode.split('(--,')[0];
      $(".btnSave" + eObj.encounteR_ID).show();
    }
    this.commonServ.stopLoading();
  }

}
