import { Component, Input } from '@angular/core';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { DeletedEncounterComponent } from './deleted-encounter.component';
declare let $: any;

@Component({
  selector: 'app-encounter',
  templateUrl: './encounter.component.html',
  styles: []
})
export class EncounterComponent {
  @Input() lisfOfEncounters: Array<any> = [];
  @Input() PatientObject: any = {};
  @Input() Index: number;
  public request: any = {};
  public encounterIds: string = "";
  @Input() firstEcounterIdToOpen: string = "";
  public lisfOfCPTData: Array<any>;
  public oldlistOfCpts: Array<any> = [];
  public encounterObj: any = {};
  public cptType: string;
  public lisfOfICDData: Array<any> = [];
  public icdType: string;
  public listOfHistory: Array<any> = [];

  constructor(private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService, private readonly deleteEntrcomp: DeletedEncounterComponent,
  ) { }

  convertCPTCodes(list) {
    let newList = list.map((cpt) => {
      cpt = cpt.substring(0, 5);
      return cpt;
    });
    newList = newList.toString();
    return newList;
  }

  getTwoCPTCodes(list) {
    let newString = "";
    let newList = list.map((cpt, index) => {
      cpt = cpt.substring(0, 5);
      if (index == 1 || list.length == 1) {
        newString = newString.concat(cpt);
      } else if (index < 2) {
        newString = newString.concat(cpt + ",");
      }
      return cpt;
    });

    if (newList.length > 2) {
      newString = newString.concat("...");
    }
    return newString;
  }

  getCPTData(item, phyEmailId, type) {
    this.commonServ.startLoading();
    $("#favorite-tab").click();
    this.lisfOfCPTData = [];
    item.listOfRemovedCpts = [];
    this.oldlistOfCpts = [];
    this.deleteEntrcomp.encounterObj = item;
    this.encounterObj = item;
    let existingArray: Array<any> = [];
    item.listOfCpts.forEach(a => {
      existingArray.push(a.split('(--')[0].trim());
      this.oldlistOfCpts.push(a);
    });
    this.cptType = type;
    this.request.PHYSICIANMAILID = this.encrDecr.set(phyEmailId);
    this.commonServ.getCPTData(this.request).subscribe((p: any) => {
      this.lisfOfCPTData = p;
      this.request = {};
      this.lisfOfCPTData.forEach(x => {
        x.isExist = existingArray.includes(x.cptname);
        x.isOld = existingArray.includes(x.cptname);
        let ittem = this.encounterObj.groupedListOfCpts.find(a => a.cpt_name.split('(--')[0].trim() == x.cptname);
        x.cpt_count = ittem ? ittem?.cpt_count : 0;
      });
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
      console.error(error.status);
    });
  }

  getICDData(item, phyEmailId, type) {
    this.commonServ.startLoading();
    $("#favorite-tab1").click();
    this.lisfOfICDData = [];
    item.listOfRemovedIcds = [];
    this.deleteEntrcomp.encounterObj = item;
    this.encounterObj = item;
    let existingArray = item.listOfIcds;
    this.icdType = type;
    this.request.PHYSICIANMAILID = this.encrDecr.set(phyEmailId);
    this.commonServ.getICDData(this.request).subscribe((p: any) => {
      this.lisfOfICDData = p;
      this.request = {};
      this.lisfOfICDData.forEach(x => {
        if (existingArray.includes(x.icdname)) {
          x.isExist = true;
        }
        else {
          x.isExist = false;
        }
      });
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
      console.error(error.status);
    });
  }

  viewHistory(envId) {
    this.commonServ.startLoading();
    this.request.sEncounterId = this.encrDecr.set(envId);
    this.commonServ.getEncouterHistory(this.request).subscribe((p: any) => {
      this.request = {};
      this.deleteEntrcomp.listOfHistory = p;
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
      console.error(error.status);
    });
  }

}
