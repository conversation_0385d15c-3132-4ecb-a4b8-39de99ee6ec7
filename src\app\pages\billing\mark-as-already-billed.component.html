<div class="modal fade" id="markAsAlreadyBilled" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header py-2">
        <h5 class="modal-title" id="exampleModalCenterTitle">Mark as already Billed</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="row mx-0 align-items-center">
          <div class="col-12">
            Are you sure want to mark as already billed?
          </div>
          <div class="col-12 mt-4">
            <input type="tex" class="form-control" [(ngModel)]="billedEncounterId" maxlength="100"
              placeholder="Please enter Next Gen encounter number" />
            <div class="text-danger" *ngIf="submitted && billedEncounterId==''">Next Gen encounter number is requied
            </div>
          </div>
          <div class="col-12 text-center mt-4">
            <button class="btn btn-outline-info mr-3"
              (click)="confirmMarkAsBilled(encounterObj,PatientObject)">Yes</button>
            <button class="btn btn-outline-info" data-dismiss="modal">No</button>
          </div>
        </div>
      </div>
      <div class="modal-footer py-2">
      </div>
    </div>
  </div>
</div>