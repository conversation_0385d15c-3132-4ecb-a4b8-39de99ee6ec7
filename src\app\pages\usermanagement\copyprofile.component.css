.empSerach {
    max-height: 50vh;
    width: max-content;
    width: intrinsic;
    width: -moz-max-content;
    width: -webkit-max-content;
    min-width: 20vw;
    max-width: 50vw;
    background-color: #eee;
    overflow-y: auto;
    overflow-x: hidden;
    z-index: 1050;
    box-shadow: 1px 1px 5px 1px #ccc;
}

.empSerach .empSearchResults:not(:last-child) {
    border-bottom: 1px solid #fff;
}

.show {
    display: block !important;
}

.hide {
    display: none !important;
}

.myaccount1 {
    background: #1f7fa8;
    height: 6vh;
    width: 6vh;
    border-radius: 50%;
    position: relative;
    text-align: center;
    margin-bottom: 0.5rem;
}

.mylabel1 {
    font-size: 1rem;
    color: white;
    margin: 0.35rem auto;
    vertical-align: middle;
    text-transform: uppercase;
}