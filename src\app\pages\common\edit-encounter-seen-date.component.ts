import { Component, OnInit, Input } from '@angular/core';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { FormBuilder, FormGroup, Validators, AbstractControl } from '@angular/forms';
import { CommonService } from 'src/app/services/common/common.service';
import { CoordinatorService } from 'src/app/services/coordinator/coordinator.service';
import { DatePipe } from '@angular/common';
import { MtxDatetimepickerType, MtxDatetimepickerMode, MtxCalendarView } from '@ng-matero/extensions/datetimepicker';
declare let $:any;

@Component({
  selector: 'app-edit-encounter-seen-date',
  templateUrl: './edit-encounter-seen-date.component.html',
  styles: []
})
export class EditEncounterSeenDateComponent implements OnInit {
  type: MtxDatetimepickerType = 'datetime';
  mode: MtxDatetimepickerMode = 'portrait';
  startView: MtxCalendarView = 'month';
  multiYearSelector = false;
  touchUi = false;
  twelvehour = false;
  timeInterval = 1;
  timeInput = true;
  @Input() encounterObj:any;
  @Input() PatientObject:any={};
  public request:any={};
  public sSeenDateForm:FormGroup;
  public vali2:boolean=false;
  public vali3:boolean=false;
  public min:Date = new Date();
  public max:Date = new Date();
  public admissionDate:Date = new Date();
  public envSeenDate:string;
  public submitted:boolean=false;
  @Input() userType:string='';
  public maxDateTime:Date=new Date();
  constructor(private readonly coorServ:CoordinatorService,private readonly encrDecr: EncrDecrServiceService,private readonly fb: FormBuilder,private readonly commonServ:CommonService,public datepipe: DatePipe) { }

  ngOnInit() {
    this.sSeenDateForm = this.fb.group({
      envSeenDate: ['', Validators.required]
    });
  }
  get f() { return this.sSeenDateForm.controls; }

  editEncounterSeenDate(eObj) { 

    this.submitted=true;
    let today = new Date();
    this.vali2 = false;
    this.vali3 = false;
    this.admissionDate = new Date(eObj.admission_Date);
    this.request.MARKASSEENDATE1 = this.datepipe.transform(this.sSeenDateForm.value.envSeenDate, 'MM/dd/yyyy hh:mm a');
    this.request.MARKASSEENDATE = this.encrDecr.set(this.datepipe.transform(this.sSeenDateForm.value.envSeenDate, 'MM/dd/yyyy hh:mm:ss a'));
    if (this.sSeenDateForm.invalid) {
      return;
    }
    else if (new Date(this.sSeenDateForm.value.envSeenDate) < (new Date(this.admissionDate.setHours(-72)))) {
      this.vali2 = true;
      return;
    }
    else if (new Date(this.sSeenDateForm.value.envSeenDate.setHours(0,0,0,0)) > new Date(today.setHours(0,0,0,0))) {
      this.vali3 = true;
      return;
    }
    else {
      this.commonServ.startLoading();
      this.request.FACTIMEZONE = this.encrDecr.set(eObj.factimezone);
      this.request.sENCOUNTER_ID = this.encrDecr.set(eObj.encounteR_ID);
      this.request.ROLE = this.encrDecr.set(this.userType);
      this.coorServ.editEncounterSeenDate(this.request).subscribe((p: any) => {      
        this.submitted=false;
        if (p > 0) {
          eObj.encounterseendate = this.request.MARKASSEENDATE1;
          this.PatientObject.last_activity_date=this.datepipe.transform(new Date(), 'MM/dd/yyyy h:mm:ss a');
          this.request={};
          $("#updateSeenDate").modal('hide');
        }
        this.commonServ.stopLoading();
      }, error => { 
        this.request={};
        this.commonServ.stopLoading();
        console.error(error.status);
       });
    }
  }

  encounterSeenDateValidation() {
    return (control: AbstractControl): { [key: string]: boolean } | null => {
        if (control.value !== undefined && this.admissionDate !==undefined && (new Date(control.value)<=this.admissionDate)) {
            return { 'fromDate': true };
        } else if(control.value !== undefined && (new Date(control.value)>=new Date())){
          return { 'toDate': true };
        }
        return null;
    };
  }

}