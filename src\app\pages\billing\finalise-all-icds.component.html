<div class="modal fade" id="finalizeICdsAll" tabindex="-1" aria-labelledby="exampleModalCenterTitle"
  maria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-xl">
    <div class="modal-content">
      <div class="modal-header py-2">
        <h5 class="modal-title" id="exampleModalCenterTitle">Send Encounter(s) To Kareo</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <!-- search start -->
        <div class="row">
          <div class="col-12">
            <!-- tabs start -->
            <nav>
              <div class="nav nav-tabs border-bottom-0" id="nav-tabpop" role="tablist">
                <a class="nav-item nav-link pop-nav px-5"
                  [class.active]="!kareoPreConfirmationMassage&&!confirmationMessage">Encounter(s) Selection</a>
                <a class="nav-item nav-link pop-nav px-5"
                  [class.active]="kareoPreConfirmationMassage&&!confirmationMessage">Finalize ICDs</a>
                <a class="nav-item nav-link pop-nav px-5"
                  [class.active]="kareoPreConfirmationMassage&&confirmationMessage&&!ssnConfirmationMessage">Validate
                  SSN</a>
                <a class="nav-item nav-link pop-nav px-5"
                  [class.active]="kareoPreConfirmationMassage&&confirmationMessage&&ssnConfirmationMessage">Pre-Confirmation</a>
              </div>
            </nav>
            <div class="card px-3 py-2 shadow tab-content" id="nav-tabContentKareo">

              <div class="tab-pane scrolling"
                [ngClass]="{'show active' : !kareoPreConfirmationMassage&&!confirmationMessage,'fade':kareoPreConfirmationMassage&&confirmationMessage}">
                <div class="row">
                  <div class="col-12 my-2" *ngFor="let item of lisfOfGroupEncountersForKareo">
                    <ng-container
                      *ngIf="item.status=='1'&&item.integrationSystem=='Kareo'&&((item.kareo_ID>0&&item.kareo_Batch_No==0)||(item.existing_Kareo_ID>0&&item.existing_Kareo_Batch_No==0))">
                      <div class="custom-control custom-checkbox">
                        <input *ngIf="item.encounteR_ID" type="checkbox" class="custom-control-input"
                          id="{{item.encounteR_ID}}" value="{{item.encounteR_ID}}" (change)="chkChangeEvent($event)">
                        <label class="custom-control-label" for="{{item.encounteR_ID}}">
                          <span class="pl-4"><i>Encounter created at:</i>
                            {{item.encounterseendate}}--<span class="small">
                              <i>By <span class="text-color-green">{{item.posT_TO_BILLED_BY}}</span></i></span></span>
                          <span class="ml-1">[</span>
                          <span *ngFor="let cpt of item.listOfCpts; let isLast=last">{{cpt | slice:0:5}}{{isLast ? '' :
                            ', '}}</span>
                          <span>]</span>
                        </label>
                      </div>
                    </ng-container>
                  </div>
                  <div class="text-danger ml-3" *ngIf="isValidationMessage">
                    Please select atleast one encounter.
                  </div>
                </div>
                <div class="modal-footer py-2">
                  <button class="btn btn-outline-info float-right"
                    (click)="finalizeICdsAll(lisfOfGroupEncountersForKareo)">Continue</button>
                </div>
              </div>

              <div class="tab-pane scrolling"
                [ngClass]="{'show active' :kareoPreConfirmationMassage&&!confirmationMessage,'fade':!kareoPreConfirmationMassage&&confirmationMessage}">
                <div class="row align-items-center">
                  <div class="col-11">
                    <select id="ddlIcdAll" name="ddlIcdAll" multiple class="border card form-control shadow"
                      style="min-height:200px;">
                      <option [value]="icd" *ngFor="let icd of groupOfIcds;let i = index" (keyup)="clickedRow(i)" (click)="clickedRow(i)">
                        {{icd}}
                      </option>
                    </select>
                  </div>
                  <div class="col-1 text-center">
                    <img id="upAll" src="../../../assets/img/uparrow.png" alt="uparrow" title="Up"
                      class="cursor-pointer pr-1 up-arrow" (click)="moveSelectedUp()" (keyup)="moveSelectedUp()">
                    <img id="downAll" src="../../../assets/img/downarrow.png" alt="downarrow" title="Down"
                      class="cursor-pointer down-arrow" (click)="moveSelectedDown()" (keyup)="moveSelectedDown()">
                  </div>
                </div>
                <div class="modal-footer py-2">

                  <button class="btn btn-outline-info float-right" (click)="back1()">Back</button>
                  <button class="btn btn-outline-info float-right" (click)="confirmIcds()">Continue</button>

                </div>
              </div>

              <div class="tab-pane scrolling"
                [ngClass]="{'show active' :kareoPreConfirmationMassage&&confirmationMessage&&!ssnConfirmationMessage,'fade':!kareoPreConfirmationMassage&&!confirmationMessage&&ssnConfirmationMessage}">
                <div class="row px-5 py-2" [formGroup]="patientForm">
                  <div class="col-3">
                    <span>Patient Name</span>
                  </div>
                  <div class="col-9">
                    <label class="has-float-label w-100">
                      <input class="form-control input-border disabled" type="text" formControlName="txtPatientName"
                        [(ngModel)]="PatientObject.patient_Name">
                    </label>
                  </div>

                  <div class="col-3">
                    <span>Account Number</span>
                  </div>
                  <div class="col-9">
                    <span class="has-float-label w-100">
                      <input class="form-control input-border disabled" type="text" formControlName="txtAccountNo"
                        [(ngModel)]="PatientObject.account_Number">
                    </span>
                  </div>

                  <div class="col-3">
                    <span>Facility Name</span>
                  </div>
                  <div class="col-9">
                    <span class="has-float-label w-100">
                      <input class="form-control input-border disabled" type="text" formControlName="txtFacilityName"
                        [(ngModel)]="PatientObject.facility_Name">
                    </span>
                  </div>

                  <div class="col-3">
                    <span>SSN</span>
                  </div>
                  <div class="col-9">
                    <label class="has-float-label w-100">
                      <input class="form-control input-border" maxlength="9" type="text" formControlName="txtSSN"
                        [(ngModel)]="PatientObject.ssn_no">
                      <div class="text-danger" *ngIf="submitted && f['txtSSN'].errors?.['maxLength']">Max length 9
                        characters</div>
                      <div class="text-danger" *ngIf="submitted && f['txtSSN'].errors?.['pattern']">SSN should be a
                        number</div>
                    </label>
                  </div>
                </div>
                <div class="modal-footer py-2">
                  <button class="btn btn-outline-info float-right" (click)="back2()">Back</button>
                  <button class="btn btn-outline-info float-right" (click)="updateSSNConfirm()">Update SSN &
                    Continue</button>
                  <button class="btn btn-outline-info float-right" (click)="ssnConfirm()">Continue</button>
                </div>
              </div>

              <div class="tab-pane scrolling"
                [ngClass]="{'show active' :kareoPreConfirmationMassage&&confirmationMessage&&ssnConfirmationMessage,'fade':!kareoPreConfirmationMassage&&!confirmationMessage&&!ssnConfirmationMessage}">
                <div class="row">
                  <div class="col-12">
                    <div [innerHTML]="confirmationMessage"></div>
                  </div>
                </div>
                <div class="modal-footer py-2">
                  <button class="btn btn-outline-info float-right" (click)="back3()">Back</button>
                  <button class="btn btn-outline-info float-right"
                    (click)="submitEncounterToKareoAll(lisfOfGroupEncountersForKareo,PatientObject)">Submit</button>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>