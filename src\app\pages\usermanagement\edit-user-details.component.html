<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <div class="modal-header">
                    <button class="btn btn-secondary btn-block w-auto"
                        [routerLink]="['/usermanagement/add-modify-user']" [state]="{filterObj:filterObj}">Back</button>
                </div>
                <div class="card-body px-md-3" [formGroup]="userForm">

                    <div class="row justify-content-center" *ngIf="userDetails.user_id==-1">
                        <div class="col-12 col-md-6 py-3 shadow border border-blue rounded">
                            <div class="form-group my-auto">
                                <label class="my-auto" for="txtUserName">Enter AD User Id / Display Name</label>
                                <div class="has-float-label w-100 my-auto">
                                    <input class="form-control input-border"
                                        (input)="term$.next($any($event.target).value)" (keyup)="showSearch()"
                                        id="txtUserName" type="text" placeholder="AD User Id / Display Name"
                                        autocomplete="off">
                                </div>
                                <div *ngIf="results$ | async as users"
                                    class="row mx-0 position-absolute empSerach {{seachClass}}">
                                    <div class="col-12 px-0 py-0">
                                        <div class="row mx-0 align-items-center px-0 py-1 empSearchResults"
                                            *ngFor="let user of results$ | async">
                                                <div class="col-2 px-1">
                                                    <div class="myaccount1">
                                                        <span class="mylabel1" (keyup)="chkChangeEvent(user)" (click)="chkChangeEvent(user)">{{user.email_id | slice:0:2}}</span>
                                                    </div>
                                                </div>
                                                <div class="col-10 pl-0">
                                                    <span (keyup)="chkChangeEvent(user)" (click)="chkChangeEvent(user)">{{user.display_name}}<br />{{user.email_id}}</span>
                                                </div>
                                        </div>
                                    </div>
                                    <div class="col-12 px-0 py-0">
                                        <div class="row mx-0 align-items-center px-0 py-1 empSearchResults">
                                            <div class="col-12 px-1 text-center">
                                                <span>No Results Found</span>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mx-0" *ngIf="userDetails.user_id!=-1">
                        <div class="col-12 col-md-6">
                            <div class="form-group">
                                <label class="my-auto" for="ddlTitle">Title</label>
                                <select class="form-control" [(ngModel)]="userDetails.title_id"
                                    [ngClass]="{ 'is-invalid': submitted && f['ddlTitle'].errors}"
                                    formControlName="ddlTitle" id="ddlTitle">
                                    <option value="0">Select Title</option>
                                    <option [value]="s.title_id" *ngFor="let s of listOfTitles">
                                        <span>{{s.title_name}}</span>
                                    </option>
                                </select>
                            </div>

                        </div>
                        <div class="col-12 col-md-6">
                            <div class="form-group">
                                <label class="my-auto" for="FirstName">First Name</label>
                                <input class="form-control input-border disabled" [(ngModel)]="userDetails.firstname"
                                    [ngClass]="{ 'is-invalid': submitted && f['txtFirstName'].errors}" type="text"
                                    formControlName="txtFirstName" id="txtFirstName">
                            </div>
                        </div>
                        <div class="col-12 col-md-6">
                            <div class="form-group">
                                <label class="my-auto" for="FirstName">Middle Name</label>
                                <input class="form-control input-border" [(ngModel)]="userDetails.middleName"
                                    [ngClass]="{ 'is-invalid': submitted && f['txtMiddleName'].errors}" type="text"
                                    formControlName="txtMiddleName" id="txtMiddleName">
                                <div class="text-danger"
                                    *ngIf="submitted && f['txtMiddleName'].errors && f['txtMiddleName'].errors['pattern']">
                                    Please enter valid text (* allowing alphanumeric, spaces and punctuations)</div>
                            </div>
                        </div>

                        <div class="col-12 col-md-6">
                            <div class="form-group">
                                <label class="my-auto" for="LastName">Last Name</label>
                                <input class="form-control input-border disabled" [(ngModel)]="userDetails.lastname"
                                    [ngClass]="{ 'is-invalid': submitted && f['txtLastName'].errors}" type="text"
                                    formControlName="txtLastName" id="txtLastName">
                            </div>
                        </div>

                        <div class="col-12 col-md-6">
                            <div class="form-group">
                                <label class="my-auto" for="txtDisplayName">Display Name</label>
                                <input class="form-control input-border disabled" [(ngModel)]="userDetails.display_name"
                                    [ngClass]="{ 'is-invalid': submitted && f['txtDisplayName'].errors}" type="text"
                                    formControlName="txtDisplayName" id="txtDisplayName">
                            </div>
                        </div>
                        <div class="col-12 col-md-6">
                            <div class="form-group">
                                <label class="my-auto" for="ddlFacility">Facility</label>
                                <select class="form-control" [(ngModel)]="userDetails.facility_id"
                                    [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}"
                                    formControlName="ddlFacility" id="ddlFacility">
                                    <option value="">Select Facility</option>
                                    <option [value]="s.facility_id" *ngFor="let s of listOfFacilities">
                                        <span>{{s.facility_name}}</span>
                                    </option>
                                </select>
                            </div>
                        </div>

                        <div class="col-12 col-md-6">
                            <div class="form-group">
                                <label class="my-auto" for="txtNPI">NPI</label>
                                <input class="form-control input-border" [(ngModel)]="userDetails.npi"
                                    [ngClass]="{ 'is-invalid': submitted && f['txtNPI'].errors}" type="text"
                                    formControlName="txtNPI" id="txtNPI">
                            </div>
                        </div>
                        <div class="col-12 col-md-6">
                            <div class="form-group">
                                <label class="my-auto" for="txtLoginID">Login ID</label>
                                <input class="form-control input-border disabled" [(ngModel)]="userDetails.email_id"
                                    maxlength="100" [ngClass]="{ 'is-invalid': submitted && f['txtLoginID'].errors}"
                                    type="text" formControlName="txtLoginID" id="txtLoginID">
                            </div>
                        </div>
                        <div class="col-12 col-md-6">
                            <div class="form-group">
                                <label class="my-auto" for="ddlAccountStatus">Account Status</label>
                                <select class="form-control" [(ngModel)]="userDetails.accountstatus"
                                    [ngClass]="{ 'is-invalid': submitted && f['ddlAccountStatus'].errors}"
                                    formControlName="ddlAccountStatus" id="ddlAccountStatus">
                                    <option value="">Select Account Status</option>
                                    <option value="Enabled"><span>Enabled</span></option>
                                    <option value="Disabled"><span>Disabled</span></option>
                                </select>
                            </div>
                        </div>
                        <div class="col-12 col-md-6">
                            <div class="form-group">
                                <label class="my-auto" for="ddlSpeciality">Speciality</label>
                                <select class="form-control" [(ngModel)]="userDetails.specialty"
                                    [ngClass]="{ 'is-invalid': submitted && f['ddlSpeciality'].errors}"
                                    formControlName="ddlSpeciality" id="ddlSpeciality">
                                    <option value="">Select Speciality</option>
                                    <option [value]="s.speciality_name" *ngFor="let s of listOfSpecialities">
                                        <span>{{s.speciality_name}}</span>
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="col-12 col-md-6">
                            <div class="form-group">
                                <div class="row">
                                    <div class="col">
                                        <input type="checkbox" [attr.disabled]="adminModuleAccess=='NO'?true:null"
                                            (keyup)="physicianModuleAccessEvent()"
                                            (click)="physicianModuleAccessEvent()" id="Physician"
                                            [(ngModel)]="userDetails.physicianModuleAccess"
                                            formControlName="chkPhysician">
                                        <label for="Physician" class="ml-1">Physician Module Access</label>
                                    </div>
                                    <div class="col">
                                        <input type="checkbox" id="Coordinator"
                                            [attr.disabled]="adminModuleAccess=='NO'?true:null"
                                            [(ngModel)]="userDetails.coordinatorModuleAccess"
                                            formControlName="chkCoordinator">
                                        <label for="Coordinator" class="ml-1">Coordinator Module Access</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 col-md-6">
                            <div class="form-group">
                                <div class="row">
                                    <div class="col">
                                        <input type="checkbox" id="Auditor"
                                            [attr.disabled]="adminModuleAccess=='NO'?true:null"
                                            [(ngModel)]="userDetails.auditorModuleAccess" formControlName="chkAuditor">
                                        <label for="Auditor" class="ml-1">Auditor Module Access</label>
                                    </div>
                                    <div class="col">
                                        <input type="checkbox" id="Biller"
                                            [attr.disabled]="adminModuleAccess=='NO'?true:null"
                                            [(ngModel)]="userDetails.billerModuleAccess" formControlName="chkBiller">
                                        <label for="Biller" class="ml-1">Biller Module Access</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 col-md-6">
                            <div class="form-group">
                                <div class="row">
                                    <div class="col">
                                        <input type="checkbox" id="UserManagement"
                                            [attr.disabled]="adminModuleAccess=='NO'?true:null"
                                            [(ngModel)]="userDetails.userManagementAccess"
                                            formControlName="chkUserManagement">
                                        <label for="UserManagement" class="ml-1">User Management Module Access</label>
                                    </div>
                                    <div class="col">
                                        <input type="checkbox" id="Administrator"
                                            [attr.disabled]="adminModuleAccess=='NO'?true:null" id="Administrator"
                                            [(ngModel)]="userDetails.adminModuleAccess"
                                            formControlName="chkAdministrator">
                                        <label for="Administrator" class="ml-1">Administrator Module Access</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 col-md-6">
                            <div class="form-group">
                                <div class="row">
                                    <div class="col">
                                        <input type="checkbox" id="Resident"
                                            [attr.disabled]="adminModuleAccess=='NO'?true:null"
                                            (click)="residentModuleAccessEvent()" (keyup)="residentModuleAccessEvent()"
                                            [(ngModel)]="userDetails.residentModuleAccess"
                                            formControlName="chkResident">
                                        <label for="Resident" class="ml-1">Resident Module Access</label>
                                    </div>
                                    <div class="col">
                                        <input type="checkbox" id="Mobile"
                                            [attr.disabled]="adminModuleAccess=='NO'?true:null"
                                            [(ngModel)]="userDetails.allowPhysicianRounds" formControlName="chkMobile">
                                        <label for="Mobile" class="ml-1">Mobile Module Access</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 col-md-6">
                            <div class="form-group">
                                <div class="row">
                                    <div class="col">
                                        <input type="checkbox" id="Dashboard"
                                            [attr.disabled]="adminModuleAccess=='NO'?true:null"
                                            [(ngModel)]="userDetails.dashboardModuleAccess"
                                            formControlName="chkDashboard">
                                        <label for="Dashboard" class="ml-1">Dashboard Module Access</label>
                                    </div>
                                    <div class="col">
                                        <input type="checkbox" id="Report"
                                            [attr.disabled]="adminModuleAccess=='NO'?true:null"
                                            [(ngModel)]="userDetails.reportModuleAccess" formControlName="chkReport">
                                        <label for="Report" class="ml-1">Report Module Access</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-6">
                            <div class="form-group">
                                <div class="row">
                                    <div class="col">
                                        <input type="checkbox" id="KareoDashboard"
                                            [attr.disabled]="adminModuleAccess=='NO'?true:null"
                                            [(ngModel)]="userDetails.kareoDashboardModuleAccess"
                                            formControlName="chkKareoDashboard">
                                        <label for="KareoDashboard" class="ml-1">Kareo Dashboard Module Access</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="card-footer px-md-3 pb-md-3 p-2"
                        *ngIf="userDetails.user_id!=-1 && adminModuleAccess=='YES'">
                        <ng-container *ngIf="type=='edit';else elsSave">
                            <button *ngIf="adminModuleAccess=='YES'&&isRemoveCompleteAccess"
                                class="btn btn-sm btn-danger btn-style buttonstyle"
                                (click)="removeCompleteAccess()">Remove Complete Access</button>
                            <button class="btn btn-sm btn-outline-info float-right btn-style"
                                (click)="insertUpdateUserDetails()"><span
                                    *ngIf="userDetails.user_id==0">Save</span><span
                                    *ngIf="userDetails.user_id!=0">Update</span></button>
                        </ng-container>
                        <ng-template #elsSave>
                            <button *ngIf="adminModuleAccess=='YES'"
                                class="btn btn-sm btn-outline-info float-right btn-style"
                                (click)="insertUpdateUserDetails()">
                                <span *ngIf="userDetails.user_id==0">Save</span><span
                                    *ngIf="userDetails.user_id!=0">Update</span></button>
                        </ng-template>
                        <button class="btn btn-sm btn-outline-secondary float-right btn-style buttonstyle"
                            [routerLink]="['/usermanagement/add-modify-user']">Cancel</button>
                    </div>
                </div>
            </div>
        </div>
    </div>