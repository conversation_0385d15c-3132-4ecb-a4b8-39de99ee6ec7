<div class="modal fade" id="sendMessagePop" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content" [formGroup]="noteForm">
            <div class="modal-header py-2">
                <h5 class="modal-title" id="exampleModalCenterTitle">Send Message</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <div class="row align-items-center">
                    <div class="col align-self-center">
                        <div class="form-group row">
                            <label for="ddlFacility" class="col-sm-4 col-form-label">Facility</label>
                            <div class="col-sm-8">
                                <select id="ddlFacility" (change)="facilityChange($any($event.target).value)"
                                    class="form-control" formControlName="ddlFacility"
                                    [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}"
                                    aria-describedby="facilityHelpInline">
                                    <option value="">---Select Facility---</option>
                                    <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                                        <span>{{s.facilityName}}</span>
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div *ngIf="submitted && f['ddlFacility'].errors && f['ddlFacility'].errors?.['required']"
                            class="form-group row justify-content-end">
                            <div class="col-sm-8 text-danger">
                                Facility is required
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row align-items-center">
                    <div class="col align-self-center">
                        <div class="form-group row">
                            <label for="ddlUserGroupTD" class="col-sm-4 col-form-label">Users And Groups</label>
                            <div class="col-sm-8">
                                <div id="ddlUserGroupTD"></div>
                            </div>
                        </div>
                        <div *ngIf="ddlVali" class="form-group row justify-content-end">
                            <div class="col-sm-8 text-danger">
                                Group is required
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row align-items-center">
                    <div class="col align-self-center">
                        <div class="form-group row">
                            <label for="txtMessage" class="col-sm-4 col-form-label">Message</label>
                            <div class="col-sm-8">
                                <textarea id="txtMessage" class="form-control" formControlName="txtMessage"
                                    [ngClass]="{ 'is-invalid': submitted && f['txtMessage'].errors}"> </textarea>
                            </div>
                        </div>
                        <div *ngIf="submitted && f['txtMessage'].errors && f['txtMessage'].errors?.['required']"
                            class="form-group row justify-content-end">
                            <div class="col-sm-8 text-danger">
                                Message is required
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-2 text-right">
                <button type="button" class="btn btn-outline-secondary btn-sm mr-2" data-dismiss="modal">Close</button>
                <button class="btn btn-outline-info btn-sm" type="submit" (click)="sendMessage()">Send</button>
            </div>
        </div>
    </div>
</div>