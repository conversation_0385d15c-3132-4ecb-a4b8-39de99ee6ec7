import { Component, OnInit } from '@angular/core';
import { CommonService } from 'src/app/services/common/common.service';
import { ReportsService } from 'src/app/services/reports/reports.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { AppComponent } from 'src/app/app.component';
import { ExcelServices } from 'src/app/services/common/ExcelService';
declare let $: any;

@Component({
  selector: 'app-missing-encounter-report',
  templateUrl: './missing-encounter-report.component.html',
  styles: []
})
export class MissingEncounterReportComponent implements OnInit {
  public listOfFacilities: Array<any> = [];
  public listOfPhysicians: Array<any> = [];
  public listOfReports: Array<any> = [];
  public listOfReportsInitial: Array<any> = [];
  public FilterForm: FormGroup;
  public request: any = {};
  public submitted: boolean = false;
  public p: number;
  public searchByName: string;
  orderBy = 'desc';
  orderByAccountNumber = 'desc';
  orderByPatientName = 'desc';
  orderByFacilityName = 'desc';
  orderByMissedEncounterSeenDate = 'desc';
  orderByAdmissionDate = 'desc';
  orderByReportedBy = 'desc';
  orderByReportedDate = 'desc';
  orderByPhysicianName = 'desc';
  orderByPhysicianSubmitted = 'desc';
  orderByPhysicianSubmittedDate = 'desc';
  orderByPatientMRNDOB = 'desc';
  sortColumnBy = 'reportedDate';
  public timeout: any = null;
  public img1: string = '.././../../assets/img/excel.png';

  constructor(private readonly reportsServ: ReportsService, private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService,
    private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly excelService: ExcelServices) { }

  ngOnInit() {
    this.appComp.loadPageName('Missing Encounter Report', 'reportsTab');
    this.getFacilities();

    this.FilterForm = this.fb.group({
      ddlFacility: ['', Validators.required],
      ddlPhysician: ['', Validators.required]
    });
  }

  get f() { return this.FilterForm.controls; }

  getFacilities() {
    this.commonServ.getFacilities().subscribe((p: any) => {
      this.listOfFacilities = p;
    }, error => { console.error(error.status); });
  }

  getPhysiciansByFacility(facillity) {
    this.commonServ.startLoading();
    this.commonServ.getPhysiciansByFacility(facillity).subscribe((p: any) => {
      this.commonServ.stopLoading();
      this.listOfPhysicians = p;
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  exportAsXLSX(): void {
    let fileName = "";
    fileName = this.FilterForm.value.ddlFacility + '_' + $('#ddlPhysician option:selected').text();
    this.excelService.exportAsExcelFile(this.listOfReports, fileName);
  }

  filterReport() {
    this.submitted = true;
    if (this.FilterForm.invalid) {
      return;
    }
    this.commonServ.startLoading();
    this.request.FacilityName = this.encrDecr.set(this.FilterForm.value.ddlFacility);
    this.request.PhysicianMailid = this.encrDecr.set(this.FilterForm.value.ddlPhysician);
    this.reportsServ.getMissingEncounterReportData(this.request).subscribe((p: any) => {
      this.listOfReports = p;
      this.listOfReportsInitial = p;
      this.request = {};
      this.commonServ.stopLoading();
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  updateOrderByAccountNumber(){
    this.orderByAccountNumber = this.orderByAccountNumber == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByPatientName(){
    this.orderByPatientName = this.orderByPatientName == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByFacilityName(){
    this.orderByFacilityName = this.orderByFacilityName == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByMissedEncounterSeenDate(){
    this.orderByMissedEncounterSeenDate = this.orderByMissedEncounterSeenDate == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByAdmissionDate(){
    this.orderByAdmissionDate = this.orderByAdmissionDate == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByReportedBy(){
    this.orderByReportedBy = this.orderByReportedBy == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByReportedDate(){
    this.orderByReportedDate = this.orderByReportedDate == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByPhysicianName(){
    this.orderByPhysicianName = this.orderByPhysicianName == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByPhysicianSubmitted(){
    this.orderByPhysicianSubmitted = this.orderByPhysicianSubmitted == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByPhysicianSubmittedDate(){
    this.orderByPhysicianSubmittedDate = this.orderByPhysicianSubmittedDate == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByPatientMRNDOB(){
    this.orderByPatientMRNDOB = this.orderByPatientMRNDOB == 'asc' ? 'desc' : 'asc';
  }

  sortColumn(columnBy) {
    this.sortColumnBy = columnBy;
    switch (columnBy) {
      case ('accountNumber'): {
        this.updateOrderByAccountNumber();
        this.orderBy = this.orderByAccountNumber;
        break;
      }
      case ('patientName'): {
        this.updateOrderByPatientName();
        this.orderBy = this.orderByPatientName;
        break;
      }
      case ('facilityName'): {
        this.updateOrderByFacilityName();
        this.orderBy = this.orderByFacilityName;
        break;
      }
      case ('missedEncounterSeenDate'): {
        this.updateOrderByMissedEncounterSeenDate();
        this.orderBy = this.orderByMissedEncounterSeenDate;
        break;
      }
      case ('admissionDate'): {
        this.updateOrderByAdmissionDate();
        this.orderBy = this.orderByAdmissionDate;
        break;
      }
      case ('reportedBy'): {
        this.updateOrderByReportedBy();
        this.orderBy = this.orderByReportedBy;
        break;
      }
      case ('reportedDate'): {
        this.updateOrderByReportedDate();
        this.orderBy = this.orderByReportedDate;
        break;
      }
      case ('physicianName'): {
        this.updateOrderByPhysicianName();
        this.orderBy = this.orderByPhysicianName;
        break;
      }
      case ('physicianSubmitted'): {
        this.updateOrderByPhysicianSubmitted();
        this.orderBy = this.orderByPhysicianSubmitted;
        break;
      }
      case ('physicianSubmittedDate'): {
        this.updateOrderByPhysicianSubmittedDate();
        this.orderBy = this.orderByPhysicianSubmittedDate;
        break;
      }
      case ('dob'): {
        this.updateOrderByPatientMRNDOB();
        this.orderBy = this.orderByPatientMRNDOB;
        break;
      }
      default: {
        break;
      }
    }
    this.listOfReports = this.sortOrderBy(this.listOfReports, columnBy, this.orderBy)
  }

  sortOrderBy(list, sortBy, sortOrder) {
    let newArray = [];
    this.p = 1;
    if (sortOrder == 'asc') {
      newArray = list.sort(
        function (a, b) {
          let sortNest: number = (b[sortBy] > a[sortBy]) ? -1 : 0;
          let sort: number = (a[sortBy] > b[sortBy]) ? 1 : sortNest;
          return sort;
        }
      );
    }
    else if (sortOrder == 'desc') {
      newArray = list.sort(
        function (a, b) {
          let sortNest: number = (b[sortBy] > a[sortBy]) ? 1 : 0;
          let sort: number = (a[sortBy] > b[sortBy]) ? -1 : sortNest;
          return sort;
        });
    }
    return newArray;
  }

  search(searchKey) {
    clearTimeout(this.timeout);

    this.timeout = setTimeout(() => {
      if (this.listOfReports.length)
        this.listOfReports = this.filterByValue(this.listOfReportsInitial, searchKey)
    }, 2000);

  }

  filterByValue(array, string) {
    return array.filter(o => {
      return Object.keys(o).some(k => {
        if (typeof o[k] === 'string')
          return o[k].toLowerCase().includes(string.toLowerCase());
      });
    });
  }

}
