import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { UserManagementService } from 'src/app/services/user-management/userManagement.service';
declare let $;
@Component({
  selector: 'app-add-modify-user',
  templateUrl: './add-modify-user.component.html',
  styles: []
})
export class AddModifyUserComponent implements OnInit {
  public listOfUsers: Array<any> = [];
  public p: number;
  public search = '';
  public addUserObject = {
    user_id: -1, display_name: '', email_id: '', title_id: '0', firstname: '', middleName: '', lastname: '', facilitynam: '', specialty: '', specialty2: '',
    accountstat: '', npi: '', billerModuleAccess: '', physicianModuleAccess: '', auditorModuleAccess: '', adminModuleAccess: '',
    coordinatorModuleAccess: '', userManagementAccess: '', residentModuleAccess: '', allowPhysicianRounds: '', kareoPhysician: ''
  }
  public adminModuleAccess: string = 'NO';
  public filterObj: any = {};

  constructor(private readonly appComp: AppComponent, private readonly userSer: UserManagementService, private readonly commonServ: CommonService, private readonly toastr: ToastrService) { }

  ngOnInit(): void {
    this.appComp.loadPageName('Add/Modify User', 'usermanagementTab');
    this.adminModuleAccess = this.appComp.userAccess.adminModuleAccess;
    this.fetchActiveUsers();
    if (history.state.filterObj) {
      this.filterObj = history.state.filterObj;
      this.p = this.filterObj.p;
      this.search = this.filterObj.searchKey;
    }
  }

  fetchActiveUsers() {
    this.commonServ.startLoading();
    this.userSer.fetchActiveUsers().subscribe((p: any) => {
      this.listOfUsers = p.fetchActiveUsers;
      this.commonServ.stopLoading();
    }, error => {
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  deassignRoleFacilityOrGroup(email_id, role) {
    if (confirm('Do you want to delete this role?')) {
      this.commonServ.startLoading();
      let request: any = {
        email_id: email_id,
        role: role,
        type: 'ROLE',
        facility_name: '',
        group_name: ''
      }
      this.userSer.deassignRoleFacilityOrGroup(request).subscribe((p: any) => {
        this.toastr.success(role + ' Role Deleted Successfully.', '', { timeOut: 2500 });
        this.fetchActiveUsers();
      }, error => {
        console.error(error.status);
        this.commonServ.stopLoading();
      });
    }
  }
  openCopyProfile() {
    $('#copyProfilepopup').modal('show');
  }

}
