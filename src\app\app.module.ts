import { BrowserModule } from '@angular/platform-browser';
import { NgModule, APP_INITIALIZER } from '@angular/core';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { HTTP_INTERCEPTORS, HttpClientModule, HttpClient } from '@angular/common/http';
import { BillingService } from './services/billing/billing.service';
import { EncrDecrServiceService } from './services/common/encr-decr-service.service';
import { UserIdleModule } from 'angular-user-idle';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ToastrModule } from 'ngx-toastr';
import { tenant, clientId, userIdleTime, redirectUri, userIdlePingTime, userIdleTimeOut } from './config';
import { NoAccessComponent } from './pages/no-access.component';
import { ReportsService } from './services/reports/reports.service';
import { ExcelServices } from './services/common/ExcelService';
import { MessageHubService } from './services/message-hub/message-hub.service';
import { LogoutComponent } from './pages/logout.component';
import { AppConfiguration } from './config/app-configuration';
import { JsonAppConfigService } from './config/json-app-config.service';
import { CustomerrorComponent } from './pages/customerror.component';
import { DatePipe } from '@angular/common';
import { SessionExpirationAlertComponent } from './pages/session-expiration-alert.component';
import { PhysicianService } from './services/physician/physician.service';
import { AuditorService } from './services/Auditor/auditor.service';
import { BrowserCacheLocation, InteractionType, IPublicClientApplication, LogLevel, PublicClientApplication } from '@azure/msal-browser';
import { MsalBroadcastService, MsalGuard, MsalGuardConfiguration, MsalInterceptor, MsalInterceptorConfiguration, MsalModule, MsalService, MSAL_GUARD_CONFIG, MSAL_INSTANCE, MSAL_INTERCEPTOR_CONFIG } from '@azure/msal-angular';
import { HomeComponent } from './general/home/<USER>';
import { NgIdleKeepaliveModule } from '@ng-idle/keepalive';
import { DataService } from './services/common/data.service';
import { AuthInterceptor } from './auth-interceptor.service';

const isIE = window.navigator.userAgent.indexOf("MSIE ") > -1 || window.navigator.userAgent.indexOf("Trident/") > -1;

export function initializerFn(jsonAppConfigService: JsonAppConfigService) {
  return () => {
    return jsonAppConfigService.checkCookie();
  };
}

export function loggerCallback(message: any) {
  console.log(message);
}

export function MSALInstanceFactory(): IPublicClientApplication {
  return new PublicClientApplication({
    auth: {
      clientId,
      authority: 'https://login.microsoftonline.com/' + tenant,
      redirectUri,
      postLogoutRedirectUri: redirectUri
    },
    cache: {
      cacheLocation: BrowserCacheLocation.LocalStorage,
      storeAuthStateInCookie: isIE, // set to true for IE 11
    },
    system: {
      loggerOptions: {
        loggerCallback,
        logLevel: LogLevel.Info,
        piiLoggingEnabled: false
      }
    }
  });
}

export function MSALInterceptorConfigFactory(): MsalInterceptorConfiguration {
  const protectedResourceMap = new Map<string, Array<string>>();
  protectedResourceMap.set('https://graph.microsoft.com/v1.0/me', ['user.read', 'people.read']);
  protectedResourceMap.set('https://graph.microsoft.com/v1.0/me/profile', ['user.read', 'people.read']);

  return {
    interactionType: InteractionType.Redirect,
    protectedResourceMap
  };
}

export function MSALGuardConfigFactory(): MsalGuardConfiguration {
  return { interactionType: InteractionType.Redirect };
}


@NgModule({
  declarations: [
    AppComponent,
    NoAccessComponent,
    SessionExpirationAlertComponent,
    LogoutComponent,
    CustomerrorComponent,
    HomeComponent
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    HttpClientModule,
    MsalModule,
    UserIdleModule.forRoot({ idle: userIdleTime, timeout: userIdleTimeOut, ping: userIdlePingTime }),
    NgIdleKeepaliveModule.forRoot(),
    BrowserAnimationsModule, // required animations module
    ToastrModule.forRoot({
      timeOut: 10000,
      positionClass: 'toast-center-center',
      preventDuplicates: true
    })
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: MsalInterceptor,
      multi: true
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true
    },

    {
      provide: MSAL_INSTANCE,
      useFactory: MSALInstanceFactory
    },
    {
      provide: MSAL_GUARD_CONFIG,
      useFactory: MSALGuardConfigFactory
    },
    {
      provide: MSAL_INTERCEPTOR_CONFIG,
      useFactory: MSALInterceptorConfigFactory
    },
    {
      provide: AppConfiguration,
      deps: [HttpClient],
      useExisting: JsonAppConfigService
    },
    {
      provide: APP_INITIALIZER,
      multi: true,
      deps: [JsonAppConfigService],
      useFactory: initializerFn
    },
    MsalService,
    MsalGuard,
    MsalBroadcastService,
    BillingService,
    EncrDecrServiceService,
    ReportsService,
    ExcelServices,
    MessageHubService,
    DatePipe,
    PhysicianService,
    AuditorService,
    DataService
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }
