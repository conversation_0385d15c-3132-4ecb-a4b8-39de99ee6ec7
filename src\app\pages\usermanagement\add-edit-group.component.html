<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <div class="card-body px-md-3 px-2 py-1">
                    <div class="row mx-0">
                        <div class="col-12 px-0">
                            <div class="card shadow border-blue">
                                <div class="card-header modal-header">
                                    <button class="btn btn-secondary btn-block w-auto"
                                        [routerLink]="['/usermanagement/add-modify-groups']"
                                        [state]="{filterObj:filterObj}">Back</button>
                                </div>
                                <div class="card-body">
                                    <ng-container [formGroup]="addeditgroupForm">
                                        <div class="row mx-auto border align-items-center"
                                            [ngClass]="{'border-bottom-0': groupItem.facilityID !=0}">
                                            <div class="form-group col-12 col-md-2 pl-2 pr-1 py-2 my-auto">
                                                <label class="my-auto" for="ddlRole">Group Name:</label>
                                                <input class="form-control input-border"
                                                    [attr.disabled]="groupItem.groupID !=0 ? true:null" maxlength="125"
                                                    [(ngModel)]="groupItem.groupName"
                                                    [ngClass]="{ 'is-invalid': submitted && f['txtgroupName'].errors}"
                                                    type="text" formControlName="txtgroupName" id="groupName"
                                                    placeholder="Group Name">

                                                <div *ngIf="submitted && f['txtgroupName'].invalid"
                                                    class="alert alert-danger">
                                                    <div
                                                        *ngIf="f['txtgroupName'].errors&&f['txtgroupName'].errors['required']">
                                                        Group Name is required.</div>
                                                </div>
                                            </div>
                                            <div class="form-group col-12 col-md-2 p-1 my-auto">
                                                <label class="my-auto" for="ddlFacility">Facility:</label>
                                                <select class="form-control"
                                                    [attr.disabled]="groupItem.groupID !=0 ? true:null"
                                                    [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}"
                                                    [(ngModel)]="groupItem.facilityID" formControlName="ddlFacility"
                                                    (change)="GetUsersbyfacility($any($event.target).value)"
                                                    id="ddlFacility">
                                                    <option value="0">Choose Facility</option>
                                                    <option [value]="f['facilityID']"
                                                        *ngFor="let f of listOfFacilities">
                                                        <span>{{f['facilityName']}}</span>
                                                    </option>
                                                </select>
                                            </div>
                                            <div class="form-group col-12 col-md-2 p-1 my-auto">
                                                <label class="my-auto" for="ddlFacility">Integration Type:</label>
                                                <select class="form-control" [(ngModel)]="groupItem.integrationType"
                                                    formControlName="ddlIntegrationType" id="ddlIntegrationType">
                                                    <option value="">Select Integration Type</option>
                                                    <option value="Kareo">Kareo</option>
                                                    <option value="ADMD">Advanced MD</option>
                                                    <option value="NG">Next Gen</option>
                                                </select>
                                            </div>
                                            <div class="form-group col-12 col-md-1 p-1 my-auto">
                                                <span class="my-auto">&nbsp;</span>
                                                <div class="form-check">
                                                    <input type="checkbox" id="specilist"
                                                        [(ngModel)]="groupItem.specialty" formControlName="chkspecilist"
                                                        class="form-check-input">
                                                    <label class="form-check-label" for="Specilist"
                                                        class="ml-1">Specilist</label>
                                                </div>

                                            </div>
                                            <div class="form-group col-12 col-md-1 p-1 my-auto">
                                                <span class="my-auto">&nbsp;</span>
                                                <div class="form-check">
                                                    <input type="checkbox" id="IsAlertCheck"
                                                        [(ngModel)]="groupItem.isAlertCheck"
                                                        formControlName="chkIsAlertCheck" class="form-check-input">
                                                    <label class="form-check-label" for="IsIsAlertCheck" class="ml-1">Is
                                                        Alert Check</label>
                                                </div>
                                            </div>
                                            <div class="form-group col-12 col-md-1 pl-1 pr-2 py-2 my-auto"
                                                *ngIf="groupItem.groupID !=0">
                                                <span class="my-auto">&nbsp;</span>
                                                <div class="form-check">
                                                    <input type="checkbox" id="InActive" [(ngModel)]="groupItem.status"
                                                        formControlName="chkInActive" class="form-check-input">
                                                    <label class="form-check-label" for="InActive" class="ml-1">In
                                                        Active</label>
                                                </div>
                                            </div>
                                            <div class="form-group col-12 col-md-2 p-1 my-auto">
                                                <span class="my-auto">&nbsp;</span>
                                                <div class="form-check">
                                                    <input type="checkbox" id="IsCoordinatorAppRqrd"
                                                        [(ngModel)]="groupItem.coordinatorAppRequired"
                                                        formControlName="chkCoordinatorAppRqrd"
                                                        class="form-check-input">
                                                    <label class="form-check-label" for="IsCoordinatorAppRqrd"
                                                        class="ml-1">Is Coordinator Approval</label>
                                                </div>
                                            </div>
                                            <div class="form-group col-12 col-md-2 p-1 my-auto">
                                                <span class="my-auto">&nbsp;</span>
                                                <div class="form-check">
                                                    <input type="checkbox" id="IsAuditorApp"
                                                        [(ngModel)]="groupItem.isAuditRequired"
                                                        formControlName="chkIsAuditorApp" class="form-check-input">
                                                    <label class="form-check-label" for="IsAuditorApp" class="ml-1">Is
                                                        Auditor Approval</label>
                                                </div>
                                            </div>

                                        </div>
                                        <div class="row mx-auto border" *ngIf="groupItem.facilityID !=0">

                                            <div class="form-group col-12 col-md-4 pr-1 pl-2 py-2 mb-auto">
                                                <div class="border bg-light p-1 m-1">
                                                    <h6 class="h6 border p-1 bg-secondary text-white font-weight-bold">
                                                        Physician Role Assignment:</h6>
                                                    <div class="form-group my-auto p-1 m-1">
                                                        <label class="my-auto" for="ddlPhysicians">Mapped
                                                            Physicians:</label>
                                                        <ng-multiselect-dropdown class="edit-group-dropdowns"
                                                            formControlName="ddlPhysicians" [data]="listOfPhysicianList"
                                                            [settings]="mDdlPhysicians"
                                                            [(ngModel)]="listOfselectedPhysicians" id="ddlPhysicians"
                                                            [placeholder]="'--Select Physicians--'">
                                                        </ng-multiselect-dropdown>
                                                    </div>
                                                </div>

                                            </div>
                                            <div class="form-group col-12 col-md-4 p-1 mb-auto">
                                                <div class="border bg-light p-1 m-1">
                                                    <h6 class="h6 border p-1 bg-secondary text-white font-weight-bold">
                                                        Coordinator Role Assignment:</h6>
                                                    <div class="form-group my-auto p-1 m-1">
                                                        <label class="my-auto" for="ddlCoordinator">Mapped
                                                            Coordinators:</label>
                                                        <ng-multiselect-dropdown class="edit-group-dropdowns"
                                                            formControlName="ddlCoordinator"
                                                            [data]="listOfCoordinatorList" [settings]="mDdlCoordinator"
                                                            [(ngModel)]="listOfSelectedCoordinator" id="ddlCoordinator"
                                                            [placeholder]="'--Select Coordinator--'">
                                                        </ng-multiselect-dropdown>
                                                    </div>
                                                </div>

                                            </div>
                                            <div class="form-group col-12 col-md-4 pl-1 pr-2 py-2 mb-auto">
                                                <div class="border bg-light p-1 m-1">
                                                    <h6 class="h6 border p-1 bg-secondary text-white font-weight-bold">
                                                        Auditor Role Assignment:</h6>
                                                    <div class="form-group my-auto p-1 m-1">
                                                        <label class="my-auto" for="ddlAuditor">Mapped Auditors:</label>
                                                        <ng-multiselect-dropdown class="edit-group-dropdowns"
                                                            formControlName="ddlAuditor" [data]="listOfAuditorsList"
                                                            [settings]="mDdlAuditor"
                                                            [(ngModel)]="listOfSelectedAuditors" id="ddlAuditor"
                                                            [placeholder]="'--Select Auditor--'">
                                                        </ng-multiselect-dropdown>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                        <div class="row mx-auto border" *ngIf="groupItem.facilityID !=0"
                                            [ngClass]="{'border-top-0': groupItem.facilityID !=0}">
                                            <div class="form-group col-12 col-md-4 pr-1 pl-2 py-2 mb-auto">
                                                <div class="border bg-light p-1 m-1">
                                                    <h6 class="h6 border p-1 bg-secondary text-white font-weight-bold">
                                                        Biller Role Assignment:</h6>
                                                    <div class="form-group my-auto p-1 m-1">
                                                        <label class="my-auto" for="ddlBiller">Mapped Billers:</label>
                                                        <ng-multiselect-dropdown class="edit-group-dropdowns"
                                                            formControlName="ddlBiller" [data]="listOfBillersList"
                                                            [settings]="mDdlBiller" [(ngModel)]="listOfSelectedBillers"
                                                            id="ddlBiller" [placeholder]="'--Select Biller--'">
                                                        </ng-multiselect-dropdown>
                                                    </div>
                                                </div>

                                            </div>
                                            <div class="form-group col-12 col-md-4 p-1 mb-auto">
                                                <div class="border bg-light p-1 m-1">
                                                    <h6 class="h6 border p-1 bg-secondary text-white font-weight-bold">
                                                        Resident Role Assignment:</h6>
                                                    <div class="form-group my-auto p-1 m-1">
                                                        <label class="my-auto" for="ddlResident">Mapped
                                                            Resident:</label>
                                                        <ng-multiselect-dropdown class="edit-group-dropdowns"
                                                            formControlName="ddlResident" [data]="listOfResudentList"
                                                            [settings]="mDdlResident"
                                                            [(ngModel)]="listOfSelectedResudents" id="ddlResident"
                                                            [placeholder]="'--Select Resident--'">
                                                        </ng-multiselect-dropdown>
                                                    </div>
                                                </div>

                                            </div>

                                            <div class="form-group col-12 col-md-4 pl-1 pr-2 py-2 mb-auto">
                                                <div class="border bg-light p-1 m-1">
                                                    <h6 class="h6 border p-1 bg-secondary text-white font-weight-bold">
                                                        Group Head:</h6>
                                                    <div class="form-group my-auto p-1 m-1">
                                                        <select class="form-control"
                                                            [ngClass]="{ 'is-invalid': submitted && f['ddlGroupHead'].errors}"
                                                            [(ngModel)]="groupItem.groupHead"
                                                            formControlName="ddlGroupHead" id="ddlGroupHead">
                                                            <option value="">Choose GroupHead</option>
                                                            <option [value]="p.userid"
                                                                *ngFor="let p of listofGroupHead">
                                                                <span>{{p.username}}</span>
                                                            </option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </ng-container>
                                </div>
                                <div class="modal-footer" *ngIf="groupItem.facilityID !=0">
                                    <button type="button" class="btn btn-sm btn-outline-secondary"
                                        [routerLink]="['/usermanagement/add-modify-groups']">Cancel</button>
                                    <button type="button" class="btn btn-sm btn-outline-info"
                                        *ngIf="groupItem.groupID ==0" (click)="InsertGroup(groupItem)">Submit</button>
                                    <button type="button" class="btn btn-sm btn-outline-info"
                                        *ngIf="groupItem.groupID !=0" (click)="UpdateGroup(groupItem)">Update</button>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>