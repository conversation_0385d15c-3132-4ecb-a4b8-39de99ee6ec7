<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new p-2" style="background: #0169ab;" [formGroup]="FilterForm">
                    <div class="align-items-center row mx-auto">
                        <div class="col-12 px-0">

                            <div class="row mb-2 mt-1 mx-auto">
                                <div class="col-12 col-md-3 px-1 py-1">
                                    <select id="ddlFacility" class="form-control" formControlName="ddlFacility"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                                        <option value="">---Select Facility---</option>
                                        <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                                            <span>{{s.facilityName}}</span>
                                        </option>
                                    </select>
                                </div>

                                <div class="col-12 col-md-3 px-1 py-1">
                                    <input class="form-control input-border" title="Discharge From Date"
                                        [ngClass]="{ 'is-invalid': submitted && f['txtFromDate'].errors}" type="date"
                                        formControlName="txtFromDate" id="txtFromDate" placeholder="mm/dd/yyyy">
                                </div>

                                <div class="col-12 col-md-4 px-1 py-1">
                                    <input class="form-control input-border" title="Discharge To Date"
                                        [ngClass]="{ 'is-invalid': submitted && f['txtToDate'].errors}" type="date"
                                        formControlName="txtToDate" id="txtToDate" placeholder="mm/dd/yyyy">
                                </div>

                                <div class="col-12 col-md-2 text-right text-md-left px-1 py-1">
                                    <button title="Submit Filter" class="btn btn-outline-info px-2 btn-block"
                                        (click)="getDischargePatientData(1)" type="submit">
                                        <i class="fa-filter fas font-size-13"></i>
                                        Filter
                                    </button>
                                </div>

                            </div>
                            <div class="mt-2 mx-0 row"
                                *ngIf="(submitted && (f['txtFromDate'].errors&&f['txtFromDate'].errors['endDateError']||f['txtToDate'].errors&&f['txtToDate'].errors['endDateError']))">
                                <div class="col-4"></div>
                                <div class="col text-danger">Encounter Seen From date should be less than Encounter Seen
                                    To date.</div>
                            </div>
                            <div class="row mt-2 mb-1 mx-auto">
                                <div class="col-12 col-md-12 px-1 py-1">
                                    <input type="text" class="form-control small" maxlength="1000"
                                        placeholder="Search for Patient Name, Account No, MRN or Room"
                                        aria-label="Search" aria-describedby="basic-addon2"
                                        formControlName="txtSearchKey">
                                    <img alt=' ' class="search position-absolute mr-3"
                                        src="../../../assets/img/search.png" (click)="onKeyPatientSearch()">
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <!-- table Card starts -->
                <div class="card-body px-md-2 py-md-1 px-1 py-0">
                    <app-patients [listOfPatients]="listOfPatients" [p]="p" [pageName]="'/physician/discharge-patients'" [FilterForm]="FilterForm" [totalCount]="totalCount" [searchByName]="''" [device]="device" (enventUpdateSortObj)="updateSortObj($event)"  [orderBy]="orderBy" [sortColumnBy]="sortColumnBy" (eventListOfPatients)="getDischargePatientData($event)"></app-patients>
                </div>
                <!-- table card ends-->

            </div>
        </div>

    </div>
</div>